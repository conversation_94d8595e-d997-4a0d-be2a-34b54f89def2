import React, { ReactNode } from "react";
import { LaunchDarklyConfig, LaunchDarklyContextValue } from "./types";
/**
 * Props for the LaunchDarklyProvider component
 */
interface LaunchDarklyProviderProps {
    children: ReactNode;
    config: LaunchDarklyConfig;
    fallback?: ReactNode;
}
/**
 * Main LaunchDarklyProvider component that wraps the application
 * with Launch Darkly functionality using the modern context API
 */
export declare const LaunchDarklyProvider: React.FC<LaunchDarklyProviderProps>;
/**
 * Hook to access the Launch Darkly context
 * @returns LaunchDarklyContextValue
 * @throws Error if used outside of LaunchDarklyProvider
 */
export declare const useLaunchDarklyContext: () => LaunchDarklyContextValue;
export {};
