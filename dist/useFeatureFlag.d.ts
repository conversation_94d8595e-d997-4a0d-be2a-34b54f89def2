import { LDFlagValue } from 'launchdarkly-react-client-sdk';
import { FeatureFlagConfig, FeatureFlagResult } from './types';
/**
 * Custom hook for retrieving feature flag values with memoization and error handling
 *
 * @param config - Configuration object containing flag key, default value, and optional context
 * @returns FeatureFlagResult with current value, loading state, error, and utility functions
 *
 * @example
 * ```tsx
 * const { value, loading, error, isAvailable } = useFeatureFlag({
 *   flagKey: 'my-feature-flag',
 *   defaultValue: false
 * });
 *
 * if (loading) return <div>Loading...</div>;
 * if (error) return <div>Error: {error.message}</div>;
 *
 * return <div>Feature enabled: {value}</div>;
 * ```
 */
export declare function useFeatureFlag<T extends LDFlagValue = LDFlagValue>(config: FeatureFlagConfig<T>): FeatureFlagResult<T>;
/**
 * Simplified hook for boolean feature flags
 *
 * @param flagKey - The feature flag key
 * @param defaultValue - Default boolean value (default: false)
 * @returns FeatureFlagResult<boolean>
 */
export declare function useBooleanFeatureFlag(flagKey: string, defaultValue?: boolean): FeatureFlagResult<boolean>;
/**
 * Simplified hook for string feature flags
 *
 * @param flagKey - The feature flag key
 * @param defaultValue - Default string value (default: '')
 * @returns FeatureFlagResult<string>
 */
export declare function useStringFeatureFlag(flagKey: string, defaultValue?: string): FeatureFlagResult<string>;
/**
 * Simplified hook for number feature flags
 *
 * @param flagKey - The feature flag key
 * @param defaultValue - Default number value (default: 0)
 * @returns FeatureFlagResult<number>
 */
export declare function useNumberFeatureFlag(flagKey: string, defaultValue?: number): FeatureFlagResult<number>;
