'use strict';

var jsxRuntime = require('react/jsx-runtime');
var e$1 = require('react');

function _interopNamespaceDefault(e) {
    var n = Object.create(null);
    if (e) {
        Object.keys(e).forEach(function (k) {
            if (k !== 'default') {
                var d = Object.getOwnPropertyDescriptor(e, k);
                Object.defineProperty(n, k, d.get ? d : {
                    enumerable: true,
                    get: function () { return e[k]; }
                });
            }
        });
    }
    n.default = e;
    return Object.freeze(n);
}

var e__namespace = /*#__PURE__*/_interopNamespaceDefault(e$1);

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
/* global Reflect, Promise, SuppressedError, Symbol, Iterator */

var extendStatics = function(d, b) {
    extendStatics = Object.setPrototypeOf ||
        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
    return extendStatics(d, b);
};

function __extends(d, b) {
    if (typeof b !== "function" && b !== null)
        throw new TypeError("Class extends value " + String(b) + " is not a constructor or null");
    extendStatics(d, b);
    function __() { this.constructor = d; }
    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
}

var __assign = function() {
    __assign = Object.assign || function __assign(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

function __generator(thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
}

typeof SuppressedError === "function" ? SuppressedError : function (error, suppressed, message) {
    var e = new Error(message);
    return e.name = "SuppressedError", e.error = error, e.suppressed = suppressed, e;
};

function e(e){function t(e,t){Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.message=e,this.code=t;}return t.prototype=new Error,t.prototype.name=e,t.prototype.constructor=t,t}const t=e("LaunchDarklyUnexpectedResponseError"),n=e("LaunchDarklyInvalidEnvironmentIdError"),r=e("LaunchDarklyInvalidUserError"),o=e("LaunchDarklyInvalidEventKeyError"),i=e("LaunchDarklyInvalidArgumentError"),a=e("LaunchDarklyFlagFetchError");for(var s={LDUnexpectedResponseError:t,LDInvalidEnvironmentIdError:n,LDInvalidUserError:r,LDInvalidEventKeyError:o,LDInvalidArgumentError:i,LDInvalidDataError:e("LaunchDarklyInvalidDataError"),LDFlagFetchError:a,LDTimeoutError:e("LaunchDarklyTimeoutError"),isHttpErrorRecoverable:function(e){return !(e>=400&&e<500)||(400===e||408===e||429===e)}},c$1=function(e){var t=m$1(e),n=t[0],r=t[1];return 3*(n+r)/4-r},u$1=function(e){var t,n,r=m$1(e),o=r[0],i=r[1],a=new g$1(function(e,t,n){return 3*(t+n)/4-n}(0,o,i)),s=0,c=i>0?o-4:o;for(n=0;n<c;n+=4)t=f[e.charCodeAt(n)]<<18|f[e.charCodeAt(n+1)]<<12|f[e.charCodeAt(n+2)]<<6|f[e.charCodeAt(n+3)],a[s++]=t>>16&255,a[s++]=t>>8&255,a[s++]=255&t;2===i&&(t=f[e.charCodeAt(n)]<<2|f[e.charCodeAt(n+1)]>>4,a[s++]=255&t);1===i&&(t=f[e.charCodeAt(n)]<<10|f[e.charCodeAt(n+1)]<<4|f[e.charCodeAt(n+2)]>>2,a[s++]=t>>8&255,a[s++]=255&t);return a},l$1=function(e){for(var t,n=e.length,r=n%3,o=[],i=16383,a=0,s=n-r;a<s;a+=i)o.push(h$1(e,a,a+i>s?s:a+i));1===r?(t=e[n-1],o.push(d[t>>2]+d[t<<4&63]+"==")):2===r&&(t=(e[n-2]<<8)+e[n-1],o.push(d[t>>10]+d[t>>4&63]+d[t<<2&63]+"="));return o.join("")},d=[],f=[],g$1="undefined"!=typeof Uint8Array?Uint8Array:Array,v$1="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",p$1=0;p$1<64;++p$1)d[p$1]=v$1[p$1],f[v$1.charCodeAt(p$1)]=p$1;function m$1(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return -1===n&&(n=t),[n,n===t?0:4-n%4]}function h$1(e,t,n){for(var r,o,i=[],a=t;a<n;a+=3)r=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),i.push(d[(o=r)>>18&63]+d[o>>12&63]+d[o>>6&63]+d[63&o]);return i.join("")}f["-".charCodeAt(0)]=62,f["_".charCodeAt(0)]=63;var y$1={byteLength:c$1,toByteArray:u$1,fromByteArray:l$1},w$1=Array.isArray,b$1=Object.keys,k$1=Object.prototype.hasOwnProperty,E$1=function e(t,n){if(t===n)return !0;if(t&&n&&"object"==typeof t&&"object"==typeof n){var r,o,i,a=w$1(t),s=w$1(n);if(a&&s){if((o=t.length)!=n.length)return !1;for(r=o;0!==r--;)if(!e(t[r],n[r]))return !1;return !0}if(a!=s)return !1;var c=t instanceof Date,u=n instanceof Date;if(c!=u)return !1;if(c&&u)return t.getTime()==n.getTime();var l=t instanceof RegExp,d=n instanceof RegExp;if(l!=d)return !1;if(l&&d)return t.toString()==n.toString();var f=b$1(t);if((o=f.length)!==b$1(n).length)return !1;for(r=o;0!==r--;)if(!k$1.call(n,f[r]))return !1;for(r=o;0!==r--;)if(!e(t[i=f[r]],n[i]))return !1;return !0}return t!=t&&n!=n};const D$1=["key","ip","country","email","firstName","lastName","avatar","name"];function x$1(e){const t=unescape(encodeURIComponent(e));return y$1.fromByteArray(function(e){const t=[];for(let n=0;n<e.length;n++)t.push(e.charCodeAt(n));return t}(t))}function C$1(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var P$1,S$1={appendUrlPath:function(e,t){return (e.endsWith("/")?e.substring(0,e.length-1):e)+(t.startsWith("/")?"":"/")+t},base64URLEncode:function(e){return x$1(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},btoa:x$1,clone:function(e){return JSON.parse(JSON.stringify(e))},deepEquals:function(e,t){return E$1(e,t)},extend:function(...e){return e.reduce(((e,t)=>({...e,...t})),{})},getLDUserAgentString:function(e){const t=e.version||"?";return e.userAgent+"/"+t},objectHasOwnProperty:C$1,onNextTick:function(e){setTimeout(e,0);},sanitizeContext:function(e){if(!e)return e;let t;return null!==e.kind&&void 0!==e.kind||D$1.forEach((n=>{const r=e[n];void 0!==r&&"string"!=typeof r&&(t=t||{...e},t[n]=String(r));})),t||e},transformValuesToVersionedValues:function(e){const t={};for(const n in e)C$1(e,n)&&(t[n]={value:e[n],version:0});return t},transformVersionedValuesToValues:function(e){const t={};for(const n in e)C$1(e,n)&&(t[n]=e[n].value);return t},wrapPromiseCallback:function(e,t){const n=e.then((e=>(t&&setTimeout((()=>{t(null,e);}),0),e)),(e=>{if(!t)return Promise.reject(e);setTimeout((()=>{t(e,null);}),0);}));return t?void 0:n},once:function(e){let t,n=!1;return function(...r){return n||(n=!0,t=e.apply(this,r)),t}}},I$1=new Uint8Array(16);function O$1(){if(!P$1&&!(P$1="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return P$1(I$1)}var T$1=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function L$1(e){return "string"==typeof e&&T$1.test(e)}for(var U$1,A,j$1=[],R$1=0;R$1<256;++R$1)j$1.push((R$1+256).toString(16).substr(1));function F$1(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(j$1[e[t+0]]+j$1[e[t+1]]+j$1[e[t+2]]+j$1[e[t+3]]+"-"+j$1[e[t+4]]+j$1[e[t+5]]+"-"+j$1[e[t+6]]+j$1[e[t+7]]+"-"+j$1[e[t+8]]+j$1[e[t+9]]+"-"+j$1[e[t+10]]+j$1[e[t+11]]+j$1[e[t+12]]+j$1[e[t+13]]+j$1[e[t+14]]+j$1[e[t+15]]).toLowerCase();if(!L$1(n))throw TypeError("Stringified UUID is invalid");return n}var N$1=0,$$1=0;function V$1(e){if(!L$1(e))throw TypeError("Invalid UUID");var t,n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n}function H(e,t,n){function r(e,r,o,i){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}(e)),"string"==typeof r&&(r=V$1(r)),16!==r.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var a=new Uint8Array(16+e.length);if(a.set(r),a.set(e,r.length),(a=n(a))[6]=15&a[6]|t,a[8]=63&a[8]|128,o){i=i||0;for(var s=0;s<16;++s)o[i+s]=a[s];return o}return F$1(a)}try{r.name=e;}catch(e){}return r.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",r.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",r}function M$1(e){return 14+(e+64>>>9<<4)+1}function q$1(e,t){var n=(65535&e)+(65535&t);return (e>>16)+(t>>16)+(n>>16)<<16|65535&n}function z$1(e,t,n,r,o,i){return q$1((a=q$1(q$1(t,e),q$1(r,i)))<<(s=o)|a>>>32-s,n);var a,s;}function K$1(e,t,n,r,o,i,a){return z$1(t&n|~t&r,e,t,o,i,a)}function _(e,t,n,r,o,i,a){return z$1(t&r|n&~r,e,t,o,i,a)}function J(e,t,n,r,o,i,a){return z$1(t^n^r,e,t,o,i,a)}function B(e,t,n,r,o,i,a){return z$1(n^(t|~r),e,t,o,i,a)}var G=H("v3",48,(function(e){if("string"==typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var n=0;n<t.length;++n)e[n]=t.charCodeAt(n);}return function(e){for(var t=[],n=32*e.length,r="0123456789abcdef",o=0;o<n;o+=8){var i=e[o>>5]>>>o%32&255,a=parseInt(r.charAt(i>>>4&15)+r.charAt(15&i),16);t.push(a);}return t}(function(e,t){e[t>>5]|=128<<t%32,e[M$1(t)-1]=t;for(var n=1732584193,r=-271733879,o=-1732584194,i=271733878,a=0;a<e.length;a+=16){var s=n,c=r,u=o,l=i;n=K$1(n,r,o,i,e[a],7,-680876936),i=K$1(i,n,r,o,e[a+1],12,-389564586),o=K$1(o,i,n,r,e[a+2],17,606105819),r=K$1(r,o,i,n,e[a+3],22,-1044525330),n=K$1(n,r,o,i,e[a+4],7,-176418897),i=K$1(i,n,r,o,e[a+5],12,1200080426),o=K$1(o,i,n,r,e[a+6],17,-1473231341),r=K$1(r,o,i,n,e[a+7],22,-45705983),n=K$1(n,r,o,i,e[a+8],7,1770035416),i=K$1(i,n,r,o,e[a+9],12,-1958414417),o=K$1(o,i,n,r,e[a+10],17,-42063),r=K$1(r,o,i,n,e[a+11],22,-1990404162),n=K$1(n,r,o,i,e[a+12],7,1804603682),i=K$1(i,n,r,o,e[a+13],12,-40341101),o=K$1(o,i,n,r,e[a+14],17,-1502002290),n=_(n,r=K$1(r,o,i,n,e[a+15],22,1236535329),o,i,e[a+1],5,-165796510),i=_(i,n,r,o,e[a+6],9,-1069501632),o=_(o,i,n,r,e[a+11],14,643717713),r=_(r,o,i,n,e[a],20,-373897302),n=_(n,r,o,i,e[a+5],5,-701558691),i=_(i,n,r,o,e[a+10],9,38016083),o=_(o,i,n,r,e[a+15],14,-660478335),r=_(r,o,i,n,e[a+4],20,-405537848),n=_(n,r,o,i,e[a+9],5,568446438),i=_(i,n,r,o,e[a+14],9,-1019803690),o=_(o,i,n,r,e[a+3],14,-187363961),r=_(r,o,i,n,e[a+8],20,1163531501),n=_(n,r,o,i,e[a+13],5,-1444681467),i=_(i,n,r,o,e[a+2],9,-51403784),o=_(o,i,n,r,e[a+7],14,1735328473),n=J(n,r=_(r,o,i,n,e[a+12],20,-1926607734),o,i,e[a+5],4,-378558),i=J(i,n,r,o,e[a+8],11,-2022574463),o=J(o,i,n,r,e[a+11],16,1839030562),r=J(r,o,i,n,e[a+14],23,-35309556),n=J(n,r,o,i,e[a+1],4,-1530992060),i=J(i,n,r,o,e[a+4],11,1272893353),o=J(o,i,n,r,e[a+7],16,-155497632),r=J(r,o,i,n,e[a+10],23,-1094730640),n=J(n,r,o,i,e[a+13],4,681279174),i=J(i,n,r,o,e[a],11,-358537222),o=J(o,i,n,r,e[a+3],16,-722521979),r=J(r,o,i,n,e[a+6],23,76029189),n=J(n,r,o,i,e[a+9],4,-640364487),i=J(i,n,r,o,e[a+12],11,-421815835),o=J(o,i,n,r,e[a+15],16,530742520),n=B(n,r=J(r,o,i,n,e[a+2],23,-995338651),o,i,e[a],6,-198630844),i=B(i,n,r,o,e[a+7],10,1126891415),o=B(o,i,n,r,e[a+14],15,-1416354905),r=B(r,o,i,n,e[a+5],21,-57434055),n=B(n,r,o,i,e[a+12],6,1700485571),i=B(i,n,r,o,e[a+3],10,-1894986606),o=B(o,i,n,r,e[a+10],15,-1051523),r=B(r,o,i,n,e[a+1],21,-2054922799),n=B(n,r,o,i,e[a+8],6,1873313359),i=B(i,n,r,o,e[a+15],10,-30611744),o=B(o,i,n,r,e[a+6],15,-1560198380),r=B(r,o,i,n,e[a+13],21,1309151649),n=B(n,r,o,i,e[a+4],6,-145523070),i=B(i,n,r,o,e[a+11],10,-1120210379),o=B(o,i,n,r,e[a+2],15,718787259),r=B(r,o,i,n,e[a+9],21,-343485551),n=q$1(n,s),r=q$1(r,c),o=q$1(o,u),i=q$1(i,l);}return [n,r,o,i]}(function(e){if(0===e.length)return [];for(var t=8*e.length,n=new Uint32Array(M$1(t)),r=0;r<t;r+=8)n[r>>5]|=(255&e[r/8])<<r%32;return n}(e),8*e.length))})),W=G;function X(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:case 3:return t^n^r;case 2:return t&n^t&r^n&r}}function Q(e,t){return e<<t|e>>>32-t}var Y=H("v5",80,(function(e){var t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof e){var r=unescape(encodeURIComponent(e));e=[];for(var o=0;o<r.length;++o)e.push(r.charCodeAt(o));}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var i=e.length/4+2,a=Math.ceil(i/16),s=new Array(a),c=0;c<a;++c){for(var u=new Uint32Array(16),l=0;l<16;++l)u[l]=e[64*c+4*l]<<24|e[64*c+4*l+1]<<16|e[64*c+4*l+2]<<8|e[64*c+4*l+3];s[c]=u;}s[a-1][14]=8*(e.length-1)/Math.pow(2,32),s[a-1][14]=Math.floor(s[a-1][14]),s[a-1][15]=8*(e.length-1)&4294967295;for(var d=0;d<a;++d){for(var f=new Uint32Array(80),g=0;g<16;++g)f[g]=s[d][g];for(var v=16;v<80;++v)f[v]=Q(f[v-3]^f[v-8]^f[v-14]^f[v-16],1);for(var p=n[0],m=n[1],h=n[2],y=n[3],w=n[4],b=0;b<80;++b){var k=Math.floor(b/20),E=Q(p,5)+X(k,m,h,y)+w+t[k]+f[b]>>>0;w=y,y=h,h=Q(m,30)>>>0,m=p,p=E;}n[0]=n[0]+p>>>0,n[1]=n[1]+m>>>0,n[2]=n[2]+h>>>0,n[3]=n[3]+y>>>0,n[4]=n[4]+w>>>0;}return [n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]})),Z=Y;var ee=Object.freeze({__proto__:null,v1:function(e,t,n){var r=t&&n||0,o=t||new Array(16),i=(e=e||{}).node||U$1,a=void 0!==e.clockseq?e.clockseq:A;if(null==i||null==a){var s=e.random||(e.rng||O$1)();null==i&&(i=U$1=[1|s[0],s[1],s[2],s[3],s[4],s[5]]),null==a&&(a=A=16383&(s[6]<<8|s[7]));}var c=void 0!==e.msecs?e.msecs:Date.now(),u=void 0!==e.nsecs?e.nsecs:$$1+1,l=c-N$1+(u-$$1)/1e4;if(l<0&&void 0===e.clockseq&&(a=a+1&16383),(l<0||c>N$1)&&void 0===e.nsecs&&(u=0),u>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");N$1=c,$$1=u,A=a;var d=(1e4*(268435455&(c+=122192928e5))+u)%4294967296;o[r++]=d>>>24&255,o[r++]=d>>>16&255,o[r++]=d>>>8&255,o[r++]=255&d;var f=c/4294967296*1e4&268435455;o[r++]=f>>>8&255,o[r++]=255&f,o[r++]=f>>>24&15|16,o[r++]=f>>>16&255,o[r++]=a>>>8|128,o[r++]=255&a;for(var g=0;g<6;++g)o[r+g]=i[g];return t||F$1(o)},v3:W,v4:function(e,t,n){var r=(e=e||{}).random||(e.rng||O$1)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var o=0;o<16;++o)t[n+o]=r[o];return t}return F$1(r)},v5:Z,NIL:"00000000-0000-0000-0000-000000000000",version:function(e){if(!L$1(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)},validate:L$1,stringify:F$1,parse:V$1});const te=["debug","info","warn","error","none"];var ne={commonBasicLogger:function(e,t){if(e&&e.destination&&"function"!=typeof e.destination)throw new Error("destination for basicLogger was set to a non-function");function n(e){return function(t){console&&console[e]&&console[e].call(console,t);}}const r=e&&e.destination?[e.destination,e.destination,e.destination,e.destination]:[n("log"),n("info"),n("warn"),n("error")],o=!(!e||!e.destination),i=e&&void 0!==e.prefix&&null!==e.prefix?e.prefix:"[LaunchDarkly] ";let a=1;if(e&&e.level)for(let t=0;t<te.length;t++)te[t]===e.level&&(a=t);function s(e,n,a){if(a.length<1)return;let s;const c=o?n+": "+i:i;if(1!==a.length&&t){const e=[...a];e[0]=c+e[0],s=t(...e);}else s=c+a[0];try{r[e](s);}catch(e){console&&console.log&&console.log("[LaunchDarkly] Configured logger's "+n+" method threw an exception: "+e);}}const c={};for(let e=0;e<te.length;e++){const t=te[e];if("none"!==t)if(e<a)c[t]=()=>{};else {const n=e;c[t]=function(){s(n,t,arguments);};}}return c},validateLogger:function(e){te.forEach((t=>{if("none"!==t&&(!e[t]||"function"!=typeof e[t]))throw new Error("Provided logger instance must support logger."+t+"(...) method")}));}};function re(e){return e&&e.message?e.message:"string"==typeof e||e instanceof String?e:JSON.stringify(e)}const oe=" Please see https://docs.launchdarkly.com/sdk/client-side/javascript#initialize-the-client for instructions on SDK initialization.";var ie$1={bootstrapInvalid:function(){return "LaunchDarkly bootstrap data is not available because the back end could not read the flags."},bootstrapOldFormat:function(){return "LaunchDarkly client was initialized with bootstrap data that did not include flag metadata. Events may not be sent correctly."+oe},clientInitialized:function(){return "LaunchDarkly client initialized"},clientNotReady:function(){return "LaunchDarkly client is not ready"},debugEnqueueingEvent:function(e){return 'enqueueing "'+e+'" event'},debugPostingDiagnosticEvent:function(e){return "sending diagnostic event ("+e.kind+")"},debugPostingEvents:function(e){return "sending "+e+" events"},debugStreamDelete:function(e){return 'received streaming deletion for flag "'+e+'"'},debugStreamDeleteIgnored:function(e){return 'received streaming deletion for flag "'+e+'" but ignored due to version check'},debugStreamPatch:function(e){return 'received streaming update for flag "'+e+'"'},debugStreamPatchIgnored:function(e){return 'received streaming update for flag "'+e+'" but ignored due to version check'},debugStreamPing:function(){return "received ping message from stream"},debugPolling:function(e){return "polling for feature flags at "+e},debugStreamPut:function(){return "received streaming update for all flags"},deprecated:function(e,t){return t?'"'+e+'" is deprecated, please use "'+t+'"':'"'+e+'" is deprecated'},environmentNotFound:function(){return "Environment not found. Double check that you specified a valid environment/client-side ID."+oe},environmentNotSpecified:function(){return "No environment/client-side ID was specified."+oe},errorFetchingFlags:function(e){return "Error fetching flag settings: "+re(e)},eventCapacityExceeded:function(){return "Exceeded event queue capacity. Increase capacity to avoid dropping events."},eventWithoutContext:function(){return "Be sure to call `identify` in the LaunchDarkly client: https://docs.launchdarkly.com/sdk/features/identify#javascript"},httpErrorMessage:function(e,t,n){return "Received error "+e+(401===e?" (invalid SDK key)":"")+" for "+t+" - "+(s.isHttpErrorRecoverable(e)?n:"giving up permanently")},httpUnavailable:function(){return "Cannot make HTTP requests in this environment."+oe},identifyDisabled:function(){return "identify() has no effect here; it must be called on the main client instance"},inspectorMethodError:(e,t)=>`an inspector: "${t}" of type: "${e}" generated an exception`,invalidContentType:function(e){return 'Expected application/json content type but got "'+e+'"'},invalidData:function(){return "Invalid data received from LaunchDarkly; connection may have been interrupted"},invalidInspector:(e,t)=>`an inspector: "${t}" of an invalid type (${e}) was configured`,invalidKey:function(){return "Event key must be a string"},invalidMetricValue:e=>`The track function was called with a non-numeric "metricValue" (${e}), only numeric metric values are supported.`,invalidContext:function(){return "Invalid context specified."+oe},invalidTagValue:e=>`Config option "${e}" must only contain letters, numbers, ., _ or -.`,localStorageUnavailable:function(e){return "local storage is unavailable: "+re(e)},networkError:e=>"network error"+(e?" ("+e+")":""),optionBelowMinimum:(e,t,n)=>'Config option "'+e+'" was set to '+t+", changing to minimum value of "+n,streamClosing:function(){return "Closing stream connection"},streamConnecting:function(e){return "Opening stream connection to "+e},streamError:function(e,t){return "Error on stream connection: "+re(e)+", will continue retrying after "+t+" milliseconds."},tagValueTooLong:e=>`Value of "${e}" was longer than 64 characters and was discarded.`,unknownCustomEventKey:function(e){return 'Custom event "'+e+'" does not exist'},unknownOption:e=>'Ignoring unknown config option "'+e+'"',contextNotSpecified:function(){return "No context specified."+oe},unrecoverableStreamError:e=>`Error on stream connection ${re(e)}, giving up permanently`,wrongOptionType:(e,t,n)=>'Config option "'+e+'" should be of type '+t+", got "+n+", using default value",wrongOptionTypeBoolean:(e,t)=>'Config option "'+e+'" should be a boolean, got '+t+", converting to boolean"};const{validateLogger:ae}=ne,se$1={baseUrl:{default:"https://app.launchdarkly.com"},streamUrl:{default:"https://clientstream.launchdarkly.com"},eventsUrl:{default:"https://events.launchdarkly.com"},sendEvents:{default:!0},streaming:{type:"boolean"},sendLDHeaders:{default:!0},requestHeaderTransform:{type:"function"},sendEventsOnlyForVariation:{default:!1},useReport:{default:!1},evaluationReasons:{default:!1},eventCapacity:{default:100,minimum:1},flushInterval:{default:2e3,minimum:2e3},samplingInterval:{default:0,minimum:0},streamReconnectDelay:{default:1e3,minimum:0},allAttributesPrivate:{default:!1},privateAttributes:{default:[]},bootstrap:{type:"string|object"},diagnosticRecordingInterval:{default:9e5,minimum:2e3},diagnosticOptOut:{default:!1},wrapperName:{type:"string"},wrapperVersion:{type:"string"},stateProvider:{type:"object"},application:{validator:function(e,t,n){const r={};t.id&&(r.id=le(`${e}.id`,t.id,n));t.version&&(r.version=le(`${e}.version`,t.version,n));return r}},inspectors:{default:[]},hooks:{default:[]},plugins:{default:[]}},ce=/^(\w|\.|-)+$/;function ue(e){return e&&e.replace(/\/+$/,"")}function le(e,t,n){if("string"==typeof t&&t.match(ce)){if(!(t.length>64))return t;n.warn(ie$1.tagValueTooLong(e));}else n.warn(ie$1.invalidTagValue(e));}var de={baseOptionDefs:se$1,validate:function(e,t,n,r){const o=S$1.extend({logger:{default:r}},se$1,n),i={};function a(e){S$1.onNextTick((()=>{t&&t.maybeReportError(new s.LDInvalidArgumentError(e));}));}let c=S$1.extend({},e||{});return function(e){const t=e;Object.keys(i).forEach((e=>{if(void 0!==t[e]){const n=i[e];r&&r.warn(ie$1.deprecated(e,n)),n&&(void 0===t[n]&&(t[n]=t[e]),delete t[e]);}}));}(c),c=function(e){const t=S$1.extend({},e);return Object.keys(o).forEach((e=>{void 0!==t[e]&&null!==t[e]||(t[e]=o[e]&&o[e].default);})),t}(c),c=function(e){const t=S$1.extend({},e),n=e=>{if(null===e)return "any";if(void 0===e)return;if(Array.isArray(e))return "array";const t=typeof e;return "boolean"===t||"string"===t||"number"===t||"function"===t?t:"object"};return Object.keys(e).forEach((i=>{const s=e[i];if(null!=s){const c=o[i];if(void 0===c)a(ie$1.unknownOption(i));else {const o=c.type||n(c.default),u=c.validator;if(u){const n=u(i,e[i],r);void 0!==n?t[i]=n:delete t[i];}else if("any"!==o){const e=o.split("|"),r=n(s);e.indexOf(r)<0?"boolean"===o?(t[i]=!!s,a(ie$1.wrongOptionTypeBoolean(i,r))):(a(ie$1.wrongOptionType(i,o,r)),t[i]=c.default):"number"===r&&void 0!==c.minimum&&s<c.minimum&&(a(ie$1.optionBelowMinimum(i,s,c.minimum)),t[i]=c.minimum);}}}})),t.baseUrl=ue(t.baseUrl),t.streamUrl=ue(t.streamUrl),t.eventsUrl=ue(t.eventsUrl),t}(c),ae(c.logger),c},getTags:function(e){const t={};return e&&(e.application&&void 0!==e.application.id&&null!==e.application.id&&(t["application-id"]=[e.application.id]),e.application&&void 0!==e.application.version&&null!==e.application.id&&(t["application-version"]=[e.application.version])),t}};const{getLDUserAgentString:fe}=S$1;var ge={getLDHeaders:function(e,t){if(t&&!t.sendLDHeaders)return {};const n={};n[e.userAgentHeaderName||"User-Agent"]=fe(e),t&&t.wrapperName&&(n["X-LaunchDarkly-Wrapper"]=t.wrapperVersion?t.wrapperName+"/"+t.wrapperVersion:t.wrapperName);const r=de.getTags(t),o=Object.keys(r);return o.length&&(n["x-launchdarkly-tags"]=o.sort().map((e=>Array.isArray(r[e])?r[e].sort().map((t=>`${e}/${t}`)):[`${e}/${r[e]}`])).reduce(((e,t)=>e.concat(t)),[]).join(" ")),n},transformHeaders:function(e,t){return t&&t.requestHeaderTransform?t.requestHeaderTransform({...e}):e}};const{v1:ve}=ee,{getLDHeaders:pe,transformHeaders:me}=ge;var he=function(e,t,n){const r=S$1.extend({"Content-Type":"application/json"},pe(e,n)),o={};return o.sendEvents=(t,o,i)=>{if(!e.httpRequest)return Promise.resolve();const a=JSON.stringify(t),c=i?null:ve();return function t(u){const l=i?r:S$1.extend({},r,{"X-LaunchDarkly-Event-Schema":"4","X-LaunchDarkly-Payload-ID":c});return e.httpRequest("POST",o,me(l,n),a).promise.then((e=>{if(e)return e.status>=400&&s.isHttpErrorRecoverable(e.status)&&u?t(!1):function(e){const t={status:e.status},n=e.header("date");if(n){const e=Date.parse(n);e&&(t.serverTime=e);}return t}(e)})).catch((()=>u?t(!1):Promise.reject()))}(!0).catch((()=>{}))},o};var ye=function e(t,n=[]){if(null===t||"object"!=typeof t)return JSON.stringify(t);if(n.includes(t))throw new Error("Cycle detected");if(Array.isArray(t)){return `[${t.map((r=>e(r,[...n,t]))).map((e=>void 0===e?"null":e)).join(",")}]`}return `{${Object.keys(t).sort().map((r=>{const o=e(t[r],[...n,t]);if(void 0!==o)return `${JSON.stringify(r)}:${o}`})).filter((e=>void 0!==e)).join(",")}}`};const{commonBasicLogger:we}=ne;function be(e){return "string"==typeof e&&"kind"!==e&&e.match(/^(\w|\.|-)+$/)}function ke(e){return e.includes("%")||e.includes(":")?e.replace(/%/g,"%25").replace(/:/g,"%3A"):e}var Ee={checkContext:function(e,t){if(e){if(t&&(void 0===e.kind||null===e.kind))return void 0!==e.key&&null!==e.key;const n=e.key,r=void 0===e.kind?"user":e.kind,o=be(r),i="multi"===r||null!=n&&""!==n;if("multi"===r){const t=Object.keys(e).filter((e=>"kind"!==e));return i&&t.every((e=>be(e)))&&t.every((t=>{const n=e[t].key;return null!=n&&""!==n}))}return i&&o}return !1},getContextKeys:function(e,t=we()){if(!e)return;const n={},{kind:r,key:o}=e;switch(r){case void 0:n.user=`${o}`;break;case"multi":Object.entries(e).filter((([e])=>"kind"!==e)).forEach((([e,t])=>{t&&t.key&&(n[e]=t.key);}));break;case null:t.warn(`null is not a valid context kind: ${e}`);break;case"":t.warn(`'' is not a valid context kind: ${e}`);break;default:n[r]=`${o}`;}return n},getContextKinds:function(e){return e?null===e.kind||void 0===e.kind?["user"]:"multi"!==e.kind?[e.kind]:Object.keys(e).filter((e=>"kind"!==e)):[]},getCanonicalKey:function(e){if(e){if((void 0===e.kind||null===e.kind||"user"===e.kind)&&e.key)return e.key;if("multi"!==e.kind&&e.key)return `${e.kind}:${ke(e.key)}`;if("multi"===e.kind)return Object.keys(e).sort().filter((e=>"kind"!==e)).map((t=>`${t}:${ke(e[t].key)}`)).join(":")}}};const{getContextKinds:De}=Ee;var xe=function(){const e={};let t=0,n=0,r={},o={};return e.summarizeEvent=e=>{if("feature"===e.kind){const i=e.key+":"+(null!==e.variation&&void 0!==e.variation?e.variation:"")+":"+(null!==e.version&&void 0!==e.version?e.version:""),a=r[i];let s=o[e.key];s||(s=new Set,o[e.key]=s),function(e){return e.context?De(e.context):e.contextKeys?Object.keys(e.contextKeys):[]}(e).forEach((e=>s.add(e))),a?a.count=a.count+1:r[i]={count:1,key:e.key,version:e.version,variation:e.variation,value:e.value,default:e.default},(0===t||e.creationDate<t)&&(t=e.creationDate),e.creationDate>n&&(n=e.creationDate);}},e.getSummary=()=>{const e={};let i=!0;for(const t of Object.values(r)){let n=e[t.key];n||(n={default:t.default,counters:[],contextKinds:[...o[t.key]]},e[t.key]=n);const r={value:t.value,count:t.count};void 0!==t.variation&&null!==t.variation&&(r.variation=t.variation),void 0!==t.version&&null!==t.version?r.version=t.version:r.unknown=!0,n.counters.push(r),i=!1;}return i?null:{startDate:t,endDate:n,features:e,kind:"summary"}},e.clearSummary=()=>{t=0,n=0,r={},o={};},e};var Ce=function(e){let t={},n={};return {summarizeEvent:function(e){if("feature"===e.kind){const r=ye(e.context);if(!r)return;let o=t[r];o||(t[r]=xe(),o=t[r],n[r]=e.context),o.summarizeEvent(e);}},getSummaries:function(){const r=t,o=n;return t={},n={},Object.entries(r).map((([t,n])=>{const r=n.getSummary();return r.context=e.filter(o[t]),r}))}}};function Pe(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}function Se(e){return (e.startsWith("/")?e.substring(1):e).split("/").map((e=>e.indexOf("~")>=0?e.replace(/~1/g,"/").replace(/~0/g,"~"):e))}function Ie(e){return !e.startsWith("/")}function Oe(e,t){const n=Ie(e),r=Ie(t);if(n&&r)return e===t;if(n){const n=Se(t);return 1===n.length&&e===n[0]}if(r){const n=Se(e);return 1===n.length&&t===n[0]}return e===t}function Te(e){return `/${Pe(e)}`}var Le={cloneExcluding:function(e,t){const n=[],r={},o=[];for(n.push(...Object.keys(e).map((t=>({key:t,ptr:Te(t),source:e,parent:r,visited:[e]}))));n.length;){const e=n.pop();if(t.some((t=>Oe(t,e.ptr))))o.push(e.ptr);else {const t=e.source[e.key];if(null===t)e.parent[e.key]=t;else if(Array.isArray(t))e.parent[e.key]=[...t];else if("object"==typeof t){if(e.visited.includes(t))continue;e.parent[e.key]={},n.push(...Object.keys(t).map((n=>{return {key:n,ptr:(r=e.ptr,o=Pe(n),`${r}/${o}`),source:t,parent:e.parent[e.key],visited:[...e.visited,t]};var r,o;})));}else e.parent[e.key]=t;}}return {cloned:r,excluded:o.sort()}},compare:Oe,literalToReference:Te};var Ue=function(e){const t={},n=e.allAttributesPrivate,r=e.privateAttributes||[],o=["key","kind","_meta","anonymous"],i=["name","ip","firstName","lastName","email","avatar","country"],a=(e,t)=>{if("object"!=typeof e||null===e||Array.isArray(e))return;const{cloned:i,excluded:a}=Le.cloneExcluding(e,((e,t)=>(n||t&&e.anonymous?Object.keys(e):[...r,...e._meta&&e._meta.privateAttributes||[]]).filter((e=>!o.some((t=>Le.compare(e,t))))))(e,t));return i.key=String(i.key),a.length&&(i._meta||(i._meta={}),i._meta.redactedAttributes=a),i._meta&&(delete i._meta.privateAttributes,0===Object.keys(i._meta).length&&delete i._meta),void 0!==i.anonymous&&(i.anonymous=!!i.anonymous),i};return t.filter=(e,t=!1)=>void 0===e.kind||null===e.kind?a((e=>{const t={...e.custom||{},kind:"user",key:e.key};void 0!==e.anonymous&&(t.anonymous=!!e.anonymous);for(const n of i)delete t[n],void 0!==e[n]&&null!==e[n]&&(t[n]=String(e[n]));return void 0!==e.privateAttributeNames&&null!==e.privateAttributeNames&&(t._meta=t._meta||{},t._meta.privateAttributes=e.privateAttributeNames.map((e=>e.startsWith("/")?Le.literalToReference(e):e))),t})(e),t):"multi"===e.kind?((e,t)=>{const n={kind:e.kind},r=Object.keys(e);for(const o of r)if("kind"!==o){const r=a(e[o],t);r&&(n[o]=r);}return n})(e,t):a(e,t),t};const{getContextKeys:Ae}=Ee;var je=function(e,t,n,r=null,o=null,i=null){const a={},c=i||he(e,n,t),u=S$1.appendUrlPath(t.eventsUrl,"/events/bulk/"+n),l=Ue(t),d=Ce(l),f=t.samplingInterval,g=t.eventCapacity,v=t.flushInterval,p=t.logger;let m,h=[],y=0,w=!1,b=!1;function k(){return 0===f||0===Math.floor(Math.random()*f)}function E(e){const t=S$1.extend({},e);return "identify"===e.kind||"feature"===e.kind||"custom"===e.kind?t.context=l.filter(e.context):(t.contextKeys=Ae(e.context,p),delete t.context),"feature"===e.kind&&(delete t.trackEvents,delete t.debugEventsUntilDate),t}function D(e){h.length<g?(h.push(e),b=!1):(b||(b=!0,p.warn(ie$1.eventCapacityExceeded())),r&&r.incrementDroppedEvents());}return a.enqueue=function(e){if(w)return;let t=!1,n=!1;var r;if(d.summarizeEvent(e),"feature"===e.kind?k()&&(t=!!e.trackEvents,n=!!(r=e).debugEventsUntilDate&&r.debugEventsUntilDate>y&&r.debugEventsUntilDate>(new Date).getTime()):t=k(),t&&D(E(e)),n){const t=S$1.extend({},e,{kind:"debug"});t.context=l.filter(t.context),delete t.trackEvents,delete t.debugEventsUntilDate,D(t);}},a.flush=async function(){if(w)return Promise.resolve();const e=h;return d.getSummaries().forEach((t=>{Object.keys(t.features).length&&e.push(t);})),r&&r.setEventsInLastBatch(e.length),0===e.length?Promise.resolve():(h=[],p.debug(ie$1.debugPostingEvents(e.length)),c.sendEvents(e,u).then((e=>{e&&(e.serverTime&&(y=e.serverTime),s.isHttpErrorRecoverable(e.status)||(w=!0),e.status>=400&&S$1.onNextTick((()=>{o.maybeReportError(new s.LDUnexpectedResponseError(ie$1.httpErrorMessage(e.status,"event posting","some events were dropped")));})));})))},a.start=function(){const e=()=>{a.flush(),m=setTimeout(e,v);};m=setTimeout(e,v);},a.stop=function(){clearTimeout(m);},a};var Re=function(e){const t={},n={};return t.on=function(e,t,r){n[e]=n[e]||[],n[e]=n[e].concat({handler:t,context:r});},t.off=function(e,t,r){if(n[e])for(let o=0;o<n[e].length;o++)n[e][o].handler===t&&n[e][o].context===r&&(n[e]=n[e].slice(0,o).concat(n[e].slice(o+1)));},t.emit=function(e){if(!n[e])return;const t=n[e].slice(0);for(let e=0;e<t.length;e++)t[e].handler.apply(t[e].context,Array.prototype.slice.call(arguments,1));},t.getEvents=function(){return Object.keys(n)},t.getEventListenerCount=function(e){return n[e]?n[e].length:0},t.maybeReportError=function(t){t&&(n["error"]?this.emit("error",t):(e||console).error(t.message));},t};const Fe="ready",Ne="initialized",$e="failed";var Ve=function(e){let t=!1,n=!1,r=null,o=null;const i=new Promise((t=>{const n=()=>{e.off(Fe,n),t();};e.on(Fe,n);})).catch((()=>{}));return {getInitializationPromise:()=>o||(t?Promise.resolve():n?Promise.reject(r):(o=new Promise(((t,n)=>{const r=()=>{e.off(Ne,r),t();},o=t=>{e.off($e,o),n(t);};e.on(Ne,r),e.on($e,o);})),o)),getReadyPromise:()=>i,signalSuccess:()=>{t||n||(t=!0,e.emit(Ne),e.emit(Fe));},signalFailure:o=>{t||n||(n=!0,r=o,e.emit($e,o),e.emit(Fe)),e.maybeReportError(o);}}};var He=function(e,t,n,r){const o={};function i(){let e="";const o=r.getContext();return o&&(e=n||S$1.btoa(JSON.stringify(o))),"ld:"+t+":"+e}return o.loadFlags=()=>e.get(i()).then((e=>{if(null==e)return null;try{let t=JSON.parse(e);if(t){const e=t.$schema;void 0===e||e<1?t=S$1.transformValuesToVersionedValues(t):delete t.$schema;}return t}catch(e){return o.clearFlags().then((()=>null))}})),o.saveFlags=t=>{const n=S$1.extend({},t,{$schema:1});return e.set(i(),JSON.stringify(n))},o.clearFlags=()=>e.clear(i()),o};var Me=function(e,t){const n={};let r=!1;const o=e=>{r||(r=!0,t.warn(ie$1.localStorageUnavailable(e)));};return n.isEnabled=()=>!!e,n.get=t=>new Promise((n=>{e?e.get(t).then(n).catch((e=>{o(e),n(void 0);})):n(void 0);})),n.set=(t,n)=>new Promise((r=>{e?e.set(t,n).then((()=>r(!0))).catch((e=>{o(e),r(!1);})):r(!1);})),n.clear=t=>new Promise((n=>{e?e.clear(t).then((()=>n(!0))).catch((e=>{o(e),n(!1);})):n(!1);})),n};const{appendUrlPath:qe,base64URLEncode:ze,objectHasOwnProperty:Ke}=S$1,{getLDHeaders:_e,transformHeaders:Je}=ge,{isHttpErrorRecoverable:Be}=s;var Ge=function(e,t,n,r){const o=t.streamUrl,i=t.logger,a={},s=qe(o,"/eval/"+n),c=t.useReport,u=t.evaluationReasons,l=t.streamReconnectDelay,d=_e(e,t);let f,g=!1,v=null,p=null,m=null,h=null,y=null,w=0;function b(){const e=(t=function(){const e=l*Math.pow(2,w);return e>3e4?3e4:e}(),t-Math.trunc(.5*Math.random()*t));var t;return w+=1,e}function k(e){if(e.status&&"number"==typeof e.status&&!Be(e.status))return x(),i.error(ie$1.unrecoverableStreamError(e)),void(p&&(clearTimeout(p),p=null));const t=b();g||(i.warn(ie$1.streamError(e,t)),g=!0),C(!1),x(),E(t);}function E(e){p||(e?p=setTimeout(D,e):D());}function D(){let r;p=null;let a="";const l={headers:d,readTimeoutMillis:3e5};if(e.eventSourceFactory){null!=h&&(a="h="+h),c?e.eventSourceAllowsReport?(r=s,l.method="REPORT",l.headers["Content-Type"]="application/json",l.body=JSON.stringify(m)):(r=qe(o,"/ping/"+n),a=""):r=s+"/"+ze(JSON.stringify(m)),l.headers=Je(l.headers,t),u&&(a=a+(a?"&":"")+"withReasons=true"),r=r+(a?"?":"")+a,x(),i.info(ie$1.streamConnecting(r)),f=(new Date).getTime(),v=e.eventSourceFactory(r,l);for(const e in y)Ke(y,e)&&v.addEventListener(e,y[e]);v.onerror=k,v.onopen=()=>{w=0;};}}function x(){v&&(i.info(ie$1.streamClosing()),v.close(),v=null);}function C(e){f&&r&&r.recordStreamInit(f,!e,(new Date).getTime()-f),f=null;}return a.connect=function(e,t,n){m=e,h=t,y={};for(const e in n||{})y[e]=function(t){g=!1,C(!0),n[e]&&n[e](t);};E();},a.disconnect=function(){clearTimeout(p),p=null,x();},a.isConnected=function(){return !!(v&&e.eventSourceIsActive&&e.eventSourceIsActive(v))},a};var We=function(e){let t,n,r,o;const i={addPromise:(i,a)=>{t=i,n&&n(),n=a,i.then((n=>{t===i&&(r(n),e&&e());}),(n=>{t===i&&(o(n),e&&e());}));}};return i.resultPromise=new Promise(((e,t)=>{r=e,o=t;})),i};const{transformHeaders:Xe,getLDHeaders:Qe}=ge,Ye="application/json";var Ze=function(e,t,n){const r=t.baseUrl,o=t.useReport,i=t.evaluationReasons,a=t.logger,c={},u={};function l(n,r){if(!e.httpRequest)return new Promise(((e,t)=>{t(new s.LDFlagFetchError(ie$1.httpUnavailable()));}));const o=r?"REPORT":"GET",i=Qe(e,t);r&&(i["Content-Type"]=Ye);let a=u[n];a||(a=We((()=>{delete u[n];})),u[n]=a);const c=e.httpRequest(o,n,Xe(i,t),r),l=c.promise.then((e=>{if(200===e.status){if(e.header("content-type")&&e.header("content-type").substring(0,16)===Ye)return JSON.parse(e.body);{const t=ie$1.invalidContentType(e.header("content-type")||"");return Promise.reject(new s.LDFlagFetchError(t))}}return Promise.reject(function(e){return 404===e.status?new s.LDInvalidEnvironmentIdError(ie$1.environmentNotFound()):new s.LDFlagFetchError(ie$1.errorFetchingFlags(e.statusText||String(e.status)))}(e))}),(e=>Promise.reject(new s.LDFlagFetchError(ie$1.networkError(e)))));return a.addPromise(l,(()=>{c.cancel&&c.cancel();})),a.resultPromise}return c.fetchJSON=function(e){return l(S$1.appendUrlPath(r,e),null)},c.fetchFlagSettings=function(e,t){let s,c,u,d="";return o?(c=[r,"/sdk/evalx/",n,"/context"].join(""),u=JSON.stringify(e)):(s=S$1.base64URLEncode(JSON.stringify(e)),c=[r,"/sdk/evalx/",n,"/contexts/",s].join("")),t&&(d="h="+t),i&&(d=d+(d?"&":"")+"withReasons=true"),c=c+(d?"?":"")+d,a.debug(ie$1.debugPolling(c)),l(c,u)},c};var et=function(e,t){const n={};let r;return n.setContext=function(e){r=S$1.sanitizeContext(e),r&&t&&t(S$1.clone(r));},n.getContext=function(){return r?S$1.clone(r):null},e&&n.setContext(e),n};const{v1:tt}=ee,{getContextKinds:nt}=Ee;var rt=function(e){function t(e){return null==e||"user"===e?"ld:$anonUserId":`ld:$contextKey:${e}`}function n(n,r){return null!==r.key&&void 0!==r.key?(r.key=r.key.toString(),Promise.resolve(r)):r.anonymous?function(n){return e.get(t(n))}(n).then((o=>{if(o)return r.key=o,r;{const o=tt();return r.key=o,function(n,r){return e.set(t(r),n)}(o,n).then((()=>r))}})):Promise.reject(new s.LDInvalidUserError(ie$1.invalidContext()))}this.processContext=e=>{if(!e)return Promise.reject(new s.LDInvalidUserError(ie$1.contextNotSpecified()));const t=S$1.clone(e);if("multi"===e.kind){const e=nt(t);return Promise.all(e.map((e=>n(e,t[e])))).then((()=>t))}return n(e.kind,t)};};const{v1:ot}=ee,{baseOptionDefs:it}=de,{appendUrlPath:at}=S$1;var st={DiagnosticId:function(e){const t={diagnosticId:ot()};return e&&(t.sdkKeySuffix=e.length>6?e.substring(e.length-6):e),t},DiagnosticsAccumulator:function(e){let t,n,r,o;function i(e){t=e,n=0,r=0,o=[];}return i(e),{getProps:()=>({dataSinceDate:t,droppedEvents:n,eventsInLastBatch:r,streamInits:o}),setProps:e=>{t=e.dataSinceDate,n=e.droppedEvents||0,r=e.eventsInLastBatch||0,o=e.streamInits||[];},incrementDroppedEvents:()=>{n++;},setEventsInLastBatch:e=>{r=e;},recordStreamInit:(e,t,n)=>{const r={timestamp:e,failed:t,durationMillis:n};o.push(r);},reset:i}},DiagnosticsManager:function(e,t,n,r,o,i,a){const s=!!e.diagnosticUseCombinedEvent,c="ld:"+o+":$diagnostics",u=at(i.eventsUrl,"/events/diagnostic/"+o),l=i.diagnosticRecordingInterval,d=n;let f,g,v=!!i.streaming;const p={};function m(){return {sdk:w(),configuration:b(),platform:e.diagnosticPlatformData}}function h(e){i.logger&&i.logger.debug(ie$1.debugPostingDiagnosticEvent(e)),r.sendEvents(e,u,!0).then((()=>{})).catch((()=>{}));}function y(){h(function(){const e=(new Date).getTime();let t={kind:s?"diagnostic-combined":"diagnostic",id:a,creationDate:e,...d.getProps()};return s&&(t={...t,...m()}),d.reset(e),t}()),g=setTimeout(y,l),f=(new Date).getTime(),s&&function(){if(t.isEnabled()){const e={...d.getProps()};t.set(c,JSON.stringify(e));}}();}function w(){const t={...e.diagnosticSdkData};return i.wrapperName&&(t.wrapperName=i.wrapperName),i.wrapperVersion&&(t.wrapperVersion=i.wrapperVersion),t}function b(){return {customBaseURI:i.baseUrl!==it.baseUrl.default,customStreamURI:i.streamUrl!==it.streamUrl.default,customEventsURI:i.eventsUrl!==it.eventsUrl.default,eventsCapacity:i.eventCapacity,eventsFlushIntervalMillis:i.flushInterval,reconnectTimeMillis:i.streamReconnectDelay,streamingDisabled:!v,allAttributesPrivate:!!i.allAttributesPrivate,diagnosticRecordingIntervalMillis:i.diagnosticRecordingInterval,usingSecureMode:!!i.hash,bootstrapMode:!!i.bootstrap,fetchGoalsDisabled:!i.fetchGoals,sendEventsOnlyForVariation:!!i.sendEventsOnlyForVariation}}return p.start=()=>{s?function(e){if(!t.isEnabled())return e(!1);t.get(c).then((t=>{if(t)try{const e=JSON.parse(t);d.setProps(e),f=e.dataSinceDate;}catch(e){}e(!0);})).catch((()=>{e(!1);}));}((e=>{if(e){const e=(f||0)+l,t=(new Date).getTime();t>=e?y():g=setTimeout(y,e-t);}else 0===Math.floor(4*Math.random())?y():g=setTimeout(y,l);})):(h({kind:"diagnostic-init",id:a,creationDate:d.getProps().dataSinceDate,...m()}),g=setTimeout(y,l));},p.stop=()=>{g&&clearTimeout(g);},p.setStreaming=e=>{v=e;},p}};var ct=function(e,t){let n=!1;const r={type:e.type,name:e.name,synchronous:e.synchronous,method:(...o)=>{try{e.method(...o);}catch{n||(n=!0,t.warn(ie$1.inspectorMethodError(r.type,r.name)));}}};return r};const{onNextTick:ut}=S$1,lt={flagUsed:"flag-used",flagDetailsChanged:"flag-details-changed",flagDetailChanged:"flag-detail-changed",clientIdentityChanged:"client-identity-changed"};Object.freeze(lt);var dt={InspectorTypes:lt,InspectorManager:function(e,t){const n={},r={[lt.flagUsed]:[],[lt.flagDetailsChanged]:[],[lt.flagDetailChanged]:[],[lt.clientIdentityChanged]:[]},o={[lt.flagUsed]:[],[lt.flagDetailsChanged]:[],[lt.flagDetailChanged]:[],[lt.clientIdentityChanged]:[]},i=e&&e.map((e=>ct(e,t)));return i&&i.forEach((e=>{Object.prototype.hasOwnProperty.call(r,e.type)&&!e.synchronous?r[e.type].push(e):Object.prototype.hasOwnProperty.call(o,e.type)&&e.synchronous?o[e.type].push(e):t.warn(ie$1.invalidInspector(e.type,e.name));})),n.hasListeners=e=>r[e]&&r[e].length||o[e]&&o[e].length,n.onFlagUsed=(e,t,n)=>{const i=lt.flagUsed;o[i].length&&o[i].forEach((r=>r.method(e,t,n))),r[i].length&&ut((()=>{r[i].forEach((r=>r.method(e,t,n)));}));},n.onFlags=e=>{const t=lt.flagDetailsChanged;o[t].length&&o[t].forEach((t=>t.method(e))),r[t].length&&ut((()=>{r[t].forEach((t=>t.method(e)));}));},n.onFlagChanged=(e,t)=>{const n=lt.flagDetailChanged;o[n].length&&o[n].forEach((n=>n.method(e,t))),r[n].length&&ut((()=>{r[n].forEach((n=>n.method(e,t)));}));},n.onIdentityChanged=e=>{const t=lt.clientIdentityChanged;o[t].length&&o[t].forEach((t=>t.method(e))),r[t].length&&ut((()=>{r[t].forEach((t=>t.method(e)));}));},n}};const{LDTimeoutError:ft}=s;var gt=function(e,t){return new Promise(((n,r)=>{setTimeout((()=>{r(new ft(`${t} timed out after ${e} seconds.`));}),1e3*e);}))};const vt="unknown hook";function pt(e,t,n,r,o){try{return r()}catch(r){return e?.error(`An error was encountered in "${t}" of the "${n}" hook: ${r}`),o}}function mt(e,t){try{return t.getMetadata().name||vt}catch{return e.error("Exception thrown getting metadata for hook. Unable to get hook name."),vt}}var ht=function(e,t){const n=t?[...t]:[];return {withEvaluation:function(t,r,o,i){if(0===n.length)return i();const a=[...n],s={flagKey:t,context:r,defaultValue:o},c=function(e,t,n){return t.map((t=>pt(e,"beforeEvaluation",mt(e,t),(()=>t?.beforeEvaluation?.(n,{})??{}),{})))}(e,a,s),u=i();return function(e,t,n,r,o){for(let i=t.length-1;i>=0;i-=1){const a=t[i],s=r[i];pt(e,"afterEvaluation",mt(e,a),(()=>a?.afterEvaluation?.(n,s,o)??{}),{});}}(e,a,s,c,u),u},identify:function(t,r){const o=[...n],i={context:t,timeout:r},a=function(e,t,n){return t.map((t=>pt(e,"beforeIdentify",mt(e,t),(()=>t?.beforeIdentify?.(n,{})??{}),{})))}(e,o,i);return t=>{!function(e,t,n,r,o){for(let i=t.length-1;i>=0;i-=1){const a=t[i],s=r[i];pt(e,"afterIdentify",mt(e,a),(()=>a?.afterIdentify?.(n,s,o)??{}),{});}}(e,o,i,a,t);}},addHook:function(e){n.push(e);},afterTrack:function(t){if(0===n.length)return;const r=[...n];!function(e,t,n){for(let r=t.length-1;r>=0;r-=1){const o=t[r];pt(e,"afterTrack",mt(e,o),(()=>o?.afterTrack?.(n)),void 0);}}(e,r,t);}}};const yt="unknown plugin";function wt(e,t){try{return t.getMetadata().name||yt}catch(t){return e.error("Exception thrown getting metadata for plugin. Unable to get plugin name."),yt}}var bt={getPluginHooks:function(e,t,n){const r=[];return n.forEach((n=>{try{const o=n.getHooks?.(t);void 0===o?e.error(`Plugin ${wt(e,n)} returned undefined from getHooks.`):o&&o.length>0&&r.push(...o);}catch(t){e.error(`Exception thrown getting hooks for plugin ${wt(e,n)}. Unable to get hooks.`);}})),r},registerPlugins:function(e,t,n,r){r.forEach((r=>{try{r.register(n,t);}catch(t){e.error(`Exception thrown registering plugin ${wt(e,r)}.`);}}));},createPluginEnvironment:function(e,t,n){const r={};e.userAgent&&(r.name=e.userAgent),e.version&&(r.version=e.version),n.wrapperName&&(r.wrapperName=n.wrapperName),n.wrapperVersion&&(r.wrapperVersion=n.wrapperVersion);const o={};n.application&&(n.application.name&&(o.name=n.application.name),n.application.version&&(o.version=n.application.version));const i={sdk:r,clientSideId:t};return Object.keys(o).length>0&&(i.application=o),i}};const{commonBasicLogger:kt}=ne,{checkContext:Et,getContextKeys:Dt}=Ee,{InspectorTypes:xt,InspectorManager:Ct}=dt,{getPluginHooks:Pt,registerPlugins:St,createPluginEnvironment:It}=bt,Ot="change",Tt="internal-change";var Lt={initialize:function(e,t,n,r,o){const i=function(){if(n&&n.logger)return n.logger;return o&&o.logger&&o.logger.default||kt("warn")}(),a=Re(i),c=Ve(a),u=de.validate(n,a,o,i),l=Ct(u.inspectors,i),d=u.sendEvents;let f=e,g=u.hash;const v=[...u.plugins],p=It(r,e,u),m=Pt(i,p,v),h=ht(i,[...u.hooks,...m]),y=Me(r.localStorage,i),w=he(r,f,u),b=u.sendEvents&&!u.diagnosticOptOut,k=b?st.DiagnosticId(f):null,E=b?st.DiagnosticsAccumulator((new Date).getTime()):null,D=b?st.DiagnosticsManager(r,y,E,w,f,u,k):null,x=Ge(r,u,f,E),C=u.eventProcessor||je(r,u,f,E,a,w),P=Ze(r,u,f);let I,O,T,L={},U=u.streaming,A=!1,j=!1,R=!0;const F=u.stateProvider,N=et(null,(function(e){((function(e){if(F)return;e&&H({kind:"identify",context:e,creationDate:(new Date).getTime()});}))(e),l.hasListeners(xt.clientIdentityChanged)&&l.onIdentityChanged(N.getContext());})),$=new rt(y),V=y.isEnabled()?He(y,f,g,N):null;function H(e){f&&(F&&F.enqueueEvent&&F.enqueueEvent(e)||(e.context?(R=!1,!d||j||r.isDoNotTrack()||(i.debug(ie$1.debugEnqueueingEvent(e.kind)),C.enqueue(e))):R&&(i.warn(ie$1.eventWithoutContext()),R=!1)));}function M(e,t){l.hasListeners(xt.flagDetailChanged)&&l.onFlagChanged(e.key,J(t));}function q(){l.hasListeners(xt.flagDetailsChanged)&&l.onFlags(Object.entries(L).map((([e,t])=>({key:e,detail:J(t)}))).reduce(((e,t)=>(e[t.key]=t.detail,e)),{}));}function z(e,t,n,r){const o=N.getContext(),i=new Date,a={kind:"feature",key:e,context:o,value:t?t.value:null,variation:t?t.variationIndex:null,default:n,creationDate:i.getTime()},s=L[e];s&&(a.version=s.flagVersion?s.flagVersion:s.version,a.trackEvents=s.trackEvents,a.debugEventsUntilDate=s.debugEventsUntilDate),(r||s&&s.trackReason)&&t&&(a.reason=t.reason),H(a);}function K(e){return Et(e,!1)?Promise.resolve(e):Promise.reject(new s.LDInvalidUserError(ie$1.invalidContext()))}function _(e,t,n,r,o,i){let a,s;return L&&S$1.objectHasOwnProperty(L,e)&&L[e]&&!L[e].deleted?(s=L[e],a=J(s),null!==s.value&&void 0!==s.value||(a.value=t)):a={value:t,variationIndex:null,reason:{kind:"ERROR",errorKind:"FLAG_NOT_FOUND"}},n&&(o||s?.prerequisites?.forEach((e=>{_(e,void 0,n,!1,!1,!1);})),z(e,a,t,r)),!o&&i&&function(e,t){l.hasListeners(xt.flagUsed)&&l.onFlagUsed(e,t,N.getContext());}(e,a),a}function J(e){return {value:e.value,variationIndex:void 0===e.variation?null:e.variation,reason:e.reason||null}}function B(){if(O=!0,!N.getContext())return;const e=e=>{try{return JSON.parse(e)}catch(e){return void a.maybeReportError(new s.LDInvalidDataError(ie$1.invalidData()))}};x.connect(N.getContext(),g,{ping:function(){i.debug(ie$1.debugStreamPing());const e=N.getContext();P.fetchFlagSettings(e,g).then((t=>{S$1.deepEquals(e,N.getContext())&&W(t||{});})).catch((e=>{a.maybeReportError(new s.LDFlagFetchError(ie$1.errorFetchingFlags(e)));}));},put:function(t){const n=e(t.data);n&&(i.debug(ie$1.debugStreamPut()),W(n));},patch:function(t){const n=e(t.data);if(!n)return;const r=L[n.key];if(!r||!r.version||!n.version||r.version<n.version){i.debug(ie$1.debugStreamPatch(n.key));const e={},t=S$1.extend({},n);delete t.key,L[n.key]=t;const o=J(t);e[n.key]=r?{previous:r.value,current:o}:{current:o},M(n,t),X(e);}else i.debug(ie$1.debugStreamPatchIgnored(n.key));},delete:function(t){const n=e(t.data);if(n)if(!L[n.key]||L[n.key].version<n.version){i.debug(ie$1.debugStreamDelete(n.key));const e={};L[n.key]&&!L[n.key].deleted&&(e[n.key]={previous:L[n.key].value}),L[n.key]={version:n.version,deleted:!0},M(n,L[n.key]),X(e);}else i.debug(ie$1.debugStreamDeleteIgnored(n.key));}});}function G(){O&&(x.disconnect(),O=!1);}function W(e){const t={};if(!e)return Promise.resolve();for(const n in L)S$1.objectHasOwnProperty(L,n)&&L[n]&&(e[n]&&!S$1.deepEquals(e[n].value,L[n].value)?t[n]={previous:L[n].value,current:J(e[n])}:e[n]&&!e[n].deleted||(t[n]={previous:L[n].value}));for(const n in e)S$1.objectHasOwnProperty(e,n)&&e[n]&&(!L[n]||L[n].deleted)&&(t[n]={current:J(e[n])});return L={...e},q(),X(t).catch((()=>{}))}function X(e){const t=Object.keys(e);if(t.length>0){const n={};t.forEach((t=>{const r=e[t].current,o=r?r.value:void 0,i=e[t].previous;a.emit(Ot+":"+t,o,i),n[t]=r?{current:o,previous:i}:{previous:i};})),a.emit(Ot,n),a.emit(Tt,L),u.sendEventsOnlyForVariation||F||t.forEach((t=>{z(t,e[t].current);}));}return I&&V?V.saveFlags(L):Promise.resolve()}function Q(){const e=U||T&&void 0===U;e&&!O?B():!e&&O&&G(),D&&D.setStreaming(e);}function Y(e){return e===Ot||e.substr(0,7)===Ot+":"}if("string"==typeof u.bootstrap&&"LOCALSTORAGE"===u.bootstrap.toUpperCase()&&(V?I=!0:i.warn(ie$1.localStorageUnavailable())),"object"==typeof u.bootstrap&&(L=function(e){const t=Object.keys(e),n="$flagsState",r="$valid",o=e[n];!o&&t.length&&i.warn(ie$1.bootstrapOldFormat()),!1===e[r]&&i.warn(ie$1.bootstrapInvalid());const a={};return t.forEach((t=>{if(t!==n&&t!==r){let n={value:e[t]};o&&o[t]?n=S$1.extend(n,o[t]):n.version=0,a[t]=n;}})),a}(u.bootstrap)),F){const e=F.getInitialState();e?Z(e):F.on("init",Z),F.on("update",(function(e){e.context&&N.setContext(e.context);e.flags&&W(e.flags);}));}else (function(){if(!e)return Promise.reject(new s.LDInvalidEnvironmentIdError(ie$1.environmentNotSpecified()));let n;return $.processContext(t).then(K).then((e=>(n=S$1.once(h.identify(e,void 0)),e))).then((e=>(n?.({status:"completed"}),N.setContext(e),"object"==typeof u.bootstrap?ee():I?V.loadFlags().then((e=>null==e?(L={},P.fetchFlagSettings(N.getContext(),g).then((e=>W(e||{}))).then(ee).catch((e=>{te(new s.LDFlagFetchError(ie$1.errorFetchingFlags(e)));}))):(L=e,S$1.onNextTick(ee),P.fetchFlagSettings(N.getContext(),g).then((e=>W(e))).catch((e=>a.maybeReportError(e)))))):P.fetchFlagSettings(N.getContext(),g).then((e=>{L=e||{},q(),ee();})).catch((e=>{L={},te(e);}))))).catch((e=>{throw n?.({status:"error"}),e}))})().catch(te);function Z(e){f=e.environment,N.setContext(e.context),L={...e.flags},S$1.onNextTick(ee);}function ee(){i.info(ie$1.clientInitialized()),A=!0,Q(),c.signalSuccess();}function te(e){c.signalFailure(e);}const ne={waitForInitialization:function(e=void 0){if(null!=e){if("number"==typeof e)return function(e){e>5&&i.warn("The waitForInitialization function was called with a timeout greater than 5 seconds. We recommend a timeout of 5 seconds or less.");const t=c.getInitializationPromise(),n=gt(e,"waitForInitialization");return Promise.race([n,t]).catch((e=>{throw e instanceof s.LDTimeoutError&&i.error(`waitForInitialization error: ${e}`),e}))}(e);i.warn("The waitForInitialization method was provided with a non-numeric timeout.");}return i.warn("The waitForInitialization function was called without a timeout specified. In a future version a default timeout will be applied."),c.getInitializationPromise()},waitUntilReady:()=>c.getReadyPromise(),identify:function(e,t,n){if(j)return S$1.wrapPromiseCallback(Promise.resolve({}),n);if(F)return i.warn(ie$1.identifyDisabled()),S$1.wrapPromiseCallback(Promise.resolve(S$1.transformVersionedValuesToValues(L)),n);let r;const o=I&&V?V.clearFlags():Promise.resolve();return S$1.wrapPromiseCallback(o.then((()=>$.processContext(e))).then(K).then((e=>(r=S$1.once(h.identify(e,void 0)),e))).then((e=>P.fetchFlagSettings(e,t).then((n=>{const r=S$1.transformVersionedValuesToValues(n);return N.setContext(e),g=t,n?W(n).then((()=>r)):r})))).then((e=>(r?.({status:"completed"}),O&&B(),e))).catch((e=>(r?.({status:"error"}),a.maybeReportError(e),Promise.reject(e)))),n)},getContext:function(){return N.getContext()},variation:function(e,t){const{value:n}=h.withEvaluation(e,N.getContext(),t,(()=>_(e,t,!0,!1,!1,!0)));return n},variationDetail:function(e,t){return h.withEvaluation(e,N.getContext(),t,(()=>_(e,t,!0,!0,!1,!0)))},track:function(e,t,n){if("string"!=typeof e)return void a.maybeReportError(new s.LDInvalidEventKeyError(ie$1.unknownCustomEventKey(e)));void 0!==n&&"number"!=typeof n&&i.warn(ie$1.invalidMetricValue(typeof n)),r.customEventFilter&&!r.customEventFilter(e)&&i.warn(ie$1.unknownCustomEventKey(e));const o=N.getContext(),c={kind:"custom",key:e,context:o,url:r.getCurrentUrl(),creationDate:(new Date).getTime()};o&&o.anonymous&&(c.contextKind=o.anonymous?"anonymousUser":"user"),null!=t&&(c.data=t),null!=n&&(c.metricValue=n),H(c),h.afterTrack({context:o,key:e,data:t,metricValue:n});},on:function(e,t,n){Y(e)?(T=!0,A&&Q(),a.on(e,t,n)):a.on(...arguments);},off:function(e){if(a.off(...arguments),Y(e)){let e=!1;a.getEvents().forEach((t=>{Y(t)&&a.getEventListenerCount(t)>0&&(e=!0);})),e||(T=!1,O&&void 0===U&&G());}},setStreaming:function(e){const t=null===e?void 0:e;t!==U&&(U=t,Q());},flush:function(e){return S$1.wrapPromiseCallback(d?C.flush():Promise.resolve(),e)},allFlags:function(){const e={};if(!L)return e;for(const t in L)S$1.objectHasOwnProperty(L,t)&&!L[t].deleted&&(e[t]=_(t,null,!u.sendEventsOnlyForVariation,!1,!0,!1).value);return e},close:function(e){if(j)return S$1.wrapPromiseCallback(Promise.resolve(),e);const t=()=>{j=!0,L={};},n=Promise.resolve().then((()=>{if(G(),D&&D.stop(),d)return C.stop(),C.flush()})).then(t).catch(t);return S$1.wrapPromiseCallback(n,e)},addHook:function(e){h.addHook(e);}};return St(i,p,ne,v),{client:ne,options:u,emitter:a,ident:N,logger:i,requestor:P,start:function(){d&&(D&&D.start(),C.start());},enqueueEvent:H,getFlagsInternal:function(){return L},getEnvironmentId:()=>f,internalChangeEventName:Tt}},commonBasicLogger:kt,errors:s,messages:ie$1,utils:S$1,getContextKeys:Dt},Ut=Lt.initialize,At=Lt.errors;function Rt(e,t,n){return (t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return ("string"===t?String:Number)(e)}(e,"string");return "symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ft(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r);}return n}function Nt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ft(Object(n),!0).forEach((function(t){Rt(e,t,n[t]);})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ft(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t));}));}return e}var Ht={promise:Promise.resolve({status:200,header:function(){return null},body:null})};function Mt(e,t,n,r,o){if(o&&!function(){var e=window.navigator&&window.navigator.userAgent;if(e){var t=e.match(/Chrom(e|ium)\/([0-9]+)\./);if(t)return parseInt(t[2],10)<73}return !0}())return Ht;var i=new window.XMLHttpRequest;for(var a in i.open(e,t,!o),n||{})Object.prototype.hasOwnProperty.call(n,a)&&i.setRequestHeader(a,n[a]);if(o){try{i.send(r);}catch(e){}return Ht}var s,c=new Promise((function(e,t){i.addEventListener("load",(function(){s||e({status:i.status,header:function(e){return i.getResponseHeader(e)},body:i.responseText});})),i.addEventListener("error",(function(){s||t(new Error);})),i.send(r);}));return {promise:c,cancel:function(){s=!0,i.abort();}}}var qt=e=>{if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")};function zt(e,t,n,r){var o,i,a=(("substring"===e.kind||"regex"===e.kind)&&r.includes("/")?t:t.replace(r,"")).replace(n,"");switch(e.kind){case"exact":i=t,o=new RegExp("^"+qt(e.url)+"/?$");break;case"canonical":i=a,o=new RegExp("^"+qt(e.url)+"/?$");break;case"substring":i=a,o=new RegExp(".*"+qt(e.substring)+".*$");break;case"regex":i=a,o=new RegExp(e.pattern);break;default:return !1}return o.test(i)}function Kt(e,t){for(var n={},r=null,o=[],i=0;i<e.length;i++)for(var a=e[i],s=a.urls||[],c=0;c<s.length;c++)if(zt(s[c],window.location.href,window.location.search,window.location.hash)){"pageview"===a.kind?t("pageview",a):(o.push(a),t("click_pageview",a));break}return o.length>0&&(r=function(e){for(var n=function(e,t){for(var n=[],r=0;r<t.length;r++)for(var o=e.target,i=t[r],a=i.selector,s=document.querySelectorAll(a);o&&s.length>0;){for(var c=0;c<s.length;c++)o===s[c]&&n.push(i);o=o.parentNode;}return n}(e,o),r=0;r<n.length;r++)t("click",n[r]);},document.addEventListener("click",r)),n.dispose=function(){document.removeEventListener("click",r);},n}function _t(e,t){var n,r;function o(){r&&r.dispose(),n&&n.length&&(r=Kt(n,i));}function i(t,n){var r=e.ident.getContext(),o={kind:t,key:n.key,data:null,url:window.location.href,creationDate:(new Date).getTime(),context:r};return "click"===t&&(o.selector=n.selector),e.enqueueEvent(o)}return e.requestor.fetchJSON("/sdk/goals/"+e.getEnvironmentId()).then((function(e){e&&e.length>0&&(r=Kt(n=e,i),function(e,t){var n,r=window.location.href;function o(){(n=window.location.href)!==r&&(r=n,t());}!function e(t,n){t(),setTimeout((function(){e(t,n);}),n);}(o,e),window.history&&window.history.pushState?window.addEventListener("popstate",o):window.addEventListener("hashchange",o);}(300,o)),t();})).catch((function(n){e.emitter.maybeReportError(new At.LDUnexpectedResponseError((n&&n.message,n.message))),t();})),{}}var Jt="goalsReady",Bt={fetchGoals:{default:!0},hash:{type:"string"},eventProcessor:{type:"object"},eventUrlTransformer:{type:"function"},disableSyncEventPost:{default:!1}};function Gt(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=function(e){var t,n={userAgentHeaderName:"X-LaunchDarkly-User-Agent",synchronousFlush:!1};if(window.XMLHttpRequest){var r=e&&e.disableSyncEventPost;n.httpRequest=function(e,t,o,i){var a=n.synchronousFlush&!r;return n.synchronousFlush=!1,Mt(e,t,o,i,a)};}n.httpAllowsPost=function(){return void 0===t&&(t=!!window.XMLHttpRequest&&"withCredentials"in new window.XMLHttpRequest),t},n.httpFallbackPing=function(e){(new window.Image).src=e;};var o,i=e&&e.eventUrlTransformer;n.getCurrentUrl=function(){return i?i(window.location.href):window.location.href},n.isDoNotTrack=function(){var e;return 1===(e=window.navigator&&void 0!==window.navigator.doNotTrack?window.navigator.doNotTrack:window.navigator&&void 0!==window.navigator.msDoNotTrack?window.navigator.msDoNotTrack:window.doNotTrack)||!0===e||"1"===e||"yes"===e};try{window.localStorage&&(n.localStorage={get:function(e){return new Promise((function(t){t(window.localStorage.getItem(e));}))},set:function(e,t){return new Promise((function(n){window.localStorage.setItem(e,t),n();}))},clear:function(e){return new Promise((function(t){window.localStorage.removeItem(e),t();}))}});}catch(e){n.localStorage=null;}if(e&&e.useReport&&"function"==typeof window.EventSourcePolyfill&&window.EventSourcePolyfill.supportedOptions&&window.EventSourcePolyfill.supportedOptions.method?(n.eventSourceAllowsReport=!0,o=window.EventSourcePolyfill):(n.eventSourceAllowsReport=!1,o=window.EventSource),window.EventSource){var a=3e5;n.eventSourceFactory=function(e,t){var n=Nt(Nt({},{heartbeatTimeout:a,silentTimeout:a,skipDefaultHeaders:!0}),t);return new o(e,n)},n.eventSourceIsActive=function(e){return e.readyState===window.EventSource.OPEN||e.readyState===window.EventSource.CONNECTING};}return n.userAgent="JSClient",n.version="3.8.1",n.diagnosticSdkData={name:"js-client-sdk",version:"3.8.1"},n.diagnosticPlatformData={name:"JS"},n.diagnosticUseCombinedEvent=!0,n}(n),o=Ut(e,t,n,r,Bt),i=o.client,a=o.options,s=o.emitter,c=new Promise((function(e){var t=s.on(Jt,(function(){s.off(Jt,t),e();}));}));i.waitUntilGoalsReady=function(){return c},a.fetchGoals?_t(o,(function(){return s.emit(Jt)})):s.emit(Jt),"complete"!==document.readyState?window.addEventListener("load",o.start):o.start();var u=function(){r.synchronousFlush=!0,i.flush().catch((function(){})),r.synchronousFlush=!1;};return document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&u();})),window.addEventListener("pagehide",u),i}

var commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};

function getDefaultExportFromCjs (x) {
	return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
}

/**
 * lodash (Custom Build) <https://lodash.com/>
 * Build: `lodash modularize exports="npm" -o ./`
 * Copyright jQuery Foundation and other contributors <https://jquery.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */

/** Used as references for various `Number` constants. */
var INFINITY = 1 / 0;

/** `Object#toString` result references. */
var symbolTag = '[object Symbol]';

/** Used to match words composed of alphanumeric characters. */
var reAsciiWord = /[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g;

/** Used to match Latin Unicode letters (excluding mathematical operators). */
var reLatin = /[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g;

/** Used to compose unicode character classes. */
var rsAstralRange = '\\ud800-\\udfff',
    rsComboMarksRange = '\\u0300-\\u036f\\ufe20-\\ufe23',
    rsComboSymbolsRange = '\\u20d0-\\u20f0',
    rsDingbatRange = '\\u2700-\\u27bf',
    rsLowerRange = 'a-z\\xdf-\\xf6\\xf8-\\xff',
    rsMathOpRange = '\\xac\\xb1\\xd7\\xf7',
    rsNonCharRange = '\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf',
    rsPunctuationRange = '\\u2000-\\u206f',
    rsSpaceRange = ' \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000',
    rsUpperRange = 'A-Z\\xc0-\\xd6\\xd8-\\xde',
    rsVarRange = '\\ufe0e\\ufe0f',
    rsBreakRange = rsMathOpRange + rsNonCharRange + rsPunctuationRange + rsSpaceRange;

/** Used to compose unicode capture groups. */
var rsApos = "['\u2019]",
    rsAstral = '[' + rsAstralRange + ']',
    rsBreak = '[' + rsBreakRange + ']',
    rsCombo = '[' + rsComboMarksRange + rsComboSymbolsRange + ']',
    rsDigits = '\\d+',
    rsDingbat = '[' + rsDingbatRange + ']',
    rsLower = '[' + rsLowerRange + ']',
    rsMisc = '[^' + rsAstralRange + rsBreakRange + rsDigits + rsDingbatRange + rsLowerRange + rsUpperRange + ']',
    rsFitz = '\\ud83c[\\udffb-\\udfff]',
    rsModifier = '(?:' + rsCombo + '|' + rsFitz + ')',
    rsNonAstral = '[^' + rsAstralRange + ']',
    rsRegional = '(?:\\ud83c[\\udde6-\\uddff]){2}',
    rsSurrPair = '[\\ud800-\\udbff][\\udc00-\\udfff]',
    rsUpper = '[' + rsUpperRange + ']',
    rsZWJ = '\\u200d';

/** Used to compose unicode regexes. */
var rsLowerMisc = '(?:' + rsLower + '|' + rsMisc + ')',
    rsUpperMisc = '(?:' + rsUpper + '|' + rsMisc + ')',
    rsOptLowerContr = '(?:' + rsApos + '(?:d|ll|m|re|s|t|ve))?',
    rsOptUpperContr = '(?:' + rsApos + '(?:D|LL|M|RE|S|T|VE))?',
    reOptMod = rsModifier + '?',
    rsOptVar = '[' + rsVarRange + ']?',
    rsOptJoin = '(?:' + rsZWJ + '(?:' + [rsNonAstral, rsRegional, rsSurrPair].join('|') + ')' + rsOptVar + reOptMod + ')*',
    rsSeq = rsOptVar + reOptMod + rsOptJoin,
    rsEmoji = '(?:' + [rsDingbat, rsRegional, rsSurrPair].join('|') + ')' + rsSeq,
    rsSymbol = '(?:' + [rsNonAstral + rsCombo + '?', rsCombo, rsRegional, rsSurrPair, rsAstral].join('|') + ')';

/** Used to match apostrophes. */
var reApos = RegExp(rsApos, 'g');

/**
 * Used to match [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks) and
 * [combining diacritical marks for symbols](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks_for_Symbols).
 */
var reComboMark = RegExp(rsCombo, 'g');

/** Used to match [string symbols](https://mathiasbynens.be/notes/javascript-unicode). */
var reUnicode = RegExp(rsFitz + '(?=' + rsFitz + ')|' + rsSymbol + rsSeq, 'g');

/** Used to match complex or compound words. */
var reUnicodeWord = RegExp([
  rsUpper + '?' + rsLower + '+' + rsOptLowerContr + '(?=' + [rsBreak, rsUpper, '$'].join('|') + ')',
  rsUpperMisc + '+' + rsOptUpperContr + '(?=' + [rsBreak, rsUpper + rsLowerMisc, '$'].join('|') + ')',
  rsUpper + '?' + rsLowerMisc + '+' + rsOptLowerContr,
  rsUpper + '+' + rsOptUpperContr,
  rsDigits,
  rsEmoji
].join('|'), 'g');

/** Used to detect strings with [zero-width joiners or code points from the astral planes](http://eev.ee/blog/2015/09/12/dark-corners-of-unicode/). */
var reHasUnicode = RegExp('[' + rsZWJ + rsAstralRange  + rsComboMarksRange + rsComboSymbolsRange + rsVarRange + ']');

/** Used to detect strings that need a more robust regexp to match words. */
var reHasUnicodeWord = /[a-z][A-Z]|[A-Z]{2,}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/;

/** Used to map Latin Unicode letters to basic Latin letters. */
var deburredLetters = {
  // Latin-1 Supplement block.
  '\xc0': 'A',  '\xc1': 'A', '\xc2': 'A', '\xc3': 'A', '\xc4': 'A', '\xc5': 'A',
  '\xe0': 'a',  '\xe1': 'a', '\xe2': 'a', '\xe3': 'a', '\xe4': 'a', '\xe5': 'a',
  '\xc7': 'C',  '\xe7': 'c',
  '\xd0': 'D',  '\xf0': 'd',
  '\xc8': 'E',  '\xc9': 'E', '\xca': 'E', '\xcb': 'E',
  '\xe8': 'e',  '\xe9': 'e', '\xea': 'e', '\xeb': 'e',
  '\xcc': 'I',  '\xcd': 'I', '\xce': 'I', '\xcf': 'I',
  '\xec': 'i',  '\xed': 'i', '\xee': 'i', '\xef': 'i',
  '\xd1': 'N',  '\xf1': 'n',
  '\xd2': 'O',  '\xd3': 'O', '\xd4': 'O', '\xd5': 'O', '\xd6': 'O', '\xd8': 'O',
  '\xf2': 'o',  '\xf3': 'o', '\xf4': 'o', '\xf5': 'o', '\xf6': 'o', '\xf8': 'o',
  '\xd9': 'U',  '\xda': 'U', '\xdb': 'U', '\xdc': 'U',
  '\xf9': 'u',  '\xfa': 'u', '\xfb': 'u', '\xfc': 'u',
  '\xdd': 'Y',  '\xfd': 'y', '\xff': 'y',
  '\xc6': 'Ae', '\xe6': 'ae',
  '\xde': 'Th', '\xfe': 'th',
  '\xdf': 'ss',
  // Latin Extended-A block.
  '\u0100': 'A',  '\u0102': 'A', '\u0104': 'A',
  '\u0101': 'a',  '\u0103': 'a', '\u0105': 'a',
  '\u0106': 'C',  '\u0108': 'C', '\u010a': 'C', '\u010c': 'C',
  '\u0107': 'c',  '\u0109': 'c', '\u010b': 'c', '\u010d': 'c',
  '\u010e': 'D',  '\u0110': 'D', '\u010f': 'd', '\u0111': 'd',
  '\u0112': 'E',  '\u0114': 'E', '\u0116': 'E', '\u0118': 'E', '\u011a': 'E',
  '\u0113': 'e',  '\u0115': 'e', '\u0117': 'e', '\u0119': 'e', '\u011b': 'e',
  '\u011c': 'G',  '\u011e': 'G', '\u0120': 'G', '\u0122': 'G',
  '\u011d': 'g',  '\u011f': 'g', '\u0121': 'g', '\u0123': 'g',
  '\u0124': 'H',  '\u0126': 'H', '\u0125': 'h', '\u0127': 'h',
  '\u0128': 'I',  '\u012a': 'I', '\u012c': 'I', '\u012e': 'I', '\u0130': 'I',
  '\u0129': 'i',  '\u012b': 'i', '\u012d': 'i', '\u012f': 'i', '\u0131': 'i',
  '\u0134': 'J',  '\u0135': 'j',
  '\u0136': 'K',  '\u0137': 'k', '\u0138': 'k',
  '\u0139': 'L',  '\u013b': 'L', '\u013d': 'L', '\u013f': 'L', '\u0141': 'L',
  '\u013a': 'l',  '\u013c': 'l', '\u013e': 'l', '\u0140': 'l', '\u0142': 'l',
  '\u0143': 'N',  '\u0145': 'N', '\u0147': 'N', '\u014a': 'N',
  '\u0144': 'n',  '\u0146': 'n', '\u0148': 'n', '\u014b': 'n',
  '\u014c': 'O',  '\u014e': 'O', '\u0150': 'O',
  '\u014d': 'o',  '\u014f': 'o', '\u0151': 'o',
  '\u0154': 'R',  '\u0156': 'R', '\u0158': 'R',
  '\u0155': 'r',  '\u0157': 'r', '\u0159': 'r',
  '\u015a': 'S',  '\u015c': 'S', '\u015e': 'S', '\u0160': 'S',
  '\u015b': 's',  '\u015d': 's', '\u015f': 's', '\u0161': 's',
  '\u0162': 'T',  '\u0164': 'T', '\u0166': 'T',
  '\u0163': 't',  '\u0165': 't', '\u0167': 't',
  '\u0168': 'U',  '\u016a': 'U', '\u016c': 'U', '\u016e': 'U', '\u0170': 'U', '\u0172': 'U',
  '\u0169': 'u',  '\u016b': 'u', '\u016d': 'u', '\u016f': 'u', '\u0171': 'u', '\u0173': 'u',
  '\u0174': 'W',  '\u0175': 'w',
  '\u0176': 'Y',  '\u0177': 'y', '\u0178': 'Y',
  '\u0179': 'Z',  '\u017b': 'Z', '\u017d': 'Z',
  '\u017a': 'z',  '\u017c': 'z', '\u017e': 'z',
  '\u0132': 'IJ', '\u0133': 'ij',
  '\u0152': 'Oe', '\u0153': 'oe',
  '\u0149': "'n", '\u017f': 'ss'
};

/** Detect free variable `global` from Node.js. */
var freeGlobal = typeof commonjsGlobal == 'object' && commonjsGlobal && commonjsGlobal.Object === Object && commonjsGlobal;

/** Detect free variable `self`. */
var freeSelf = typeof self == 'object' && self && self.Object === Object && self;

/** Used as a reference to the global object. */
var root = freeGlobal || freeSelf || Function('return this')();

/**
 * A specialized version of `_.reduce` for arrays without support for
 * iteratee shorthands.
 *
 * @private
 * @param {Array} [array] The array to iterate over.
 * @param {Function} iteratee The function invoked per iteration.
 * @param {*} [accumulator] The initial value.
 * @param {boolean} [initAccum] Specify using the first element of `array` as
 *  the initial value.
 * @returns {*} Returns the accumulated value.
 */
function arrayReduce(array, iteratee, accumulator, initAccum) {
  var index = -1,
      length = array ? array.length : 0;

  if (initAccum && length) {
    accumulator = array[++index];
  }
  while (++index < length) {
    accumulator = iteratee(accumulator, array[index], index, array);
  }
  return accumulator;
}

/**
 * Converts an ASCII `string` to an array.
 *
 * @private
 * @param {string} string The string to convert.
 * @returns {Array} Returns the converted array.
 */
function asciiToArray(string) {
  return string.split('');
}

/**
 * Splits an ASCII `string` into an array of its words.
 *
 * @private
 * @param {string} The string to inspect.
 * @returns {Array} Returns the words of `string`.
 */
function asciiWords(string) {
  return string.match(reAsciiWord) || [];
}

/**
 * The base implementation of `_.propertyOf` without support for deep paths.
 *
 * @private
 * @param {Object} object The object to query.
 * @returns {Function} Returns the new accessor function.
 */
function basePropertyOf(object) {
  return function(key) {
    return object == null ? undefined : object[key];
  };
}

/**
 * Used by `_.deburr` to convert Latin-1 Supplement and Latin Extended-A
 * letters to basic Latin letters.
 *
 * @private
 * @param {string} letter The matched letter to deburr.
 * @returns {string} Returns the deburred letter.
 */
var deburrLetter = basePropertyOf(deburredLetters);

/**
 * Checks if `string` contains Unicode symbols.
 *
 * @private
 * @param {string} string The string to inspect.
 * @returns {boolean} Returns `true` if a symbol is found, else `false`.
 */
function hasUnicode(string) {
  return reHasUnicode.test(string);
}

/**
 * Checks if `string` contains a word composed of Unicode symbols.
 *
 * @private
 * @param {string} string The string to inspect.
 * @returns {boolean} Returns `true` if a word is found, else `false`.
 */
function hasUnicodeWord(string) {
  return reHasUnicodeWord.test(string);
}

/**
 * Converts `string` to an array.
 *
 * @private
 * @param {string} string The string to convert.
 * @returns {Array} Returns the converted array.
 */
function stringToArray(string) {
  return hasUnicode(string)
    ? unicodeToArray(string)
    : asciiToArray(string);
}

/**
 * Converts a Unicode `string` to an array.
 *
 * @private
 * @param {string} string The string to convert.
 * @returns {Array} Returns the converted array.
 */
function unicodeToArray(string) {
  return string.match(reUnicode) || [];
}

/**
 * Splits a Unicode `string` into an array of its words.
 *
 * @private
 * @param {string} The string to inspect.
 * @returns {Array} Returns the words of `string`.
 */
function unicodeWords(string) {
  return string.match(reUnicodeWord) || [];
}

/** Used for built-in method references. */
var objectProto = Object.prototype;

/**
 * Used to resolve the
 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
 * of values.
 */
var objectToString = objectProto.toString;

/** Built-in value references. */
var Symbol$1 = root.Symbol;

/** Used to convert symbols to primitives and strings. */
var symbolProto = Symbol$1 ? Symbol$1.prototype : undefined,
    symbolToString = symbolProto ? symbolProto.toString : undefined;

/**
 * The base implementation of `_.slice` without an iteratee call guard.
 *
 * @private
 * @param {Array} array The array to slice.
 * @param {number} [start=0] The start position.
 * @param {number} [end=array.length] The end position.
 * @returns {Array} Returns the slice of `array`.
 */
function baseSlice(array, start, end) {
  var index = -1,
      length = array.length;

  if (start < 0) {
    start = -start > length ? 0 : (length + start);
  }
  end = end > length ? length : end;
  if (end < 0) {
    end += length;
  }
  length = start > end ? 0 : ((end - start) >>> 0);
  start >>>= 0;

  var result = Array(length);
  while (++index < length) {
    result[index] = array[index + start];
  }
  return result;
}

/**
 * The base implementation of `_.toString` which doesn't convert nullish
 * values to empty strings.
 *
 * @private
 * @param {*} value The value to process.
 * @returns {string} Returns the string.
 */
function baseToString(value) {
  // Exit early for strings to avoid a performance hit in some environments.
  if (typeof value == 'string') {
    return value;
  }
  if (isSymbol(value)) {
    return symbolToString ? symbolToString.call(value) : '';
  }
  var result = (value + '');
  return (result == '0' && (1 / value) == -INFINITY) ? '-0' : result;
}

/**
 * Casts `array` to a slice if it's needed.
 *
 * @private
 * @param {Array} array The array to inspect.
 * @param {number} start The start position.
 * @param {number} [end=array.length] The end position.
 * @returns {Array} Returns the cast slice.
 */
function castSlice(array, start, end) {
  var length = array.length;
  end = end === undefined ? length : end;
  return (!start && end >= length) ? array : baseSlice(array, start, end);
}

/**
 * Creates a function like `_.lowerFirst`.
 *
 * @private
 * @param {string} methodName The name of the `String` case method to use.
 * @returns {Function} Returns the new case function.
 */
function createCaseFirst(methodName) {
  return function(string) {
    string = toString(string);

    var strSymbols = hasUnicode(string)
      ? stringToArray(string)
      : undefined;

    var chr = strSymbols
      ? strSymbols[0]
      : string.charAt(0);

    var trailing = strSymbols
      ? castSlice(strSymbols, 1).join('')
      : string.slice(1);

    return chr[methodName]() + trailing;
  };
}

/**
 * Creates a function like `_.camelCase`.
 *
 * @private
 * @param {Function} callback The function to combine each word.
 * @returns {Function} Returns the new compounder function.
 */
function createCompounder(callback) {
  return function(string) {
    return arrayReduce(words(deburr(string).replace(reApos, '')), callback, '');
  };
}

/**
 * Checks if `value` is object-like. A value is object-like if it's not `null`
 * and has a `typeof` result of "object".
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
 * @example
 *
 * _.isObjectLike({});
 * // => true
 *
 * _.isObjectLike([1, 2, 3]);
 * // => true
 *
 * _.isObjectLike(_.noop);
 * // => false
 *
 * _.isObjectLike(null);
 * // => false
 */
function isObjectLike(value) {
  return !!value && typeof value == 'object';
}

/**
 * Checks if `value` is classified as a `Symbol` primitive or object.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to check.
 * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.
 * @example
 *
 * _.isSymbol(Symbol.iterator);
 * // => true
 *
 * _.isSymbol('abc');
 * // => false
 */
function isSymbol(value) {
  return typeof value == 'symbol' ||
    (isObjectLike(value) && objectToString.call(value) == symbolTag);
}

/**
 * Converts `value` to a string. An empty string is returned for `null`
 * and `undefined` values. The sign of `-0` is preserved.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category Lang
 * @param {*} value The value to process.
 * @returns {string} Returns the string.
 * @example
 *
 * _.toString(null);
 * // => ''
 *
 * _.toString(-0);
 * // => '-0'
 *
 * _.toString([1, 2, 3]);
 * // => '1,2,3'
 */
function toString(value) {
  return value == null ? '' : baseToString(value);
}

/**
 * Converts `string` to [camel case](https://en.wikipedia.org/wiki/CamelCase).
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category String
 * @param {string} [string=''] The string to convert.
 * @returns {string} Returns the camel cased string.
 * @example
 *
 * _.camelCase('Foo Bar');
 * // => 'fooBar'
 *
 * _.camelCase('--foo-bar--');
 * // => 'fooBar'
 *
 * _.camelCase('__FOO_BAR__');
 * // => 'fooBar'
 */
var camelCase = createCompounder(function(result, word, index) {
  word = word.toLowerCase();
  return result + (index ? capitalize(word) : word);
});

/**
 * Converts the first character of `string` to upper case and the remaining
 * to lower case.
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category String
 * @param {string} [string=''] The string to capitalize.
 * @returns {string} Returns the capitalized string.
 * @example
 *
 * _.capitalize('FRED');
 * // => 'Fred'
 */
function capitalize(string) {
  return upperFirst(toString(string).toLowerCase());
}

/**
 * Deburrs `string` by converting
 * [Latin-1 Supplement](https://en.wikipedia.org/wiki/Latin-1_Supplement_(Unicode_block)#Character_table)
 * and [Latin Extended-A](https://en.wikipedia.org/wiki/Latin_Extended-A)
 * letters to basic Latin letters and removing
 * [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks).
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category String
 * @param {string} [string=''] The string to deburr.
 * @returns {string} Returns the deburred string.
 * @example
 *
 * _.deburr('déjà vu');
 * // => 'deja vu'
 */
function deburr(string) {
  string = toString(string);
  return string && string.replace(reLatin, deburrLetter).replace(reComboMark, '');
}

/**
 * Converts the first character of `string` to upper case.
 *
 * @static
 * @memberOf _
 * @since 4.0.0
 * @category String
 * @param {string} [string=''] The string to convert.
 * @returns {string} Returns the converted string.
 * @example
 *
 * _.upperFirst('fred');
 * // => 'Fred'
 *
 * _.upperFirst('FRED');
 * // => 'FRED'
 */
var upperFirst = createCaseFirst('toUpperCase');

/**
 * Splits `string` into an array of its words.
 *
 * @static
 * @memberOf _
 * @since 3.0.0
 * @category String
 * @param {string} [string=''] The string to inspect.
 * @param {RegExp|string} [pattern] The pattern to match words.
 * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.
 * @returns {Array} Returns the words of `string`.
 * @example
 *
 * _.words('fred, barney, & pebbles');
 * // => ['fred', 'barney', 'pebbles']
 *
 * _.words('fred, barney, & pebbles', /[^, ]+/g);
 * // => ['fred', 'barney', '&', 'pebbles']
 */
function words(string, pattern, guard) {
  string = toString(string);
  pattern = guard ? undefined : pattern;

  if (pattern === undefined) {
    return hasUnicodeWord(string) ? unicodeWords(string) : asciiWords(string);
  }
  return string.match(pattern) || [];
}

var lodash_camelcase = camelCase;

var l = /*@__PURE__*/getDefaultExportFromCjs(lodash_camelcase);

var reactIs$1 = {exports: {}};

var reactIs_production_min = {};

/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var hasRequiredReactIs_production_min;

function requireReactIs_production_min () {
	if (hasRequiredReactIs_production_min) return reactIs_production_min;
	hasRequiredReactIs_production_min = 1;
var b="function"===typeof Symbol&&Symbol.for,c=b?Symbol.for("react.element"):60103,d=b?Symbol.for("react.portal"):60106,e=b?Symbol.for("react.fragment"):60107,f=b?Symbol.for("react.strict_mode"):60108,g=b?Symbol.for("react.profiler"):60114,h=b?Symbol.for("react.provider"):60109,k=b?Symbol.for("react.context"):60110,l=b?Symbol.for("react.async_mode"):60111,m=b?Symbol.for("react.concurrent_mode"):60111,n=b?Symbol.for("react.forward_ref"):60112,p=b?Symbol.for("react.suspense"):60113,q=b?
	Symbol.for("react.suspense_list"):60120,r=b?Symbol.for("react.memo"):60115,t=b?Symbol.for("react.lazy"):60116,v=b?Symbol.for("react.block"):60121,w=b?Symbol.for("react.fundamental"):60117,x=b?Symbol.for("react.responder"):60118,y=b?Symbol.for("react.scope"):60119;
	function z(a){if("object"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function A(a){return z(a)===m}reactIs_production_min.AsyncMode=l;reactIs_production_min.ConcurrentMode=m;reactIs_production_min.ContextConsumer=k;reactIs_production_min.ContextProvider=h;reactIs_production_min.Element=c;reactIs_production_min.ForwardRef=n;reactIs_production_min.Fragment=e;reactIs_production_min.Lazy=t;reactIs_production_min.Memo=r;reactIs_production_min.Portal=d;
	reactIs_production_min.Profiler=g;reactIs_production_min.StrictMode=f;reactIs_production_min.Suspense=p;reactIs_production_min.isAsyncMode=function(a){return A(a)||z(a)===l};reactIs_production_min.isConcurrentMode=A;reactIs_production_min.isContextConsumer=function(a){return z(a)===k};reactIs_production_min.isContextProvider=function(a){return z(a)===h};reactIs_production_min.isElement=function(a){return "object"===typeof a&&null!==a&&a.$$typeof===c};reactIs_production_min.isForwardRef=function(a){return z(a)===n};reactIs_production_min.isFragment=function(a){return z(a)===e};reactIs_production_min.isLazy=function(a){return z(a)===t};
	reactIs_production_min.isMemo=function(a){return z(a)===r};reactIs_production_min.isPortal=function(a){return z(a)===d};reactIs_production_min.isProfiler=function(a){return z(a)===g};reactIs_production_min.isStrictMode=function(a){return z(a)===f};reactIs_production_min.isSuspense=function(a){return z(a)===p};
	reactIs_production_min.isValidElementType=function(a){return "string"===typeof a||"function"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||"object"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===w||a.$$typeof===x||a.$$typeof===y||a.$$typeof===v)};reactIs_production_min.typeOf=z;
	return reactIs_production_min;
}

var reactIs_development = {};

/** @license React v16.13.1
 * react-is.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

var hasRequiredReactIs_development;

function requireReactIs_development () {
	if (hasRequiredReactIs_development) return reactIs_development;
	hasRequiredReactIs_development = 1;



	if (process.env.NODE_ENV !== "production") {
	  (function() {

	// The Symbol used to tag the ReactElement-like types. If there is no native Symbol
	// nor polyfill, then a plain number is used for performance.
	var hasSymbol = typeof Symbol === 'function' && Symbol.for;
	var REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;
	var REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;
	var REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;
	var REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;
	var REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;
	var REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;
	var REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary
	// (unstable) APIs that have been removed. Can we remove the symbols?

	var REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;
	var REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;
	var REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;
	var REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;
	var REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;
	var REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;
	var REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;
	var REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;
	var REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;
	var REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;
	var REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;

	function isValidElementType(type) {
	  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.
	  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);
	}

	function typeOf(object) {
	  if (typeof object === 'object' && object !== null) {
	    var $$typeof = object.$$typeof;

	    switch ($$typeof) {
	      case REACT_ELEMENT_TYPE:
	        var type = object.type;

	        switch (type) {
	          case REACT_ASYNC_MODE_TYPE:
	          case REACT_CONCURRENT_MODE_TYPE:
	          case REACT_FRAGMENT_TYPE:
	          case REACT_PROFILER_TYPE:
	          case REACT_STRICT_MODE_TYPE:
	          case REACT_SUSPENSE_TYPE:
	            return type;

	          default:
	            var $$typeofType = type && type.$$typeof;

	            switch ($$typeofType) {
	              case REACT_CONTEXT_TYPE:
	              case REACT_FORWARD_REF_TYPE:
	              case REACT_LAZY_TYPE:
	              case REACT_MEMO_TYPE:
	              case REACT_PROVIDER_TYPE:
	                return $$typeofType;

	              default:
	                return $$typeof;
	            }

	        }

	      case REACT_PORTAL_TYPE:
	        return $$typeof;
	    }
	  }

	  return undefined;
	} // AsyncMode is deprecated along with isAsyncMode

	var AsyncMode = REACT_ASYNC_MODE_TYPE;
	var ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;
	var ContextConsumer = REACT_CONTEXT_TYPE;
	var ContextProvider = REACT_PROVIDER_TYPE;
	var Element = REACT_ELEMENT_TYPE;
	var ForwardRef = REACT_FORWARD_REF_TYPE;
	var Fragment = REACT_FRAGMENT_TYPE;
	var Lazy = REACT_LAZY_TYPE;
	var Memo = REACT_MEMO_TYPE;
	var Portal = REACT_PORTAL_TYPE;
	var Profiler = REACT_PROFILER_TYPE;
	var StrictMode = REACT_STRICT_MODE_TYPE;
	var Suspense = REACT_SUSPENSE_TYPE;
	var hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated

	function isAsyncMode(object) {
	  {
	    if (!hasWarnedAboutDeprecatedIsAsyncMode) {
	      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint

	      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');
	    }
	  }

	  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;
	}
	function isConcurrentMode(object) {
	  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;
	}
	function isContextConsumer(object) {
	  return typeOf(object) === REACT_CONTEXT_TYPE;
	}
	function isContextProvider(object) {
	  return typeOf(object) === REACT_PROVIDER_TYPE;
	}
	function isElement(object) {
	  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;
	}
	function isForwardRef(object) {
	  return typeOf(object) === REACT_FORWARD_REF_TYPE;
	}
	function isFragment(object) {
	  return typeOf(object) === REACT_FRAGMENT_TYPE;
	}
	function isLazy(object) {
	  return typeOf(object) === REACT_LAZY_TYPE;
	}
	function isMemo(object) {
	  return typeOf(object) === REACT_MEMO_TYPE;
	}
	function isPortal(object) {
	  return typeOf(object) === REACT_PORTAL_TYPE;
	}
	function isProfiler(object) {
	  return typeOf(object) === REACT_PROFILER_TYPE;
	}
	function isStrictMode(object) {
	  return typeOf(object) === REACT_STRICT_MODE_TYPE;
	}
	function isSuspense(object) {
	  return typeOf(object) === REACT_SUSPENSE_TYPE;
	}

	reactIs_development.AsyncMode = AsyncMode;
	reactIs_development.ConcurrentMode = ConcurrentMode;
	reactIs_development.ContextConsumer = ContextConsumer;
	reactIs_development.ContextProvider = ContextProvider;
	reactIs_development.Element = Element;
	reactIs_development.ForwardRef = ForwardRef;
	reactIs_development.Fragment = Fragment;
	reactIs_development.Lazy = Lazy;
	reactIs_development.Memo = Memo;
	reactIs_development.Portal = Portal;
	reactIs_development.Profiler = Profiler;
	reactIs_development.StrictMode = StrictMode;
	reactIs_development.Suspense = Suspense;
	reactIs_development.isAsyncMode = isAsyncMode;
	reactIs_development.isConcurrentMode = isConcurrentMode;
	reactIs_development.isContextConsumer = isContextConsumer;
	reactIs_development.isContextProvider = isContextProvider;
	reactIs_development.isElement = isElement;
	reactIs_development.isForwardRef = isForwardRef;
	reactIs_development.isFragment = isFragment;
	reactIs_development.isLazy = isLazy;
	reactIs_development.isMemo = isMemo;
	reactIs_development.isPortal = isPortal;
	reactIs_development.isProfiler = isProfiler;
	reactIs_development.isStrictMode = isStrictMode;
	reactIs_development.isSuspense = isSuspense;
	reactIs_development.isValidElementType = isValidElementType;
	reactIs_development.typeOf = typeOf;
	  })();
	}
	return reactIs_development;
}

if (process.env.NODE_ENV === 'production') {
  reactIs$1.exports = requireReactIs_production_min();
} else {
  reactIs$1.exports = requireReactIs_development();
}

var reactIsExports = reactIs$1.exports;

var reactIs = reactIsExports;

/**
 * Copyright 2015, Yahoo! Inc.
 * Copyrights licensed under the New BSD License. See the accompanying LICENSE file for terms.
 */
var REACT_STATICS = {
  childContextTypes: true,
  contextType: true,
  contextTypes: true,
  defaultProps: true,
  displayName: true,
  getDefaultProps: true,
  getDerivedStateFromError: true,
  getDerivedStateFromProps: true,
  mixins: true,
  propTypes: true,
  type: true
};
var KNOWN_STATICS = {
  name: true,
  length: true,
  prototype: true,
  caller: true,
  callee: true,
  arguments: true,
  arity: true
};
var FORWARD_REF_STATICS = {
  '$$typeof': true,
  render: true,
  defaultProps: true,
  displayName: true,
  propTypes: true
};
var MEMO_STATICS = {
  '$$typeof': true,
  compare: true,
  defaultProps: true,
  displayName: true,
  propTypes: true,
  type: true
};
var TYPE_STATICS = {};
TYPE_STATICS[reactIs.ForwardRef] = FORWARD_REF_STATICS;
TYPE_STATICS[reactIs.Memo] = MEMO_STATICS;

function getStatics(component) {
  // React v16.11 and below
  if (reactIs.isMemo(component)) {
    return MEMO_STATICS;
  } // React v16.12 and above


  return TYPE_STATICS[component['$$typeof']] || REACT_STATICS;
}

var defineProperty = Object.defineProperty;
var getOwnPropertyNames = Object.getOwnPropertyNames;
var getOwnPropertySymbols = Object.getOwnPropertySymbols;
var getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
var getPrototypeOf = Object.getPrototypeOf;
var objectPrototype = Object.prototype;
function hoistNonReactStatics(targetComponent, sourceComponent, blacklist) {
  if (typeof sourceComponent !== 'string') {
    // don't hoist over string (html) components
    if (objectPrototype) {
      var inheritedComponent = getPrototypeOf(sourceComponent);

      if (inheritedComponent && inheritedComponent !== objectPrototype) {
        hoistNonReactStatics(targetComponent, inheritedComponent, blacklist);
      }
    }

    var keys = getOwnPropertyNames(sourceComponent);

    if (getOwnPropertySymbols) {
      keys = keys.concat(getOwnPropertySymbols(sourceComponent));
    }

    var targetStatics = getStatics(targetComponent);
    var sourceStatics = getStatics(sourceComponent);

    for (var i = 0; i < keys.length; ++i) {
      var key = keys[i];

      if (!KNOWN_STATICS[key] && !(blacklist && blacklist[key]) && !(sourceStatics && sourceStatics[key]) && !(targetStatics && targetStatics[key])) {
        var descriptor = getOwnPropertyDescriptor(sourceComponent, key);

        try {
          // Avoid failures from read-only properties
          defineProperty(targetComponent, key, descriptor);
        } catch (e) {}
      }
    }
  }

  return targetComponent;
}

var hoistNonReactStatics_cjs = hoistNonReactStatics;

var c = /*@__PURE__*/getDefaultExportFromCjs(hoistNonReactStatics_cjs);

const p=()=>e$1.createContext({flags:{},flagKeyMap:{},ldClient:void 0}),u=p(),y={useCamelCaseFlagKeys:!0,sendEventsOnFlagRead:!0,reactContext:u},h=e=>{var t;return null!=(t=e.context)?t:e.user},g=e=>{const t={};for(const r in e)0!==r.indexOf("$")&&(t[l(r)]=e[r]);return t},O=(e,t)=>{const r={};for(const n in e)t&&void 0===t[n]||(r[n]=e[n].current);return r},b=(e,t)=>{const r=e.allFlags();return t?Object.keys(t).reduce(((e,n)=>(e[n]=Object.prototype.hasOwnProperty.call(r,n)?r[n]:t[n],e)),{}):r};function m(e,t,r=y,n){const o=function(e,t){if(void 0===t)return e;return Object.keys(t).reduce(((t,r)=>(v(e,r)&&(t[r]=e[r]),t)),{})}(t,n),{useCamelCaseFlagKeys:a=!0}=r,[i,s={}]=a?function(e){const t={},r={};for(const n in e){if(0===n.indexOf("$"))continue;const o=l(n);t[o]=e[n],r[o]=n;}return [t,r]}(o):[o];return {flags:r.sendEventsOnFlagRead?C(e,i,s,a):i,flagKeyMap:s}}function v(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function C(e,t,r,n){return new Proxy(t,{get(t,o,a){const i=Reflect.get(t,o,a),s=n&&v(r,o)||v(t,o);if("symbol"==typeof o||!s)return i;if(void 0===i)return;const l=n?r[o]:o;return e.variation(l,i)}})}g.camelCaseKeys=g;const x={wrapperName:"react-client-sdk",wrapperVersion:"3.8.1",sendEventsOnlyForVariation:!0};var j=Object.defineProperty,w=Object.defineProperties,P=Object.getOwnPropertyDescriptors,F=Object.getOwnPropertySymbols,E=Object.prototype.hasOwnProperty,S=Object.prototype.propertyIsEnumerable,k=(e,t,r)=>t in e?j(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,D=(e,t)=>{for(var r in t||(t={}))E.call(t,r)&&k(e,r,t[r]);if(F)for(var r of F(t))S.call(t,r)&&k(e,r,t[r]);return e},I=(e,t)=>w(e,P(t)),K=(e,t,r)=>new Promise(((n,o)=>{var a=e=>{try{s(r.next(e));}catch(e){o(e);}},i=e=>{try{s(r.throw(e));}catch(e){o(e);}},s=e=>e.done?n(e.value):Promise.resolve(e.value).then(a,i);s((r=r.apply(e,t)).next());}));class R extends e$1.Component{constructor(e){super(e),this.getReactOptions=()=>D(D({},y),this.props.reactOptions),this.subscribeToChanges=e=>{const{flags:t}=this.props;e.on("change",(r=>{const n=this.getReactOptions(),o=O(r,t),a=D(D({},this.state.unproxiedFlags),o);Object.keys(o).length>0&&this.setState((r=>D(I(D({},r),{unproxiedFlags:a}),m(e,a,n,t))));}));},this.onFailed=(e,t)=>{this.setState((e=>I(D({},e),{error:t})));},this.onReady=(e,t,r)=>{const n=b(e,r);this.setState((o=>D(I(D({},o),{unproxiedFlags:n}),m(e,n,t,r))));},this.prepareLDClient=()=>K(this,null,(function*(){var e;const{clientSideID:t,flags:r,options:n}=this.props;let o=yield this.props.ldClient;const a=this.getReactOptions();let i,l=this.state.unproxiedFlags;if(o)l=b(o,r);else {const c=null!=(e=h(this.props))?e:{anonymous:!0,kind:"user"};o=Gt(t,c,D(D({},x),n));try{yield o.waitForInitialization(this.props.timeout),l=b(o,r);}catch(e){i=e,(null==i?void 0:i.name.toLowerCase().includes("timeout"))&&(o.on("failed",this.onFailed),o.on("ready",(()=>{this.onReady(o,a,r);})));}}this.setState((e=>I(D(I(D({},e),{unproxiedFlags:l}),m(o,l,a,r)),{ldClient:o,error:i}))),this.subscribeToChanges(o);}));const{options:t}=e;if(this.state={flags:{},unproxiedFlags:{},flagKeyMap:{}},t){const{bootstrap:e}=t;if(e&&"localStorage"!==e){const{useCamelCaseFlagKeys:t}=this.getReactOptions();this.state={flags:t?g(e):e,unproxiedFlags:e,flagKeyMap:{}};}}}componentDidMount(){return K(this,null,(function*(){const{deferInitialization:e}=this.props;e&&!h(this.props)||(yield this.prepareLDClient());}))}componentDidUpdate(e){return K(this,null,(function*(){const{deferInitialization:t}=this.props,r=!h(e)&&h(this.props);t&&r&&(yield this.prepareLDClient());}))}render(){const{flags:e,flagKeyMap:r,ldClient:n,error:o}=this.state,{reactContext:a}=this.getReactOptions();return e$1.createElement(a.Provider,{value:{flags:e,flagKeyMap:r,ldClient:n,error:o}},this.props.children)}}var M=Object.defineProperty,L=Object.defineProperties,z=Object.getOwnPropertyDescriptors,T=Object.getOwnPropertySymbols,V=Object.prototype.hasOwnProperty,$=Object.prototype.propertyIsEnumerable,N=(e,t,r)=>t in e?M(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,U=(e,t)=>{for(var r in t||(t={}))V.call(t,r)&&N(e,r,t[r]);if(T)for(var r of T(t))$.call(t,r)&&N(e,r,t[r]);return e};function q(t){return function(r){const{reactOptions:n}=t,o=U(U({},y),n),a=(i=U({},t),L(i,z({reactOptions:o})));var i;function s(t){return e__namespace.createElement(R,U({},a),e__namespace.createElement(r,U({},t)))}return c(s,r),s}}const ie=e=>{const{flags:t}=e$1.useContext(null!=e?e:y.reactContext);return t},se=e=>{const{ldClient:t}=e$1.useContext(null!=e?e:y.reactContext);return t};

/**
 * Error types that can occur
 */
exports.LaunchDarklyErrorType = void 0;
(function (LaunchDarklyErrorType) {
    LaunchDarklyErrorType["INITIALIZATION_ERROR"] = "INITIALIZATION_ERROR";
    LaunchDarklyErrorType["FLAG_EVALUATION_ERROR"] = "FLAG_EVALUATION_ERROR";
    LaunchDarklyErrorType["CONTEXT_UPDATE_ERROR"] = "CONTEXT_UPDATE_ERROR";
    LaunchDarklyErrorType["CLIENT_UNAVAILABLE"] = "CLIENT_UNAVAILABLE";
})(exports.LaunchDarklyErrorType || (exports.LaunchDarklyErrorType = {}));
/**
 * Custom error class for Launch Darkly operations
 */
var LaunchDarklyError = /** @class */ (function (_super) {
    __extends(LaunchDarklyError, _super);
    function LaunchDarklyError(type, message, originalError) {
        var _this = _super.call(this, message) || this;
        _this.name = 'LaunchDarklyError';
        _this.type = type;
        _this.originalError = originalError;
        return _this;
    }
    return LaunchDarklyError;
}(Error));

/**
 * Internal context for Launch Darkly state management
 */
var LaunchDarklyContext = e$1.createContext(null);
/**
 * Internal provider component that manages Launch Darkly state
 */
var LaunchDarklyProviderInternal = function (_a) {
    var children = _a.children, config = _a.config, fallback = _a.fallback;
    var client = se();
    var _b = e$1.useState(false), isInitialized = _b[0], setIsInitialized = _b[1];
    var _c = e$1.useState(true), loading = _c[0], setLoading = _c[1];
    var _d = e$1.useState(null), error = _d[0], setError = _d[1];
    var _e = e$1.useState(config.context), currentContext = _e[0], setCurrentContext = _e[1];
    e$1.useEffect(function () {
        if (!client) {
            setError(new LaunchDarklyError(exports.LaunchDarklyErrorType.CLIENT_UNAVAILABLE, "Launch Darkly client is not available"));
            setLoading(false);
            return;
        }
        var handleReady = function () {
            setIsInitialized(true);
            setLoading(false);
            setError(null);
        };
        var handleError = function (err) {
            setError(new LaunchDarklyError(exports.LaunchDarklyErrorType.INITIALIZATION_ERROR, "Failed to initialize Launch Darkly client", err));
            setLoading(false);
        };
        // Check if client is already ready
        if (client.initialized) {
            handleReady();
        }
        else {
            client.on("ready", handleReady);
            client.on("error", handleError);
        }
        return function () {
            client.off("ready", handleReady);
            client.off("error", handleError);
        };
    }, [client]);
    var updateContext = e$1.useCallback(function (newContext) { return __awaiter(void 0, void 0, void 0, function () {
        var err_1, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!client) {
                        throw new LaunchDarklyError(exports.LaunchDarklyErrorType.CLIENT_UNAVAILABLE, "Cannot update context: Launch Darkly client is not available");
                    }
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, , 4]);
                    return [4 /*yield*/, client.identify(newContext)];
                case 2:
                    _a.sent();
                    setCurrentContext(newContext);
                    return [3 /*break*/, 4];
                case 3:
                    err_1 = _a.sent();
                    error_1 = new LaunchDarklyError(exports.LaunchDarklyErrorType.CONTEXT_UPDATE_ERROR, "Failed to update user context", err_1);
                    setError(error_1);
                    throw error_1;
                case 4: return [2 /*return*/];
            }
        });
    }); }, [client]);
    var contextValue = {
        isInitialized: isInitialized,
        loading: loading,
        error: error,
        client: client,
        context: currentContext,
        updateContext: updateContext,
    };
    if (loading && fallback) {
        return jsxRuntime.jsx(jsxRuntime.Fragment, { children: fallback });
    }
    return (jsxRuntime.jsx(LaunchDarklyContext.Provider, { value: contextValue, children: children }));
};
/**
 * Main LaunchDarklyProvider component that wraps the application
 * with Launch Darkly functionality using the modern context API
 */
var LaunchDarklyProvider = function (_a) {
    var _b, _c, _d, _e, _f, _g, _h, _j;
    var children = _a.children, config = _a.config, fallback = _a.fallback;
    // Create the wrapped component with LDProvider
    var WrappedProvider = q({
        clientSideID: config.clientSideId,
        context: config.context,
        options: __assign({ streaming: (_c = (_b = config.options) === null || _b === void 0 ? void 0 : _b.streaming) !== null && _c !== void 0 ? _c : true, bootstrap: (_d = config.options) === null || _d === void 0 ? void 0 : _d.bootstrap, baseUri: (_e = config.options) === null || _e === void 0 ? void 0 : _e.baseUri, eventsUri: (_f = config.options) === null || _f === void 0 ? void 0 : _f.eventsUri, streamUri: (_g = config.options) === null || _g === void 0 ? void 0 : _g.streamUri, debug: (_j = (_h = config.options) === null || _h === void 0 ? void 0 : _h.debug) !== null && _j !== void 0 ? _j : false }, config.options),
    })(function () { return (jsxRuntime.jsx(LaunchDarklyProviderInternal, { config: config, fallback: fallback, children: children })); });
    return jsxRuntime.jsx(WrappedProvider, {});
};
/**
 * Hook to access the Launch Darkly context
 * @returns LaunchDarklyContextValue
 * @throws Error if used outside of LaunchDarklyProvider
 */
var useLaunchDarklyContext = function () {
    var context = e$1.useContext(LaunchDarklyContext);
    if (!context) {
        throw new Error("useLaunchDarklyContext must be used within a LaunchDarklyProvider");
    }
    return context;
};

/**
 * Custom hook for retrieving feature flag values with memoization and error handling
 *
 * @param config - Configuration object containing flag key, default value, and optional context
 * @returns FeatureFlagResult with current value, loading state, error, and utility functions
 *
 * @example
 * ```tsx
 * const { value, loading, error, isAvailable } = useFeatureFlag({
 *   flagKey: 'my-feature-flag',
 *   defaultValue: false
 * });
 *
 * if (loading) return <div>Loading...</div>;
 * if (error) return <div>Error: {error.message}</div>;
 *
 * return <div>Feature enabled: {value}</div>;
 * ```
 */
function useFeatureFlag(config) {
    var _this = this;
    var flagKey = config.flagKey, defaultValue = config.defaultValue, contextOverride = config.context;
    var _a = useLaunchDarklyContext(), isInitialized = _a.isInitialized, contextLoading = _a.loading, contextError = _a.error, client = _a.client;
    var flags = ie();
    var ldClient = se();
    var _b = e$1.useState(null), error = _b[0], setError = _b[1];
    var _c = e$1.useState(false), isRefreshing = _c[0], setIsRefreshing = _c[1];
    // Determine the effective client to use
    var effectiveClient = client || ldClient;
    // Memoize the flag value to prevent unnecessary re-renders
    var flagValue = e$1.useMemo(function () {
        try {
            if (!isInitialized || contextLoading) {
                return defaultValue;
            }
            if (contextError) {
                setError(contextError);
                return defaultValue;
            }
            // Try to get the flag value from the flags object first (most efficient)
            if (flags && flagKey in flags) {
                var value = flags[flagKey];
                setError(null);
                return value;
            }
            // Fallback to client variation method if available
            if (effectiveClient && effectiveClient.variation) {
                var value = effectiveClient.variation(flagKey, defaultValue, contextOverride);
                setError(null);
                return value;
            }
            // If no client is available, return default value
            if (!effectiveClient) {
                setError(new LaunchDarklyError(exports.LaunchDarklyErrorType.CLIENT_UNAVAILABLE, 'Launch Darkly client is not available'));
            }
            return defaultValue;
        }
        catch (err) {
            var flagError = new LaunchDarklyError(exports.LaunchDarklyErrorType.FLAG_EVALUATION_ERROR, "Failed to evaluate flag \"".concat(flagKey, "\""), err);
            setError(flagError);
            return defaultValue;
        }
    }, [
        flagKey,
        defaultValue,
        flags,
        isInitialized,
        contextLoading,
        contextError,
        effectiveClient,
        contextOverride
    ]);
    // Function to manually refresh the flag value
    var refresh = e$1.useCallback(function () { return __awaiter(_this, void 0, void 0, function () {
        var err_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    if (!effectiveClient) {
                        setError(new LaunchDarklyError(exports.LaunchDarklyErrorType.CLIENT_UNAVAILABLE, 'Cannot refresh flag: Launch Darkly client is not available'));
                        return [2 /*return*/];
                    }
                    setIsRefreshing(true);
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 4, 5, 6]);
                    if (!effectiveClient.flush) return [3 /*break*/, 3];
                    return [4 /*yield*/, effectiveClient.flush()];
                case 2:
                    _a.sent();
                    _a.label = 3;
                case 3:
                    setError(null);
                    return [3 /*break*/, 6];
                case 4:
                    err_1 = _a.sent();
                    setError(new LaunchDarklyError(exports.LaunchDarklyErrorType.FLAG_EVALUATION_ERROR, "Failed to refresh flag \"".concat(flagKey, "\""), err_1));
                    return [3 /*break*/, 6];
                case 5:
                    setIsRefreshing(false);
                    return [7 /*endfinally*/];
                case 6: return [2 /*return*/];
            }
        });
    }); }, [effectiveClient, flagKey]);
    // Set up real-time flag change listeners
    e$1.useEffect(function () {
        if (!effectiveClient || !isInitialized) {
            return;
        }
        var handleFlagChange = function (flagKey) {
            if (flagKey === config.flagKey) {
                // Flag value will be automatically updated through the flags object
                setError(null);
            }
        };
        // Listen for flag changes if the client supports it
        if (effectiveClient.on) {
            effectiveClient.on("update:".concat(flagKey), handleFlagChange);
        }
        return function () {
            if (effectiveClient.off) {
                effectiveClient.off("update:".concat(flagKey), handleFlagChange);
            }
        };
    }, [effectiveClient, isInitialized, flagKey, config.flagKey]);
    // Determine loading state
    var loading = contextLoading || isRefreshing;
    // Determine if Launch Darkly is available
    var isAvailable = isInitialized && !contextError && !!effectiveClient;
    // Combine errors
    var combinedError = error || contextError;
    return {
        value: flagValue,
        loading: loading,
        error: combinedError,
        isAvailable: isAvailable,
        refresh: refresh
    };
}
/**
 * Simplified hook for boolean feature flags
 *
 * @param flagKey - The feature flag key
 * @param defaultValue - Default boolean value (default: false)
 * @returns FeatureFlagResult<boolean>
 */
function useBooleanFeatureFlag(flagKey, defaultValue) {
    if (defaultValue === void 0) { defaultValue = false; }
    return useFeatureFlag({ flagKey: flagKey, defaultValue: defaultValue });
}
/**
 * Simplified hook for string feature flags
 *
 * @param flagKey - The feature flag key
 * @param defaultValue - Default string value (default: '')
 * @returns FeatureFlagResult<string>
 */
function useStringFeatureFlag(flagKey, defaultValue) {
    if (defaultValue === void 0) { defaultValue = ''; }
    return useFeatureFlag({ flagKey: flagKey, defaultValue: defaultValue });
}
/**
 * Simplified hook for number feature flags
 *
 * @param flagKey - The feature flag key
 * @param defaultValue - Default number value (default: 0)
 * @returns FeatureFlagResult<number>
 */
function useNumberFeatureFlag(flagKey, defaultValue) {
    if (defaultValue === void 0) { defaultValue = 0; }
    return useFeatureFlag({ flagKey: flagKey, defaultValue: defaultValue });
}

exports.LaunchDarklyError = LaunchDarklyError;
exports.LaunchDarklyProvider = LaunchDarklyProvider;
exports.useBooleanFeatureFlag = useBooleanFeatureFlag;
exports.useFeatureFlag = useFeatureFlag;
exports.useLaunchDarklyContext = useLaunchDarklyContext;
exports.useNumberFeatureFlag = useNumberFeatureFlag;
exports.useStringFeatureFlag = useStringFeatureFlag;
//# sourceMappingURL=index.js.map
