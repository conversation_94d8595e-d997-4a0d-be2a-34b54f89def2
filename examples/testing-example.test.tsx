/**
 * Testing Example: How to test components using Launch Darkly React Wrapper
 * 
 * This example shows various testing strategies for components that use
 * feature flags with the new Launch Darkly React wrapper.
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { jest } from '@jest/globals';
import {
  LaunchDarklyProvider,
  useBooleanFeatureFlag,
  useFeatureFlag,
  useLaunchDarklyContext
} from '@your-org/launchdarkly-react-wrapper';

// Mock the entire module
jest.mock('@your-org/launchdarkly-react-wrapper', () => ({
  LaunchDarklyProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useBooleanFeatureFlag: jest.fn(),
  useFeatureFlag: jest.fn(),
  useLaunchDarklyContext: jest.fn(),
}));

// Component to test
const FeatureComponent: React.FC = () => {
  const { value: isEnabled, loading, error } = useBooleanFeatureFlag('test-feature', false);
  const { value: theme } = useFeatureFlag<string>({
    flagKey: 'theme',
    defaultValue: 'light'
  });

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div data-theme={theme}>
      {isEnabled ? (
        <div>Feature is enabled</div>
      ) : (
        <div>Feature is disabled</div>
      )}
    </div>
  );
};

// Test utilities
const mockUseBooleanFeatureFlag = useBooleanFeatureFlag as jest.MockedFunction<typeof useBooleanFeatureFlag>;
const mockUseFeatureFlag = useFeatureFlag as jest.MockedFunction<typeof useFeatureFlag>;
const mockUseLaunchDarklyContext = useLaunchDarklyContext as jest.MockedFunction<typeof useLaunchDarklyContext>;

describe('FeatureComponent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Feature Flag States', () => {
    it('shows enabled state when feature flag is true', () => {
      mockUseBooleanFeatureFlag.mockReturnValue({
        value: true,
        loading: false,
        error: null,
        isAvailable: true,
        refresh: jest.fn()
      });

      mockUseFeatureFlag.mockReturnValue({
        value: 'dark',
        loading: false,
        error: null,
        isAvailable: true,
        refresh: jest.fn()
      });

      render(<FeatureComponent />);

      expect(screen.getByText('Feature is enabled')).toBeInTheDocument();
      expect(screen.getByTestId('theme')).toHaveAttribute('data-theme', 'dark');
    });

    it('shows disabled state when feature flag is false', () => {
      mockUseBooleanFeatureFlag.mockReturnValue({
        value: false,
        loading: false,
        error: null,
        isAvailable: true,
        refresh: jest.fn()
      });

      mockUseFeatureFlag.mockReturnValue({
        value: 'light',
        loading: false,
        error: null,
        isAvailable: true,
        refresh: jest.fn()
      });

      render(<FeatureComponent />);

      expect(screen.getByText('Feature is disabled')).toBeInTheDocument();
    });

    it('shows loading state', () => {
      mockUseBooleanFeatureFlag.mockReturnValue({
        value: false,
        loading: true,
        error: null,
        isAvailable: false,
        refresh: jest.fn()
      });

      mockUseFeatureFlag.mockReturnValue({
        value: 'light',
        loading: false,
        error: null,
        isAvailable: true,
        refresh: jest.fn()
      });

      render(<FeatureComponent />);

      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('shows error state', () => {
      const error = new Error('Feature flag error');
      
      mockUseBooleanFeatureFlag.mockReturnValue({
        value: false,
        loading: false,
        error,
        isAvailable: false,
        refresh: jest.fn()
      });

      mockUseFeatureFlag.mockReturnValue({
        value: 'light',
        loading: false,
        error: null,
        isAvailable: true,
        refresh: jest.fn()
      });

      render(<FeatureComponent />);

      expect(screen.getByText('Error: Feature flag error')).toBeInTheDocument();
    });
  });

  describe('Integration Testing with Provider', () => {
    const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
      const config = {
        clientSideId: 'test-client-id',
        context: {
          kind: 'user' as const,
          key: 'test-user'
        }
      };

      return (
        <LaunchDarklyProvider config={config}>
          {children}
        </LaunchDarklyProvider>
      );
    };

    it('works with provider wrapper', () => {
      mockUseBooleanFeatureFlag.mockReturnValue({
        value: true,
        loading: false,
        error: null,
        isAvailable: true,
        refresh: jest.fn()
      });

      mockUseFeatureFlag.mockReturnValue({
        value: 'dark',
        loading: false,
        error: null,
        isAvailable: true,
        refresh: jest.fn()
      });

      render(<FeatureComponent />, { wrapper: TestWrapper });

      expect(screen.getByText('Feature is enabled')).toBeInTheDocument();
    });
  });

  describe('Context Updates', () => {
    const ContextUpdateComponent: React.FC = () => {
      const { updateContext, context } = useLaunchDarklyContext();
      
      const handleUpdate = async () => {
        await updateContext({
          ...context,
          custom: { tier: 'premium' }
        });
      };

      return (
        <button onClick={handleUpdate}>
          Update Context
        </button>
      );
    };

    it('handles context updates', async () => {
      const mockUpdateContext = jest.fn().mockResolvedValue(undefined);
      
      mockUseLaunchDarklyContext.mockReturnValue({
        isInitialized: true,
        loading: false,
        error: null,
        client: {},
        context: { kind: 'user', key: 'test-user' },
        updateContext: mockUpdateContext
      });

      render(<ContextUpdateComponent />);

      fireEvent.click(screen.getByText('Update Context'));

      await waitFor(() => {
        expect(mockUpdateContext).toHaveBeenCalledWith({
          kind: 'user',
          key: 'test-user',
          custom: { tier: 'premium' }
        });
      });
    });

    it('handles context update errors', async () => {
      const mockUpdateContext = jest.fn().mockRejectedValue(new Error('Update failed'));
      
      mockUseLaunchDarklyContext.mockReturnValue({
        isInitialized: true,
        loading: false,
        error: null,
        client: {},
        context: { kind: 'user', key: 'test-user' },
        updateContext: mockUpdateContext
      });

      // Spy on console.error to verify error handling
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      render(<ContextUpdateComponent />);

      fireEvent.click(screen.getByText('Update Context'));

      await waitFor(() => {
        expect(mockUpdateContext).toHaveBeenCalled();
      });

      consoleSpy.mockRestore();
    });
  });

  describe('Refresh Functionality', () => {
    it('calls refresh function when requested', async () => {
      const mockRefresh = jest.fn().mockResolvedValue(undefined);
      
      mockUseBooleanFeatureFlag.mockReturnValue({
        value: false,
        loading: false,
        error: null,
        isAvailable: true,
        refresh: mockRefresh
      });

      const RefreshComponent: React.FC = () => {
        const { refresh } = useBooleanFeatureFlag('test-feature', false);
        
        return (
          <button onClick={() => refresh()}>
            Refresh Flag
          </button>
        );
      };

      render(<RefreshComponent />);

      fireEvent.click(screen.getByText('Refresh Flag'));

      await waitFor(() => {
        expect(mockRefresh).toHaveBeenCalled();
      });
    });
  });
});

// Custom testing utilities
export const createMockFeatureFlagResult = <T>(overrides: Partial<ReturnType<typeof useFeatureFlag<T>>> = {}) => ({
  value: overrides.value as T,
  loading: false,
  error: null,
  isAvailable: true,
  refresh: jest.fn(),
  ...overrides
});

export const createMockLaunchDarklyContext = (overrides: Partial<ReturnType<typeof useLaunchDarklyContext>> = {}) => ({
  isInitialized: true,
  loading: false,
  error: null,
  client: {},
  context: { kind: 'user' as const, key: 'test-user' },
  updateContext: jest.fn().mockResolvedValue(undefined),
  ...overrides
});

// Example usage of utilities
describe('Testing Utilities Example', () => {
  it('uses mock utilities', () => {
    const mockResult = createMockFeatureFlagResult<boolean>({
      value: true,
      loading: false
    });

    mockUseBooleanFeatureFlag.mockReturnValue(mockResult);

    render(<FeatureComponent />);

    expect(screen.getByText('Feature is enabled')).toBeInTheDocument();
  });
});
