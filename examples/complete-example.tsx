/**
 * Complete Example: Modern Launch Darkly Implementation
 * 
 * This example demonstrates a complete implementation using the new
 * Launch Darkly React wrapper with multiple feature flags, error handling,
 * and real-time updates.
 */

import React, { useState, useCallback } from 'react';
import {
  LaunchDarklyProvider,
  useFeatureFlag,
  useBooleanFeatureFlag,
  useStringFeatureFlag,
  useNumberFeatureFlag,
  useLaunchDarklyContext,
  LaunchDarklyConfig,
  LaunchDarklyError,
  LaunchDarklyErrorType
} from '@your-org/launchdarkly-react-wrapper';

// Main App Component with Provider
const App: React.FC = () => {
  const [currentUser, setCurrentUser] = useState({
    id: 'user-123',
    name: '<PERSON>',
    email: '<EMAIL>',
    tier: 'premium'
  });

  const config: LaunchDarklyConfig = {
    clientSideId: process.env.REACT_APP_LAUNCHDARKLY_CLIENT_ID || 'your-client-side-id',
    context: {
      kind: 'user',
      key: currentUser.id,
      name: currentUser.name,
      email: currentUser.email,
      custom: {
        tier: currentUser.tier,
        signupDate: '2023-01-15'
      }
    },
    options: {
      streaming: true,
      debug: process.env.NODE_ENV === 'development',
      bootstrap: {
        // Optional: Bootstrap with initial values for faster loading
        'new-dashboard': true,
        'theme': 'dark'
      }
    }
  };

  return (
    <LaunchDarklyProvider 
      config={config}
      fallback={
        <div style={{ padding: '20px', textAlign: 'center' }}>
          <div>🚀 Loading feature flags...</div>
          <div style={{ marginTop: '10px', fontSize: '14px', color: '#666' }}>
            Initializing Launch Darkly client
          </div>
        </div>
      }
    >
      <Dashboard user={currentUser} onUserChange={setCurrentUser} />
    </LaunchDarklyProvider>
  );
};

// Dashboard Component with Multiple Feature Flags
const Dashboard: React.FC<{
  user: any;
  onUserChange: (user: any) => void;
}> = ({ user, onUserChange }) => {
  // Boolean feature flag for new dashboard
  const { 
    value: isNewDashboard, 
    loading: dashboardLoading, 
    error: dashboardError 
  } = useBooleanFeatureFlag('new-dashboard', false);

  // String feature flag for theme
  const { value: theme } = useStringFeatureFlag('theme', 'light');

  // Number feature flag for max items
  const { value: maxItems } = useNumberFeatureFlag('max-dashboard-items', 10);

  // Complex feature flag with custom type
  const { 
    value: features, 
    loading: featuresLoading,
    error: featuresError,
    refresh: refreshFeatures
  } = useFeatureFlag<string[]>({
    flagKey: 'enabled-features',
    defaultValue: ['basic-features']
  });

  if (dashboardLoading || featuresLoading) {
    return <div>Loading dashboard configuration...</div>;
  }

  return (
    <div style={{ 
      minHeight: '100vh', 
      backgroundColor: theme === 'dark' ? '#1a1a1a' : '#ffffff',
      color: theme === 'dark' ? '#ffffff' : '#000000',
      padding: '20px'
    }}>
      <Header />
      
      <ErrorBoundary>
        {dashboardError && (
          <ErrorDisplay error={dashboardError} />
        )}
        
        {featuresError && (
          <ErrorDisplay error={featuresError} />
        )}
        
        {isNewDashboard ? (
          <NewDashboard 
            theme={theme}
            maxItems={maxItems}
            features={features}
            onRefreshFeatures={refreshFeatures}
          />
        ) : (
          <LegacyDashboard theme={theme} />
        )}
      </ErrorBoundary>
      
      <UserControls user={user} onUserChange={onUserChange} />
      <FeatureFlagDebugPanel />
    </div>
  );
};

// New Dashboard Component
const NewDashboard: React.FC<{
  theme: string;
  maxItems: number;
  features: string[];
  onRefreshFeatures: () => void;
}> = ({ theme, maxItems, features, onRefreshFeatures }) => {
  return (
    <div>
      <h1>🚀 New Dashboard (Enhanced)</h1>
      <p>Theme: {theme} | Max Items: {maxItems}</p>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Enabled Features:</h3>
        <ul>
          {features.slice(0, maxItems).map((feature, index) => (
            <li key={index}>{feature}</li>
          ))}
        </ul>
        <button onClick={onRefreshFeatures}>
          Refresh Features
        </button>
      </div>
      
      {features.includes('analytics') && <AnalyticsWidget />}
      {features.includes('notifications') && <NotificationsWidget />}
      {features.includes('advanced-search') && <AdvancedSearchWidget />}
    </div>
  );
};

// Legacy Dashboard Component
const LegacyDashboard: React.FC<{ theme: string }> = ({ theme }) => {
  return (
    <div>
      <h1>📊 Legacy Dashboard</h1>
      <p>Theme: {theme}</p>
      <p>Using the classic dashboard experience.</p>
    </div>
  );
};

// User Controls for Context Updates
const UserControls: React.FC<{
  user: any;
  onUserChange: (user: any) => void;
}> = ({ user, onUserChange }) => {
  const { updateContext, context } = useLaunchDarklyContext();
  
  const handleTierChange = useCallback(async (newTier: string) => {
    const updatedUser = { ...user, tier: newTier };
    onUserChange(updatedUser);
    
    try {
      await updateContext({
        ...context,
        custom: {
          ...context.custom,
          tier: newTier
        }
      });
    } catch (error) {
      console.error('Failed to update user context:', error);
    }
  }, [user, onUserChange, updateContext, context]);

  return (
    <div style={{ 
      marginTop: '40px', 
      padding: '20px', 
      border: '1px solid #ccc',
      borderRadius: '8px'
    }}>
      <h3>User Controls</h3>
      <div>
        <label>User Tier: </label>
        <select 
          value={user.tier} 
          onChange={(e) => handleTierChange(e.target.value)}
        >
          <option value="basic">Basic</option>
          <option value="premium">Premium</option>
          <option value="enterprise">Enterprise</option>
        </select>
      </div>
    </div>
  );
};

// Debug Panel for Feature Flags
const FeatureFlagDebugPanel: React.FC = () => {
  const { isInitialized, loading, error, client } = useLaunchDarklyContext();
  const [isExpanded, setIsExpanded] = useState(false);

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div style={{ 
      position: 'fixed', 
      bottom: '20px', 
      right: '20px',
      backgroundColor: '#f0f0f0',
      border: '1px solid #ccc',
      borderRadius: '8px',
      padding: '10px',
      maxWidth: '300px'
    }}>
      <button onClick={() => setIsExpanded(!isExpanded)}>
        🐛 Debug Panel {isExpanded ? '▼' : '▶'}
      </button>
      
      {isExpanded && (
        <div style={{ marginTop: '10px', fontSize: '12px' }}>
          <div>Initialized: {isInitialized ? '✅' : '❌'}</div>
          <div>Loading: {loading ? '⏳' : '✅'}</div>
          <div>Error: {error ? '❌' : '✅'}</div>
          <div>Client: {client ? '✅' : '❌'}</div>
        </div>
      )}
    </div>
  );
};

// Error Display Component
const ErrorDisplay: React.FC<{ error: Error }> = ({ error }) => {
  const isLaunchDarklyError = error instanceof LaunchDarklyError;
  
  return (
    <div style={{ 
      backgroundColor: '#fee', 
      border: '1px solid #fcc',
      borderRadius: '4px',
      padding: '10px',
      margin: '10px 0'
    }}>
      <h4>⚠️ Feature Flag Error</h4>
      <p><strong>Message:</strong> {error.message}</p>
      {isLaunchDarklyError && (
        <p><strong>Type:</strong> {(error as LaunchDarklyError).type}</p>
      )}
      <details>
        <summary>Stack Trace</summary>
        <pre style={{ fontSize: '10px', overflow: 'auto' }}>
          {error.stack}
        </pre>
      </details>
    </div>
  );
};

// Simple Error Boundary
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return (
        <div>
          <h2>Something went wrong.</h2>
          <ErrorDisplay error={this.state.error!} />
        </div>
      );
    }

    return this.props.children;
  }
}

// Widget Components
const Header: React.FC = () => (
  <header style={{ marginBottom: '30px' }}>
    <h1>Launch Darkly React Wrapper Demo</h1>
    <p>Demonstrating modern feature flag implementation</p>
  </header>
);

const AnalyticsWidget: React.FC = () => (
  <div style={{ padding: '10px', border: '1px solid #007acc', margin: '10px 0' }}>
    📊 Analytics Widget (Feature Flag Enabled)
  </div>
);

const NotificationsWidget: React.FC = () => (
  <div style={{ padding: '10px', border: '1px solid #28a745', margin: '10px 0' }}>
    🔔 Notifications Widget (Feature Flag Enabled)
  </div>
);

const AdvancedSearchWidget: React.FC = () => (
  <div style={{ padding: '10px', border: '1px solid #ffc107', margin: '10px 0' }}>
    🔍 Advanced Search Widget (Feature Flag Enabled)
  </div>
);

export default App;
