# Migration Example: From Legacy to Modern Implementation

This example shows how to migrate from the old window-based Launch Darkly implementation to the new reactive hook-based approach.

## Before: Legacy Implementation

### 1. App.tsx (Old)
```tsx
import React from 'react';
import { withLD<PERSON>rovider } from 'launchdarkly-react-client-sdk';

// Using deprecated user property
const App = withLDProvider({
  clientSideID: 'your-client-side-id',
  user: {  // ❌ DEPRECATED
    key: 'user-123',
    name: '<PERSON>',
    email: '<EMAIL>'
  }
})(({ children }) => {
  return <div>{children}</div>;
});

export default App;
```

### 2. AppRoutes.tsx (Old)
```tsx
import React, { useEffect } from 'react';
import { useLDClient } from 'launchdarkly-react-client-sdk';

const AppRoutes: React.FC = () => {
  const ldClient = useLDClient();

  useEffect(() => {
    // ❌ Setting client on window object
    if (ldClient) {
      (window as any).IdClient = ldClient;
    }
  }, [ldClient]);

  return (
    <div>
      {/* Your routes */}
    </div>
  );
};

export default AppRoutes;
```

### 3. oktaAuth.tsx (Old)
```tsx
import React, { useState, useEffect } from 'react';

const OktaAuth: React.FC = () => {
  const [isFeatureEnabled, setIsFeatureEnabled] = useState(false);

  useEffect(() => {
    // ❌ Reading from window object
    const checkFeatureFlag = () => {
      const client = (window as any).IdClient;
      if (client) {
        const flagValue = client.variation('okta-auth-enabled', false);
        setIsFeatureEnabled(flagValue);
      }
    };

    // ❌ Manual polling or event listening
    checkFeatureFlag();
    const interval = setInterval(checkFeatureFlag, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div>
      {isFeatureEnabled ? (
        <div>Okta Auth is enabled</div>
      ) : (
        <div>Okta Auth is disabled</div>
      )}
    </div>
  );
};

export default OktaAuth;
```

## After: Modern Implementation

### 1. App.tsx (New)
```tsx
import React from 'react';
import { LaunchDarklyProvider } from '@your-org/launchdarkly-react-wrapper';
import AppRoutes from './AppRoutes';

const App: React.FC = () => {
  const config = {
    clientSideId: 'your-client-side-id',
    context: {  // ✅ Modern context API
      kind: 'user',
      key: 'user-123',
      name: 'John Doe',
      email: '<EMAIL>'
    },
    options: {
      streaming: true,  // ✅ Real-time updates
      debug: process.env.NODE_ENV === 'development'
    }
  };

  return (
    <LaunchDarklyProvider 
      config={config}
      fallback={<div>Loading feature flags...</div>}  // ✅ Loading state
    >
      <AppRoutes />
    </LaunchDarklyProvider>
  );
};

export default App;
```

### 2. AppRoutes.tsx (New)
```tsx
import React from 'react';
import { Routes, Route } from 'react-router-dom';
import OktaAuth from './OktaAuth';
// ✅ No need to set window object or manage client manually

const AppRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/auth" element={<OktaAuth />} />
      {/* Your other routes */}
    </Routes>
  );
};

export default AppRoutes;
```

### 3. oktaAuth.tsx (New)
```tsx
import React from 'react';
import { useBooleanFeatureFlag } from '@your-org/launchdarkly-react-wrapper';

const OktaAuth: React.FC = () => {
  // ✅ Reactive hook with automatic updates
  const { 
    value: isFeatureEnabled, 
    loading, 
    error, 
    isAvailable 
  } = useBooleanFeatureFlag('okta-auth-enabled', false);

  // ✅ Handle loading state
  if (loading) {
    return <div>Loading authentication settings...</div>;
  }

  // ✅ Handle error state
  if (error) {
    console.error('Feature flag error:', error);
    // Fallback to default behavior
  }

  // ✅ Handle Launch Darkly unavailable
  if (!isAvailable) {
    console.warn('Launch Darkly unavailable, using default value');
  }

  return (
    <div>
      {isFeatureEnabled ? (
        <div>
          <h2>Okta Authentication</h2>
          <p>Enhanced authentication is enabled</p>
          {/* Okta auth components */}
        </div>
      ) : (
        <div>
          <h2>Standard Authentication</h2>
          <p>Using standard authentication</p>
          {/* Standard auth components */}
        </div>
      )}
    </div>
  );
};

export default OktaAuth;
```

## Advanced Example: Dynamic Context Updates

### User Context Management
```tsx
import React, { useCallback } from 'react';
import { useLaunchDarklyContext, useFeatureFlag } from '@your-org/launchdarkly-react-wrapper';

const UserProfile: React.FC = () => {
  const { updateContext, context } = useLaunchDarklyContext();
  
  // Feature flag that depends on user tier
  const { value: premiumFeatures } = useFeatureFlag<string[]>({
    flagKey: 'premium-features',
    defaultValue: []
  });

  const handleUserTierChange = useCallback(async (newTier: string) => {
    try {
      await updateContext({
        ...context,
        custom: {
          ...context.custom,
          tier: newTier
        }
      });
    } catch (error) {
      console.error('Failed to update user context:', error);
    }
  }, [context, updateContext]);

  return (
    <div>
      <h2>User Profile</h2>
      <select onChange={(e) => handleUserTierChange(e.target.value)}>
        <option value="basic">Basic</option>
        <option value="premium">Premium</option>
        <option value="enterprise">Enterprise</option>
      </select>
      
      <div>
        <h3>Available Features:</h3>
        <ul>
          {premiumFeatures.map(feature => (
            <li key={feature}>{feature}</li>
          ))}
        </ul>
      </div>
    </div>
  );
};
```

## Key Benefits of Migration

1. **No Global State Pollution**: No more window object dependencies
2. **Automatic Re-renders**: Components automatically update when flags change
3. **Better Error Handling**: Comprehensive error states and fallbacks
4. **Type Safety**: Full TypeScript support with proper types
5. **Performance**: Memoized values prevent unnecessary re-renders
6. **Testability**: Easy to mock and test without global dependencies
7. **Real-time Updates**: Automatic streaming updates without manual polling

## Testing Comparison

### Old Testing (Difficult)
```tsx
// ❌ Hard to test - requires mocking window object
describe('OktaAuth', () => {
  beforeEach(() => {
    (window as any).IdClient = {
      variation: jest.fn().mockReturnValue(true)
    };
  });
  
  // Complex setup required...
});
```

### New Testing (Easy)
```tsx
// ✅ Easy to test with React Testing Library
import { render } from '@testing-library/react';
import { LaunchDarklyProvider } from '@your-org/launchdarkly-react-wrapper';

const TestWrapper = ({ children, flagValue = false }) => (
  <LaunchDarklyProvider config={mockConfig}>
    {children}
  </LaunchDarklyProvider>
);

describe('OktaAuth', () => {
  it('shows enabled state when flag is true', () => {
    // Mock the hook return value
    jest.mock('@your-org/launchdarkly-react-wrapper', () => ({
      useBooleanFeatureFlag: () => ({ value: true, loading: false, error: null })
    }));
    
    render(<OktaAuth />, { wrapper: TestWrapper });
    // Test assertions...
  });
});
```
