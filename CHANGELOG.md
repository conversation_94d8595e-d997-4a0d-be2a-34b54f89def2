# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-07-09

### Added
- Initial release of Launch Darkly React Wrapper
- `LaunchDarklyProvider` component with modern context API support
- `useFeatureFlag` hook with memoization and real-time updates
- Convenience hooks: `useBooleanFeatureFlag`, `useStringFeatureFlag`, `useNumberFeatureFlag`
- `useLaunchDarklyContext` hook for advanced use cases
- Comprehensive TypeScript support with proper type definitions
- Error handling with custom `LaunchDarklyError` class
- Real-time flag updates with streaming support
- Migration guide from legacy window-based implementation
- Complete examples and testing utilities

### Features
- ✅ Modern React hooks and context API
- ✅ Real-time feature flag updates
- ✅ Memoized values to prevent unnecessary re-renders
- ✅ Comprehensive error handling with fallback values
- ✅ Full TypeScript support
- ✅ Easy migration from legacy implementations
- ✅ Performance optimized with tree-shaking support
- ✅ Extensive documentation and examples

### Dependencies
- `launchdarkly-react-client-sdk`: ^3.8.1
- `react`: >=16.8.0
- `react-dom`: >=16.8.0

### Breaking Changes
- This is the initial release, no breaking changes from previous versions
- Replaces legacy window-based Launch Darkly implementations
- Uses modern `context` API instead of deprecated `user` property
