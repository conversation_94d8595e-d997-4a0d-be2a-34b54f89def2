import { LD<PERSON>ser, <PERSON><PERSON><PERSON>Value, LDContext } from 'launchdarkly-react-client-sdk';

/**
 * Configuration options for the Launch Darkly provider
 */
export interface LaunchDarklyConfig {
  /** Launch Darkly client-side ID */
  clientSideId: string;
  /** User context for Launch Darkly */
  context: LDContext;
  /** Optional configuration options */
  options?: {
    /** Enable streaming updates (default: true) */
    streaming?: boolean;
    /** Bootstrap flag values */
    bootstrap?: Record<string, LDFlagValue>;
    /** Custom base URI */
    baseUri?: string;
    /** Custom events URI */
    eventsUri?: string;
    /** Custom stream URI */
    streamUri?: string;
    /** Enable debug mode */
    debug?: boolean;
  };
}

/**
 * Feature flag hook configuration
 */
export interface FeatureFlagConfig<T = LDFlagValue> {
  /** Feature flag key */
  flagKey: string;
  /** Default value to return if flag is unavailable */
  defaultValue: T;
  /** Optional user context override */
  context?: LDContext;
}

/**
 * Feature flag hook return value
 */
export interface FeatureFlagResult<T = LDFlagValue> {
  /** Current flag value */
  value: T;
  /** Whether the flag is loading */
  loading: boolean;
  /** Any error that occurred */
  error: Error | null;
  /** Whether Launch Darkly is available */
  isAvailable: boolean;
  /** Function to manually refresh the flag */
  refresh: () => void;
}

/**
 * Launch Darkly provider context value
 */
export interface LaunchDarklyContextValue {
  /** Whether the client is initialized */
  isInitialized: boolean;
  /** Whether the client is loading */
  loading: boolean;
  /** Any initialization error */
  error: Error | null;
  /** The Launch Darkly client instance */
  client: any; // LDClient type from SDK
  /** Current user context */
  context: LDContext;
  /** Function to update user context */
  updateContext: (newContext: LDContext) => void;
}

/**
 * Error types that can occur
 */
export enum LaunchDarklyErrorType {
  INITIALIZATION_ERROR = 'INITIALIZATION_ERROR',
  FLAG_EVALUATION_ERROR = 'FLAG_EVALUATION_ERROR',
  CONTEXT_UPDATE_ERROR = 'CONTEXT_UPDATE_ERROR',
  CLIENT_UNAVAILABLE = 'CLIENT_UNAVAILABLE'
}

/**
 * Custom error class for Launch Darkly operations
 */
export class LaunchDarklyError extends Error {
  public readonly type: LaunchDarklyErrorType;
  public readonly originalError?: Error;

  constructor(type: LaunchDarklyErrorType, message: string, originalError?: Error) {
    super(message);
    this.name = 'LaunchDarklyError';
    this.type = type;
    this.originalError = originalError;
  }
}

/**
 * @deprecated Use LDContext instead
 * Legacy user interface for backward compatibility
 */
export interface LegacyUser extends LDUser {
  // This maintains compatibility with the old user property
}
