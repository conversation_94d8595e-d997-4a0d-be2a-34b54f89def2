import {
  LDContext,
  useLDClient,
  withLD<PERSON><PERSON>ider,
} from "launchdarkly-react-client-sdk";
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import {
  LaunchDarklyConfig,
  LaunchDarklyContextValue,
  LaunchDarklyError,
  LaunchDarklyErrorType,
} from "./types";

/**
 * Internal context for Launch Darkly state management
 */
const LaunchDarklyContext = createContext<LaunchDarklyContextValue | null>(
  null
);

/**
 * Props for the LaunchDarklyProvider component
 */
interface LaunchDarklyProviderProps {
  children: ReactNode;
  config: LaunchDarklyConfig;
  fallback?: ReactNode;
}

/**
 * Internal provider component that manages Launch Darkly state
 */
const LaunchDarklyProviderInternal: React.FC<{
  children: ReactNode;
  config: LaunchDarklyConfig;
  fallback?: ReactNode;
}> = ({ children, config, fallback }) => {
  const client = useLDClient();
  const [isInitialized, setIsInitialized] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [currentContext, setCurrentContext] = useState<LDContext>(
    config.context
  );

  useEffect(() => {
    if (!client) {
      setError(
        new LaunchDarklyError(
          LaunchDarklyErrorType.CLIENT_UNAVAILABLE,
          "Launch Darkly client is not available"
        )
      );
      setLoading(false);
      return;
    }

    const handleReady = () => {
      setIsInitialized(true);
      setLoading(false);
      setError(null);
    };

    const handleError = (err: Error) => {
      setError(
        new LaunchDarklyError(
          LaunchDarklyErrorType.INITIALIZATION_ERROR,
          "Failed to initialize Launch Darkly client",
          err
        )
      );
      setLoading(false);
    };

    // Check if client is already ready
    if ((client as any).initialized) {
      handleReady();
    } else {
      client.on("ready", handleReady);
      client.on("error", handleError);
    }

    return () => {
      client.off("ready", handleReady);
      client.off("error", handleError);
    };
  }, [client]);

  const updateContext = useCallback(
    async (newContext: LDContext) => {
      if (!client) {
        throw new LaunchDarklyError(
          LaunchDarklyErrorType.CLIENT_UNAVAILABLE,
          "Cannot update context: Launch Darkly client is not available"
        );
      }

      try {
        await client.identify(newContext);
        setCurrentContext(newContext);
      } catch (err) {
        const error = new LaunchDarklyError(
          LaunchDarklyErrorType.CONTEXT_UPDATE_ERROR,
          "Failed to update user context",
          err as Error
        );
        setError(error);
        throw error;
      }
    },
    [client]
  );

  const contextValue: LaunchDarklyContextValue = {
    isInitialized,
    loading,
    error,
    client,
    context: currentContext,
    updateContext,
  };

  if (loading && fallback) {
    return <>{fallback}</>;
  }

  return (
    <LaunchDarklyContext.Provider value={contextValue}>
      {children}
    </LaunchDarklyContext.Provider>
  );
};

/**
 * Main LaunchDarklyProvider component that wraps the application
 * with Launch Darkly functionality using the modern context API
 */
export const LaunchDarklyProvider: React.FC<LaunchDarklyProviderProps> = ({
  children,
  config,
  fallback,
}) => {
  // Create the wrapped component with LDProvider
  const WrappedProvider = withLDProvider({
    clientSideID: config.clientSideId,
    context: config.context,
    options: {
      streaming: config.options?.streaming ?? true,
      bootstrap: config.options?.bootstrap,
      baseUri: config.options?.baseUri,
      eventsUri: config.options?.eventsUri,
      streamUri: config.options?.streamUri,
      debug: config.options?.debug ?? false,
      ...config.options,
    },
  })(() => (
    <LaunchDarklyProviderInternal config={config} fallback={fallback}>
      {children}
    </LaunchDarklyProviderInternal>
  ));

  return <WrappedProvider />;
};

/**
 * Hook to access the Launch Darkly context
 * @returns LaunchDarklyContextValue
 * @throws Error if used outside of LaunchDarklyProvider
 */
export const useLaunchDarklyContext = (): LaunchDarklyContextValue => {
  const context = useContext(LaunchDarklyContext);

  if (!context) {
    throw new Error(
      "useLaunchDarklyContext must be used within a LaunchDarklyProvider"
    );
  }

  return context;
};
