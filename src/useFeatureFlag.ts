import { useState, useEffect, useCallback, useMemo } from 'react';
import { useFlags, useLDClient, LDFlagValue } from 'launchdarkly-react-client-sdk';
import { 
  FeatureFlagConfig, 
  FeatureFlagResult, 
  LaunchDarklyError, 
  LaunchDarklyErrorType 
} from './types';
import { useLaunchDarklyContext } from './LaunchDarklyProvider';

/**
 * Custom hook for retrieving feature flag values with memoization and error handling
 * 
 * @param config - Configuration object containing flag key, default value, and optional context
 * @returns FeatureFlagResult with current value, loading state, error, and utility functions
 * 
 * @example
 * ```tsx
 * const { value, loading, error, isAvailable } = useFeatureFlag({
 *   flagKey: 'my-feature-flag',
 *   defaultValue: false
 * });
 * 
 * if (loading) return <div>Loading...</div>;
 * if (error) return <div>Error: {error.message}</div>;
 * 
 * return <div>Feature enabled: {value}</div>;
 * ```
 */
export function useFeatureFlag<T extends LDFlagValue = LDFlagValue>(
  config: FeatureFlagConfig<T>
): FeatureFlagResult<T> {
  const { flagKey, defaultValue, context: contextOverride } = config;
  const { isInitialized, loading: contextLoading, error: contextError, client } = useLaunchDarklyContext();
  const flags = useFlags();
  const ldClient = useLDClient();
  
  const [error, setError] = useState<Error | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Determine the effective client to use
  const effectiveClient = client || ldClient;

  // Memoize the flag value to prevent unnecessary re-renders
  const flagValue = useMemo(() => {
    try {
      if (!isInitialized || contextLoading) {
        return defaultValue;
      }

      if (contextError) {
        setError(contextError);
        return defaultValue;
      }

      // Try to get the flag value from the flags object first (most efficient)
      if (flags && flagKey in flags) {
        const value = flags[flagKey];
        setError(null);
        return value as T;
      }

      // Fallback to client variation method if available
      if (effectiveClient && effectiveClient.variation) {
        const value = effectiveClient.variation(flagKey, defaultValue, contextOverride);
        setError(null);
        return value as T;
      }

      // If no client is available, return default value
      if (!effectiveClient) {
        setError(new LaunchDarklyError(
          LaunchDarklyErrorType.CLIENT_UNAVAILABLE,
          'Launch Darkly client is not available'
        ));
      }

      return defaultValue;
    } catch (err) {
      const flagError = new LaunchDarklyError(
        LaunchDarklyErrorType.FLAG_EVALUATION_ERROR,
        `Failed to evaluate flag "${flagKey}"`,
        err as Error
      );
      setError(flagError);
      return defaultValue;
    }
  }, [
    flagKey, 
    defaultValue, 
    flags, 
    isInitialized, 
    contextLoading, 
    contextError, 
    effectiveClient, 
    contextOverride
  ]);

  // Function to manually refresh the flag value
  const refresh = useCallback(async () => {
    if (!effectiveClient) {
      setError(new LaunchDarklyError(
        LaunchDarklyErrorType.CLIENT_UNAVAILABLE,
        'Cannot refresh flag: Launch Darkly client is not available'
      ));
      return;
    }

    setIsRefreshing(true);
    try {
      // Force a flag evaluation refresh
      if (effectiveClient.flush) {
        await effectiveClient.flush();
      }
      setError(null);
    } catch (err) {
      setError(new LaunchDarklyError(
        LaunchDarklyErrorType.FLAG_EVALUATION_ERROR,
        `Failed to refresh flag "${flagKey}"`,
        err as Error
      ));
    } finally {
      setIsRefreshing(false);
    }
  }, [effectiveClient, flagKey]);

  // Set up real-time flag change listeners
  useEffect(() => {
    if (!effectiveClient || !isInitialized) {
      return;
    }

    const handleFlagChange = (flagKey: string) => {
      if (flagKey === config.flagKey) {
        // Flag value will be automatically updated through the flags object
        setError(null);
      }
    };

    // Listen for flag changes if the client supports it
    if (effectiveClient.on) {
      effectiveClient.on(`update:${flagKey}`, handleFlagChange);
    }

    return () => {
      if (effectiveClient.off) {
        effectiveClient.off(`update:${flagKey}`, handleFlagChange);
      }
    };
  }, [effectiveClient, isInitialized, flagKey, config.flagKey]);

  // Determine loading state
  const loading = contextLoading || isRefreshing;

  // Determine if Launch Darkly is available
  const isAvailable = isInitialized && !contextError && !!effectiveClient;

  // Combine errors
  const combinedError = error || contextError;

  return {
    value: flagValue,
    loading,
    error: combinedError,
    isAvailable,
    refresh
  };
}

/**
 * Simplified hook for boolean feature flags
 * 
 * @param flagKey - The feature flag key
 * @param defaultValue - Default boolean value (default: false)
 * @returns FeatureFlagResult<boolean>
 */
export function useBooleanFeatureFlag(
  flagKey: string, 
  defaultValue: boolean = false
): FeatureFlagResult<boolean> {
  return useFeatureFlag<boolean>({ flagKey, defaultValue });
}

/**
 * Simplified hook for string feature flags
 * 
 * @param flagKey - The feature flag key
 * @param defaultValue - Default string value (default: '')
 * @returns FeatureFlagResult<string>
 */
export function useStringFeatureFlag(
  flagKey: string, 
  defaultValue: string = ''
): FeatureFlagResult<string> {
  return useFeatureFlag<string>({ flagKey, defaultValue });
}

/**
 * Simplified hook for number feature flags
 * 
 * @param flagKey - The feature flag key
 * @param defaultValue - Default number value (default: 0)
 * @returns FeatureFlagResult<number>
 */
export function useNumberFeatureFlag(
  flagKey: string, 
  defaultValue: number = 0
): FeatureFlagResult<number> {
  return useFeatureFlag<number>({ flagKey, defaultValue });
}
