// Main exports
export { LaunchDarklyProvider, useLaunchDarklyContext } from './LaunchDarklyProvider';
export { 
  useFeatureFlag, 
  useBooleanFeatureFlag, 
  useStringFeatureFlag, 
  useNumberFeatureFlag 
} from './useFeatureFlag';

// Type exports
export type {
  LaunchDarklyConfig,
  FeatureFlagConfig,
  FeatureFlagResult,
  LaunchDarklyContextValue,
  LegacyUser
} from './types';

export {
  LaunchDarklyError,
  LaunchDarklyErrorType
} from './types';

// Re-export commonly used types from the Launch Darkly SDK
export type { 
  LDContext, 
  LDUser, 
  LDFlagValue 
} from 'launchdarkly-react-client-sdk';
