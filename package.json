{"name": "@your-org/launchdarkly-react-wrapper", "version": "1.0.0", "description": "A reusable React library for Launch Darkly feature flag interactions with modern hooks and context providers", "type": "module", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "rollup -c", "dev": "rollup -c -w", "test": "jest", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit"}, "keywords": ["launchdarkly", "feature-flags", "react", "hooks", "typescript"], "author": "", "license": "MIT", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@rollup/plugin-commonjs": "^25.0.8", "@rollup/plugin-node-resolve": "^15.3.1", "@rollup/plugin-typescript": "^11.1.6", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^8.57.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^4.6.2", "jest": "^29.7.0", "rollup": "^3.29.5", "rollup-plugin-peer-deps-external": "^2.2.4", "tslib": "^2.8.1", "typescript": "^5.8.3"}, "dependencies": {"launchdarkly-react-client-sdk": "^3.8.1"}}