{"name": "launchdarkly-js-sdk-common", "version": "5.7.1", "description": "LaunchDarkly SDK for JavaScript - common code", "author": "LaunchDarkly <<EMAIL>>", "license": "Apache-2.0", "types": "./typings.d.ts", "main": "src/index.js", "scripts": {"lint": "eslint --format 'node_modules/eslint-formatter-pretty' --ignore-path .eslintignore", "lint:all": "eslint --format 'node_modules/eslint-formatter-pretty' --ignore-path .eslintignore src", "lint-fix:all": "eslint --fix --format 'node_modules/eslint-formatter-pretty' --ignore-path .eslintignore src", "format": "npm run format:md && npm run format:js", "format:md": "prettier --parser markdown --ignore-path .prettierignore --write '*.md'", "format:js": "prettier --ignore-path .prettierignore --write 'src/**/*.js'", "format:test": "npm run format:test:md && npm run format:test:js", "format:test:md": "prettier --parser markdown --ignore-path .prettierignore --list-different '*.md'", "format:test:js": "prettier --ignore-path .prettierignore --list-different 'src/**/*.js'", "test": "cross-env NODE_ENV=test jest", "check-typescript": "tsc", "doc": "typedoc"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.6.4", "@babel/plugin-transform-regenerator": "^7.4.5", "@babel/plugin-transform-runtime": "^7.6.2", "@babel/preset-env": "^7.6.3", "@babel/runtime": "7.6.3", "@rollup/plugin-replace": "^2.2.0", "@types/jest": "^27.4.1", "@types/node": "12.12.6", "babel-eslint": "^10.1.0", "babel-jest": "^25.1.0", "cross-env": "^5.1.4", "eslint": "^6.8.0", "eslint-config-prettier": "^2.9.0", "eslint-config-xo": "^0.20.1", "eslint-formatter-pretty": "^1.3.0", "eslint-plugin-babel": "^5.0.0", "eslint-plugin-prettier": "^2.6.0", "jest": "^26.6.3", "jsdom": "^11.11.0", "launchdarkly-js-test-helpers": "1.1.0", "prettier": "1.19.1", "readline-sync": "^1.4.9", "typescript": "~5.4.5", "typedoc": "^0.25.13"}, "dependencies": {"base64-js": "^1.3.0", "fast-deep-equal": "^2.0.1", "uuid": "^8.0.0"}, "repository": {"type": "git", "url": "git://github.com/launchdarkly/js-sdk-common.git"}}