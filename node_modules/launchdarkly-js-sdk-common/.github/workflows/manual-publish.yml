name: Manual Publish Package
on:
  workflow_dispatch:
    inputs:
      dry-run:
        description: 'Is this a dry run. If so no package will be published.'
        type: boolean
        required: true
      prerelease:
          description: 'Is this a prerelease. If so, then the latest tag will not be updated in npm.'
          type: boolean
          required: true

jobs:
  publish-package:
    runs-on: ubuntu-latest
    permissions:
      id-token: write
      contents: write
    steps:
      - uses: actions/checkout@v4

      - uses: actions/setup-node@v4
        with:
          node-version: 20.x
          registry-url: 'https://registry.npmjs.org'

      - uses: launchdarkly/gh-actions/actions/release-secrets@release-secrets-v1.2.0
        name: 'Get NPM token'
        with:
          aws_assume_role: ${{ vars.AWS_ROLE_ARN }}
          ssm_parameter_pairs: '/production/common/releasing/npm/token = NODE_AUTH_TOKEN'

      - name: Install Dependencies
        run: npm install

      - id: publish-npm
        name: Publish NPM Package
        uses: ./.github/actions/publish-npm
        with:
          dry-run: ${{ inputs.dry-run }}
          prerelease: ${{ inputs.prerelease }}
