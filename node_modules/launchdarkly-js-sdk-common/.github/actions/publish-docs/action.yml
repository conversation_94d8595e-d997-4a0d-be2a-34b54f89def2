name: Publish Documentation
description: 'Publish documentation to github pages.'

inputs:
  github_token:
    description: 'The github token to use for committing'
    required: true

runs:
  using: composite
  steps:
    - uses: launchdarkly/gh-actions/actions/publish-pages@publish-pages-v1.0.2
      name: 'Publish to Github pages'
      with:
        docs_path: docs
        github_token: ${{ inputs.github_token }}
