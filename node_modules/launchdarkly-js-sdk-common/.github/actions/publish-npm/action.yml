name: Publish to NPM
description: Publish an npm package.
inputs:
  prerelease:
    description: 'Is this a prerelease. If so, then the latest tag will not be updated in npm.'
    required: false
  dry-run:
    description: 'Is this a dry run. If so no package will be published.'
    required: false

runs:
  using: composite
  steps:
    - name: Publish
      shell: bash
      run: |
        ./scripts/publish-npm.sh
      env:
        LD_RELEASE_IS_PRERELEASE: ${{ inputs.prerelease }}
        LD_RELEASE_IS_DRYRUN: ${{ inputs.dry-run }}
