{"name": "launchdarkly-js-client-sdk", "version": "3.8.1", "description": "LaunchDarkly SDK for JavaScript", "author": "LaunchDarkly <<EMAIL>>", "license": "Apache-2.0", "keywords": ["launchdarkly", "analytics", "client"], "files": ["ldclient.cjs.js", "ldclient.cjs.js.map", "ldclient.es.js", "ldclient.es.js.map", "ldclient.min.js", "ldclient.min.js.map", "typings.d.ts"], "types": "./typings.d.ts", "main": "dist/ldclient.cjs.js", "module": "dist/ldclient.es.js", "unpkg": "dist/ldclient.min.js", "jsdelivr": "dist/ldclient.min.js", "scripts": {"lint": "eslint --format 'node_modules/eslint-formatter-pretty' --ignore-path ./.eslintignore", "lint:all": "eslint --format 'node_modules/eslint-formatter-pretty' --ignore-path ./.eslintignore src", "format": "npm run format:md && npm run format:js", "format:md": "prettier --parser markdown --ignore-path ../../.prettierignore --write '*.md'", "format:js": "prettier --ignore-path ../../.prettierignore --write 'src/**/*.js'", "format:test": "npm run format:test:md && npm run format:test:js", "format:test:md": "prettier --parser markdown --ignore-path ../../.prettierignore --list-different '*.md'", "format:test:js": "prettier --ignore-path ../../.prettierignore --list-different 'src/**/*.js'", "build": "cross-env NODE_ENV=development rollup -c rollup.config.js", "build:min": "cross-env NODE_ENV=production rollup -c rollup.config.js", "test": "cross-env NODE_ENV=test jest", "test:junit": "cross-env NODE_ENV=test jest --testResultsProcessor jest-junit", "check-typescript": "node_modules/typescript/bin/tsc", "clean": "rimraf dist/**", "prepublishOnly": "npm run build:min", "doc": "typedoc"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.19.3", "@babel/eslint-parser": "7.19.1", "@babel/plugin-transform-regenerator": "7.18.6", "@babel/plugin-transform-runtime": "7.19.1", "@babel/preset-env": "^7.19.3", "@babel/runtime": "^7.19.0", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-replace": "^4.0.0", "babel-jest": "^29.1.0", "cross-env": "^7.0.3", "eslint": "^8.24.0", "eslint-config-prettier": "^8.5.0", "eslint-config-xo": "^0.42.0", "eslint-formatter-pretty": "^4.1.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.1.1", "jest-environment-jsdom": "^29.1.1", "jest-junit": "^14.0.1", "jest-localstorage-mock": "^2.4.22", "jsdom": "^20.0.0", "prettier": "2.7.1", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-filesize": "^9.1.2", "rollup-plugin-node-globals": "^1.4.0", "rollup-plugin-terser": "^7.0.2", "sinon": "^14.0.0", "typescript": "~5.4.5", "typedoc": "^0.25.13", "@types/estree": "^1.0.0"}, "dependencies": {"escape-string-regexp": "^4.0.0", "launchdarkly-js-sdk-common": "5.7.1"}, "repository": {"type": "git", "url": "git://github.com/launchdarkly/js-client-sdk.git"}, "overrides": {"parse5": "7.2.1"}}