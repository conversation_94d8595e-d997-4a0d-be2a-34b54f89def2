"use strict";function e(e){function t(e,t){Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.message=e,this.code=t}return t.prototype=new Error,t.prototype.name=e,t.prototype.constructor=t,t}Object.defineProperty(exports,"__esModule",{value:!0});const t=e("LaunchDarklyUnexpectedResponseError"),n=e("LaunchDarklyInvalidEnvironmentIdError"),r=e("LaunchDarklyInvalidUserError"),o=e("LaunchDarklyInvalidEventKeyError"),i=e("LaunchDarklyInvalidArgumentError"),a=e("LaunchDarklyFlagFetchError");for(var s={LDUnexpectedResponseError:t,LDInvalidEnvironmentIdError:n,LDInvalidUserError:r,LDInvalidEventKeyError:o,LDInvalidArgumentError:i,LDInvalidDataError:e("LaunchDarklyInvalidDataError"),LDFlagFetchError:a,LDTimeoutError:e("LaunchDarklyTimeoutError"),isHttpErrorRecoverable:function(e){return!(e>=400&&e<500)||(400===e||408===e||429===e)}},c=function(e){var t=m(e),n=t[0],r=t[1];return 3*(n+r)/4-r},u=function(e){var t,n,r=m(e),o=r[0],i=r[1],a=new g(function(e,t,n){return 3*(t+n)/4-n}(0,o,i)),s=0,c=i>0?o-4:o;for(n=0;n<c;n+=4)t=f[e.charCodeAt(n)]<<18|f[e.charCodeAt(n+1)]<<12|f[e.charCodeAt(n+2)]<<6|f[e.charCodeAt(n+3)],a[s++]=t>>16&255,a[s++]=t>>8&255,a[s++]=255&t;2===i&&(t=f[e.charCodeAt(n)]<<2|f[e.charCodeAt(n+1)]>>4,a[s++]=255&t);1===i&&(t=f[e.charCodeAt(n)]<<10|f[e.charCodeAt(n+1)]<<4|f[e.charCodeAt(n+2)]>>2,a[s++]=t>>8&255,a[s++]=255&t);return a},l=function(e){for(var t,n=e.length,r=n%3,o=[],i=16383,a=0,s=n-r;a<s;a+=i)o.push(h(e,a,a+i>s?s:a+i));1===r?(t=e[n-1],o.push(d[t>>2]+d[t<<4&63]+"==")):2===r&&(t=(e[n-2]<<8)+e[n-1],o.push(d[t>>10]+d[t>>4&63]+d[t<<2&63]+"="));return o.join("")},d=[],f=[],g="undefined"!=typeof Uint8Array?Uint8Array:Array,v="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",p=0;p<64;++p)d[p]=v[p],f[v.charCodeAt(p)]=p;function m(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function h(e,t,n){for(var r,o,i=[],a=t;a<n;a+=3)r=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),i.push(d[(o=r)>>18&63]+d[o>>12&63]+d[o>>6&63]+d[63&o]);return i.join("")}f["-".charCodeAt(0)]=62,f["_".charCodeAt(0)]=63;var y={byteLength:c,toByteArray:u,fromByteArray:l},w=Array.isArray,b=Object.keys,k=Object.prototype.hasOwnProperty,E=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){var r,o,i,a=w(t),s=w(n);if(a&&s){if((o=t.length)!=n.length)return!1;for(r=o;0!==r--;)if(!e(t[r],n[r]))return!1;return!0}if(a!=s)return!1;var c=t instanceof Date,u=n instanceof Date;if(c!=u)return!1;if(c&&u)return t.getTime()==n.getTime();var l=t instanceof RegExp,d=n instanceof RegExp;if(l!=d)return!1;if(l&&d)return t.toString()==n.toString();var f=b(t);if((o=f.length)!==b(n).length)return!1;for(r=o;0!==r--;)if(!k.call(n,f[r]))return!1;for(r=o;0!==r--;)if(!e(t[i=f[r]],n[i]))return!1;return!0}return t!=t&&n!=n};const D=["key","ip","country","email","firstName","lastName","avatar","name"];function x(e){const t=unescape(encodeURIComponent(e));return y.fromByteArray(function(e){const t=[];for(let n=0;n<e.length;n++)t.push(e.charCodeAt(n));return t}(t))}function C(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var P,S={appendUrlPath:function(e,t){return(e.endsWith("/")?e.substring(0,e.length-1):e)+(t.startsWith("/")?"":"/")+t},base64URLEncode:function(e){return x(e).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},btoa:x,clone:function(e){return JSON.parse(JSON.stringify(e))},deepEquals:function(e,t){return E(e,t)},extend:function(...e){return e.reduce(((e,t)=>({...e,...t})),{})},getLDUserAgentString:function(e){const t=e.version||"?";return e.userAgent+"/"+t},objectHasOwnProperty:C,onNextTick:function(e){setTimeout(e,0)},sanitizeContext:function(e){if(!e)return e;let t;return null!==e.kind&&void 0!==e.kind||D.forEach((n=>{const r=e[n];void 0!==r&&"string"!=typeof r&&(t=t||{...e},t[n]=String(r))})),t||e},transformValuesToVersionedValues:function(e){const t={};for(const n in e)C(e,n)&&(t[n]={value:e[n],version:0});return t},transformVersionedValuesToValues:function(e){const t={};for(const n in e)C(e,n)&&(t[n]=e[n].value);return t},wrapPromiseCallback:function(e,t){const n=e.then((e=>(t&&setTimeout((()=>{t(null,e)}),0),e)),(e=>{if(!t)return Promise.reject(e);setTimeout((()=>{t(e,null)}),0)}));return t?void 0:n},once:function(e){let t,n=!1;return function(...r){return n||(n=!0,t=e.apply(this,r)),t}}},I=new Uint8Array(16);function O(){if(!P&&!(P="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return P(I)}var T=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function L(e){return"string"==typeof e&&T.test(e)}for(var U,A,j=[],R=0;R<256;++R)j.push((R+256).toString(16).substr(1));function F(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(j[e[t+0]]+j[e[t+1]]+j[e[t+2]]+j[e[t+3]]+"-"+j[e[t+4]]+j[e[t+5]]+"-"+j[e[t+6]]+j[e[t+7]]+"-"+j[e[t+8]]+j[e[t+9]]+"-"+j[e[t+10]]+j[e[t+11]]+j[e[t+12]]+j[e[t+13]]+j[e[t+14]]+j[e[t+15]]).toLowerCase();if(!L(n))throw TypeError("Stringified UUID is invalid");return n}var N=0,$=0;function V(e){if(!L(e))throw TypeError("Invalid UUID");var t,n=new Uint8Array(16);return n[0]=(t=parseInt(e.slice(0,8),16))>>>24,n[1]=t>>>16&255,n[2]=t>>>8&255,n[3]=255&t,n[4]=(t=parseInt(e.slice(9,13),16))>>>8,n[5]=255&t,n[6]=(t=parseInt(e.slice(14,18),16))>>>8,n[7]=255&t,n[8]=(t=parseInt(e.slice(19,23),16))>>>8,n[9]=255&t,n[10]=(t=parseInt(e.slice(24,36),16))/1099511627776&255,n[11]=t/4294967296&255,n[12]=t>>>24&255,n[13]=t>>>16&255,n[14]=t>>>8&255,n[15]=255&t,n}function H(e,t,n){function r(e,r,o,i){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],n=0;n<e.length;++n)t.push(e.charCodeAt(n));return t}(e)),"string"==typeof r&&(r=V(r)),16!==r.length)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var a=new Uint8Array(16+e.length);if(a.set(r),a.set(e,r.length),(a=n(a))[6]=15&a[6]|t,a[8]=63&a[8]|128,o){i=i||0;for(var s=0;s<16;++s)o[i+s]=a[s];return o}return F(a)}try{r.name=e}catch(e){}return r.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",r.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",r}function M(e){return 14+(e+64>>>9<<4)+1}function q(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function z(e,t,n,r,o,i){return q((a=q(q(t,e),q(r,i)))<<(s=o)|a>>>32-s,n);var a,s}function K(e,t,n,r,o,i,a){return z(t&n|~t&r,e,t,o,i,a)}function _(e,t,n,r,o,i,a){return z(t&r|n&~r,e,t,o,i,a)}function J(e,t,n,r,o,i,a){return z(t^n^r,e,t,o,i,a)}function B(e,t,n,r,o,i,a){return z(n^(t|~r),e,t,o,i,a)}var G=H("v3",48,(function(e){if("string"==typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var n=0;n<t.length;++n)e[n]=t.charCodeAt(n)}return function(e){for(var t=[],n=32*e.length,r="0123456789abcdef",o=0;o<n;o+=8){var i=e[o>>5]>>>o%32&255,a=parseInt(r.charAt(i>>>4&15)+r.charAt(15&i),16);t.push(a)}return t}(function(e,t){e[t>>5]|=128<<t%32,e[M(t)-1]=t;for(var n=1732584193,r=-271733879,o=-1732584194,i=271733878,a=0;a<e.length;a+=16){var s=n,c=r,u=o,l=i;n=K(n,r,o,i,e[a],7,-680876936),i=K(i,n,r,o,e[a+1],12,-389564586),o=K(o,i,n,r,e[a+2],17,606105819),r=K(r,o,i,n,e[a+3],22,-1044525330),n=K(n,r,o,i,e[a+4],7,-176418897),i=K(i,n,r,o,e[a+5],12,1200080426),o=K(o,i,n,r,e[a+6],17,-1473231341),r=K(r,o,i,n,e[a+7],22,-45705983),n=K(n,r,o,i,e[a+8],7,1770035416),i=K(i,n,r,o,e[a+9],12,-1958414417),o=K(o,i,n,r,e[a+10],17,-42063),r=K(r,o,i,n,e[a+11],22,-1990404162),n=K(n,r,o,i,e[a+12],7,1804603682),i=K(i,n,r,o,e[a+13],12,-40341101),o=K(o,i,n,r,e[a+14],17,-1502002290),n=_(n,r=K(r,o,i,n,e[a+15],22,1236535329),o,i,e[a+1],5,-165796510),i=_(i,n,r,o,e[a+6],9,-1069501632),o=_(o,i,n,r,e[a+11],14,643717713),r=_(r,o,i,n,e[a],20,-373897302),n=_(n,r,o,i,e[a+5],5,-701558691),i=_(i,n,r,o,e[a+10],9,38016083),o=_(o,i,n,r,e[a+15],14,-660478335),r=_(r,o,i,n,e[a+4],20,-405537848),n=_(n,r,o,i,e[a+9],5,568446438),i=_(i,n,r,o,e[a+14],9,-1019803690),o=_(o,i,n,r,e[a+3],14,-187363961),r=_(r,o,i,n,e[a+8],20,1163531501),n=_(n,r,o,i,e[a+13],5,-1444681467),i=_(i,n,r,o,e[a+2],9,-51403784),o=_(o,i,n,r,e[a+7],14,1735328473),n=J(n,r=_(r,o,i,n,e[a+12],20,-1926607734),o,i,e[a+5],4,-378558),i=J(i,n,r,o,e[a+8],11,-2022574463),o=J(o,i,n,r,e[a+11],16,1839030562),r=J(r,o,i,n,e[a+14],23,-35309556),n=J(n,r,o,i,e[a+1],4,-1530992060),i=J(i,n,r,o,e[a+4],11,1272893353),o=J(o,i,n,r,e[a+7],16,-155497632),r=J(r,o,i,n,e[a+10],23,-1094730640),n=J(n,r,o,i,e[a+13],4,681279174),i=J(i,n,r,o,e[a],11,-358537222),o=J(o,i,n,r,e[a+3],16,-722521979),r=J(r,o,i,n,e[a+6],23,76029189),n=J(n,r,o,i,e[a+9],4,-640364487),i=J(i,n,r,o,e[a+12],11,-421815835),o=J(o,i,n,r,e[a+15],16,530742520),n=B(n,r=J(r,o,i,n,e[a+2],23,-995338651),o,i,e[a],6,-198630844),i=B(i,n,r,o,e[a+7],10,1126891415),o=B(o,i,n,r,e[a+14],15,-1416354905),r=B(r,o,i,n,e[a+5],21,-57434055),n=B(n,r,o,i,e[a+12],6,1700485571),i=B(i,n,r,o,e[a+3],10,-1894986606),o=B(o,i,n,r,e[a+10],15,-1051523),r=B(r,o,i,n,e[a+1],21,-2054922799),n=B(n,r,o,i,e[a+8],6,1873313359),i=B(i,n,r,o,e[a+15],10,-30611744),o=B(o,i,n,r,e[a+6],15,-1560198380),r=B(r,o,i,n,e[a+13],21,1309151649),n=B(n,r,o,i,e[a+4],6,-145523070),i=B(i,n,r,o,e[a+11],10,-1120210379),o=B(o,i,n,r,e[a+2],15,718787259),r=B(r,o,i,n,e[a+9],21,-343485551),n=q(n,s),r=q(r,c),o=q(o,u),i=q(i,l)}return[n,r,o,i]}(function(e){if(0===e.length)return[];for(var t=8*e.length,n=new Uint32Array(M(t)),r=0;r<t;r+=8)n[r>>5]|=(255&e[r/8])<<r%32;return n}(e),8*e.length))})),W=G;function X(e,t,n,r){switch(e){case 0:return t&n^~t&r;case 1:case 3:return t^n^r;case 2:return t&n^t&r^n&r}}function Q(e,t){return e<<t|e>>>32-t}var Y=H("v5",80,(function(e){var t=[1518500249,1859775393,2400959708,3395469782],n=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof e){var r=unescape(encodeURIComponent(e));e=[];for(var o=0;o<r.length;++o)e.push(r.charCodeAt(o))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var i=e.length/4+2,a=Math.ceil(i/16),s=new Array(a),c=0;c<a;++c){for(var u=new Uint32Array(16),l=0;l<16;++l)u[l]=e[64*c+4*l]<<24|e[64*c+4*l+1]<<16|e[64*c+4*l+2]<<8|e[64*c+4*l+3];s[c]=u}s[a-1][14]=8*(e.length-1)/Math.pow(2,32),s[a-1][14]=Math.floor(s[a-1][14]),s[a-1][15]=8*(e.length-1)&4294967295;for(var d=0;d<a;++d){for(var f=new Uint32Array(80),g=0;g<16;++g)f[g]=s[d][g];for(var v=16;v<80;++v)f[v]=Q(f[v-3]^f[v-8]^f[v-14]^f[v-16],1);for(var p=n[0],m=n[1],h=n[2],y=n[3],w=n[4],b=0;b<80;++b){var k=Math.floor(b/20),E=Q(p,5)+X(k,m,h,y)+w+t[k]+f[b]>>>0;w=y,y=h,h=Q(m,30)>>>0,m=p,p=E}n[0]=n[0]+p>>>0,n[1]=n[1]+m>>>0,n[2]=n[2]+h>>>0,n[3]=n[3]+y>>>0,n[4]=n[4]+w>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]})),Z=Y;var ee=Object.freeze({__proto__:null,v1:function(e,t,n){var r=t&&n||0,o=t||new Array(16),i=(e=e||{}).node||U,a=void 0!==e.clockseq?e.clockseq:A;if(null==i||null==a){var s=e.random||(e.rng||O)();null==i&&(i=U=[1|s[0],s[1],s[2],s[3],s[4],s[5]]),null==a&&(a=A=16383&(s[6]<<8|s[7]))}var c=void 0!==e.msecs?e.msecs:Date.now(),u=void 0!==e.nsecs?e.nsecs:$+1,l=c-N+(u-$)/1e4;if(l<0&&void 0===e.clockseq&&(a=a+1&16383),(l<0||c>N)&&void 0===e.nsecs&&(u=0),u>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");N=c,$=u,A=a;var d=(1e4*(268435455&(c+=122192928e5))+u)%4294967296;o[r++]=d>>>24&255,o[r++]=d>>>16&255,o[r++]=d>>>8&255,o[r++]=255&d;var f=c/4294967296*1e4&268435455;o[r++]=f>>>8&255,o[r++]=255&f,o[r++]=f>>>24&15|16,o[r++]=f>>>16&255,o[r++]=a>>>8|128,o[r++]=255&a;for(var g=0;g<6;++g)o[r+g]=i[g];return t||F(o)},v3:W,v4:function(e,t,n){var r=(e=e||{}).random||(e.rng||O)();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var o=0;o<16;++o)t[n+o]=r[o];return t}return F(r)},v5:Z,NIL:"00000000-0000-0000-0000-000000000000",version:function(e){if(!L(e))throw TypeError("Invalid UUID");return parseInt(e.substr(14,1),16)},validate:L,stringify:F,parse:V});const te=["debug","info","warn","error","none"];var ne={commonBasicLogger:function(e,t){if(e&&e.destination&&"function"!=typeof e.destination)throw new Error("destination for basicLogger was set to a non-function");function n(e){return function(t){console&&console[e]&&console[e].call(console,t)}}const r=e&&e.destination?[e.destination,e.destination,e.destination,e.destination]:[n("log"),n("info"),n("warn"),n("error")],o=!(!e||!e.destination),i=e&&void 0!==e.prefix&&null!==e.prefix?e.prefix:"[LaunchDarkly] ";let a=1;if(e&&e.level)for(let t=0;t<te.length;t++)te[t]===e.level&&(a=t);function s(e,n,a){if(a.length<1)return;let s;const c=o?n+": "+i:i;if(1!==a.length&&t){const e=[...a];e[0]=c+e[0],s=t(...e)}else s=c+a[0];try{r[e](s)}catch(e){console&&console.log&&console.log("[LaunchDarkly] Configured logger's "+n+" method threw an exception: "+e)}}const c={};for(let e=0;e<te.length;e++){const t=te[e];if("none"!==t)if(e<a)c[t]=()=>{};else{const n=e;c[t]=function(){s(n,t,arguments)}}}return c},validateLogger:function(e){te.forEach((t=>{if("none"!==t&&(!e[t]||"function"!=typeof e[t]))throw new Error("Provided logger instance must support logger."+t+"(...) method")}))}};function re(e){return e&&e.message?e.message:"string"==typeof e||e instanceof String?e:JSON.stringify(e)}const oe=" Please see https://docs.launchdarkly.com/sdk/client-side/javascript#initialize-the-client for instructions on SDK initialization.";var ie={bootstrapInvalid:function(){return"LaunchDarkly bootstrap data is not available because the back end could not read the flags."},bootstrapOldFormat:function(){return"LaunchDarkly client was initialized with bootstrap data that did not include flag metadata. Events may not be sent correctly."+oe},clientInitialized:function(){return"LaunchDarkly client initialized"},clientNotReady:function(){return"LaunchDarkly client is not ready"},debugEnqueueingEvent:function(e){return'enqueueing "'+e+'" event'},debugPostingDiagnosticEvent:function(e){return"sending diagnostic event ("+e.kind+")"},debugPostingEvents:function(e){return"sending "+e+" events"},debugStreamDelete:function(e){return'received streaming deletion for flag "'+e+'"'},debugStreamDeleteIgnored:function(e){return'received streaming deletion for flag "'+e+'" but ignored due to version check'},debugStreamPatch:function(e){return'received streaming update for flag "'+e+'"'},debugStreamPatchIgnored:function(e){return'received streaming update for flag "'+e+'" but ignored due to version check'},debugStreamPing:function(){return"received ping message from stream"},debugPolling:function(e){return"polling for feature flags at "+e},debugStreamPut:function(){return"received streaming update for all flags"},deprecated:function(e,t){return t?'"'+e+'" is deprecated, please use "'+t+'"':'"'+e+'" is deprecated'},environmentNotFound:function(){return"Environment not found. Double check that you specified a valid environment/client-side ID."+oe},environmentNotSpecified:function(){return"No environment/client-side ID was specified."+oe},errorFetchingFlags:function(e){return"Error fetching flag settings: "+re(e)},eventCapacityExceeded:function(){return"Exceeded event queue capacity. Increase capacity to avoid dropping events."},eventWithoutContext:function(){return"Be sure to call `identify` in the LaunchDarkly client: https://docs.launchdarkly.com/sdk/features/identify#javascript"},httpErrorMessage:function(e,t,n){return"Received error "+e+(401===e?" (invalid SDK key)":"")+" for "+t+" - "+(s.isHttpErrorRecoverable(e)?n:"giving up permanently")},httpUnavailable:function(){return"Cannot make HTTP requests in this environment."+oe},identifyDisabled:function(){return"identify() has no effect here; it must be called on the main client instance"},inspectorMethodError:(e,t)=>`an inspector: "${t}" of type: "${e}" generated an exception`,invalidContentType:function(e){return'Expected application/json content type but got "'+e+'"'},invalidData:function(){return"Invalid data received from LaunchDarkly; connection may have been interrupted"},invalidInspector:(e,t)=>`an inspector: "${t}" of an invalid type (${e}) was configured`,invalidKey:function(){return"Event key must be a string"},invalidMetricValue:e=>`The track function was called with a non-numeric "metricValue" (${e}), only numeric metric values are supported.`,invalidContext:function(){return"Invalid context specified."+oe},invalidTagValue:e=>`Config option "${e}" must only contain letters, numbers, ., _ or -.`,localStorageUnavailable:function(e){return"local storage is unavailable: "+re(e)},networkError:e=>"network error"+(e?" ("+e+")":""),optionBelowMinimum:(e,t,n)=>'Config option "'+e+'" was set to '+t+", changing to minimum value of "+n,streamClosing:function(){return"Closing stream connection"},streamConnecting:function(e){return"Opening stream connection to "+e},streamError:function(e,t){return"Error on stream connection: "+re(e)+", will continue retrying after "+t+" milliseconds."},tagValueTooLong:e=>`Value of "${e}" was longer than 64 characters and was discarded.`,unknownCustomEventKey:function(e){return'Custom event "'+e+'" does not exist'},unknownOption:e=>'Ignoring unknown config option "'+e+'"',contextNotSpecified:function(){return"No context specified."+oe},unrecoverableStreamError:e=>`Error on stream connection ${re(e)}, giving up permanently`,wrongOptionType:(e,t,n)=>'Config option "'+e+'" should be of type '+t+", got "+n+", using default value",wrongOptionTypeBoolean:(e,t)=>'Config option "'+e+'" should be a boolean, got '+t+", converting to boolean"};const{validateLogger:ae}=ne,se={baseUrl:{default:"https://app.launchdarkly.com"},streamUrl:{default:"https://clientstream.launchdarkly.com"},eventsUrl:{default:"https://events.launchdarkly.com"},sendEvents:{default:!0},streaming:{type:"boolean"},sendLDHeaders:{default:!0},requestHeaderTransform:{type:"function"},sendEventsOnlyForVariation:{default:!1},useReport:{default:!1},evaluationReasons:{default:!1},eventCapacity:{default:100,minimum:1},flushInterval:{default:2e3,minimum:2e3},samplingInterval:{default:0,minimum:0},streamReconnectDelay:{default:1e3,minimum:0},allAttributesPrivate:{default:!1},privateAttributes:{default:[]},bootstrap:{type:"string|object"},diagnosticRecordingInterval:{default:9e5,minimum:2e3},diagnosticOptOut:{default:!1},wrapperName:{type:"string"},wrapperVersion:{type:"string"},stateProvider:{type:"object"},application:{validator:function(e,t,n){const r={};t.id&&(r.id=le(`${e}.id`,t.id,n));t.version&&(r.version=le(`${e}.version`,t.version,n));return r}},inspectors:{default:[]},hooks:{default:[]},plugins:{default:[]}},ce=/^(\w|\.|-)+$/;function ue(e){return e&&e.replace(/\/+$/,"")}function le(e,t,n){if("string"==typeof t&&t.match(ce)){if(!(t.length>64))return t;n.warn(ie.tagValueTooLong(e))}else n.warn(ie.invalidTagValue(e))}var de={baseOptionDefs:se,validate:function(e,t,n,r){const o=S.extend({logger:{default:r}},se,n),i={};function a(e){S.onNextTick((()=>{t&&t.maybeReportError(new s.LDInvalidArgumentError(e))}))}let c=S.extend({},e||{});return function(e){const t=e;Object.keys(i).forEach((e=>{if(void 0!==t[e]){const n=i[e];r&&r.warn(ie.deprecated(e,n)),n&&(void 0===t[n]&&(t[n]=t[e]),delete t[e])}}))}(c),c=function(e){const t=S.extend({},e);return Object.keys(o).forEach((e=>{void 0!==t[e]&&null!==t[e]||(t[e]=o[e]&&o[e].default)})),t}(c),c=function(e){const t=S.extend({},e),n=e=>{if(null===e)return"any";if(void 0===e)return;if(Array.isArray(e))return"array";const t=typeof e;return"boolean"===t||"string"===t||"number"===t||"function"===t?t:"object"};return Object.keys(e).forEach((i=>{const s=e[i];if(null!=s){const c=o[i];if(void 0===c)a(ie.unknownOption(i));else{const o=c.type||n(c.default),u=c.validator;if(u){const n=u(i,e[i],r);void 0!==n?t[i]=n:delete t[i]}else if("any"!==o){const e=o.split("|"),r=n(s);e.indexOf(r)<0?"boolean"===o?(t[i]=!!s,a(ie.wrongOptionTypeBoolean(i,r))):(a(ie.wrongOptionType(i,o,r)),t[i]=c.default):"number"===r&&void 0!==c.minimum&&s<c.minimum&&(a(ie.optionBelowMinimum(i,s,c.minimum)),t[i]=c.minimum)}}}})),t.baseUrl=ue(t.baseUrl),t.streamUrl=ue(t.streamUrl),t.eventsUrl=ue(t.eventsUrl),t}(c),ae(c.logger),c},getTags:function(e){const t={};return e&&(e.application&&void 0!==e.application.id&&null!==e.application.id&&(t["application-id"]=[e.application.id]),e.application&&void 0!==e.application.version&&null!==e.application.id&&(t["application-version"]=[e.application.version])),t}};const{getLDUserAgentString:fe}=S;var ge={getLDHeaders:function(e,t){if(t&&!t.sendLDHeaders)return{};const n={};n[e.userAgentHeaderName||"User-Agent"]=fe(e),t&&t.wrapperName&&(n["X-LaunchDarkly-Wrapper"]=t.wrapperVersion?t.wrapperName+"/"+t.wrapperVersion:t.wrapperName);const r=de.getTags(t),o=Object.keys(r);return o.length&&(n["x-launchdarkly-tags"]=o.sort().map((e=>Array.isArray(r[e])?r[e].sort().map((t=>`${e}/${t}`)):[`${e}/${r[e]}`])).reduce(((e,t)=>e.concat(t)),[]).join(" ")),n},transformHeaders:function(e,t){return t&&t.requestHeaderTransform?t.requestHeaderTransform({...e}):e}};const{v1:ve}=ee,{getLDHeaders:pe,transformHeaders:me}=ge;var he=function(e,t,n){const r=S.extend({"Content-Type":"application/json"},pe(e,n)),o={};return o.sendEvents=(t,o,i)=>{if(!e.httpRequest)return Promise.resolve();const a=JSON.stringify(t),c=i?null:ve();return function t(u){const l=i?r:S.extend({},r,{"X-LaunchDarkly-Event-Schema":"4","X-LaunchDarkly-Payload-ID":c});return e.httpRequest("POST",o,me(l,n),a).promise.then((e=>{if(e)return e.status>=400&&s.isHttpErrorRecoverable(e.status)&&u?t(!1):function(e){const t={status:e.status},n=e.header("date");if(n){const e=Date.parse(n);e&&(t.serverTime=e)}return t}(e)})).catch((()=>u?t(!1):Promise.reject()))}(!0).catch((()=>{}))},o};var ye=function e(t,n=[]){if(null===t||"object"!=typeof t)return JSON.stringify(t);if(n.includes(t))throw new Error("Cycle detected");if(Array.isArray(t)){return`[${t.map((r=>e(r,[...n,t]))).map((e=>void 0===e?"null":e)).join(",")}]`}return`{${Object.keys(t).sort().map((r=>{const o=e(t[r],[...n,t]);if(void 0!==o)return`${JSON.stringify(r)}:${o}`})).filter((e=>void 0!==e)).join(",")}}`};const{commonBasicLogger:we}=ne;function be(e){return"string"==typeof e&&"kind"!==e&&e.match(/^(\w|\.|-)+$/)}function ke(e){return e.includes("%")||e.includes(":")?e.replace(/%/g,"%25").replace(/:/g,"%3A"):e}var Ee={checkContext:function(e,t){if(e){if(t&&(void 0===e.kind||null===e.kind))return void 0!==e.key&&null!==e.key;const n=e.key,r=void 0===e.kind?"user":e.kind,o=be(r),i="multi"===r||null!=n&&""!==n;if("multi"===r){const t=Object.keys(e).filter((e=>"kind"!==e));return i&&t.every((e=>be(e)))&&t.every((t=>{const n=e[t].key;return null!=n&&""!==n}))}return i&&o}return!1},getContextKeys:function(e,t=we()){if(!e)return;const n={},{kind:r,key:o}=e;switch(r){case void 0:n.user=`${o}`;break;case"multi":Object.entries(e).filter((([e])=>"kind"!==e)).forEach((([e,t])=>{t&&t.key&&(n[e]=t.key)}));break;case null:t.warn(`null is not a valid context kind: ${e}`);break;case"":t.warn(`'' is not a valid context kind: ${e}`);break;default:n[r]=`${o}`}return n},getContextKinds:function(e){return e?null===e.kind||void 0===e.kind?["user"]:"multi"!==e.kind?[e.kind]:Object.keys(e).filter((e=>"kind"!==e)):[]},getCanonicalKey:function(e){if(e){if((void 0===e.kind||null===e.kind||"user"===e.kind)&&e.key)return e.key;if("multi"!==e.kind&&e.key)return`${e.kind}:${ke(e.key)}`;if("multi"===e.kind)return Object.keys(e).sort().filter((e=>"kind"!==e)).map((t=>`${t}:${ke(e[t].key)}`)).join(":")}}};const{getContextKinds:De}=Ee;var xe=function(){const e={};let t=0,n=0,r={},o={};return e.summarizeEvent=e=>{if("feature"===e.kind){const i=e.key+":"+(null!==e.variation&&void 0!==e.variation?e.variation:"")+":"+(null!==e.version&&void 0!==e.version?e.version:""),a=r[i];let s=o[e.key];s||(s=new Set,o[e.key]=s),function(e){return e.context?De(e.context):e.contextKeys?Object.keys(e.contextKeys):[]}(e).forEach((e=>s.add(e))),a?a.count=a.count+1:r[i]={count:1,key:e.key,version:e.version,variation:e.variation,value:e.value,default:e.default},(0===t||e.creationDate<t)&&(t=e.creationDate),e.creationDate>n&&(n=e.creationDate)}},e.getSummary=()=>{const e={};let i=!0;for(const t of Object.values(r)){let n=e[t.key];n||(n={default:t.default,counters:[],contextKinds:[...o[t.key]]},e[t.key]=n);const r={value:t.value,count:t.count};void 0!==t.variation&&null!==t.variation&&(r.variation=t.variation),void 0!==t.version&&null!==t.version?r.version=t.version:r.unknown=!0,n.counters.push(r),i=!1}return i?null:{startDate:t,endDate:n,features:e,kind:"summary"}},e.clearSummary=()=>{t=0,n=0,r={},o={}},e};var Ce=function(e){let t={},n={};return{summarizeEvent:function(e){if("feature"===e.kind){const r=ye(e.context);if(!r)return;let o=t[r];o||(t[r]=xe(),o=t[r],n[r]=e.context),o.summarizeEvent(e)}},getSummaries:function(){const r=t,o=n;return t={},n={},Object.entries(r).map((([t,n])=>{const r=n.getSummary();return r.context=e.filter(o[t]),r}))}}};function Pe(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}function Se(e){return(e.startsWith("/")?e.substring(1):e).split("/").map((e=>e.indexOf("~")>=0?e.replace(/~1/g,"/").replace(/~0/g,"~"):e))}function Ie(e){return!e.startsWith("/")}function Oe(e,t){const n=Ie(e),r=Ie(t);if(n&&r)return e===t;if(n){const n=Se(t);return 1===n.length&&e===n[0]}if(r){const n=Se(e);return 1===n.length&&t===n[0]}return e===t}function Te(e){return`/${Pe(e)}`}var Le={cloneExcluding:function(e,t){const n=[],r={},o=[];for(n.push(...Object.keys(e).map((t=>({key:t,ptr:Te(t),source:e,parent:r,visited:[e]}))));n.length;){const e=n.pop();if(t.some((t=>Oe(t,e.ptr))))o.push(e.ptr);else{const t=e.source[e.key];if(null===t)e.parent[e.key]=t;else if(Array.isArray(t))e.parent[e.key]=[...t];else if("object"==typeof t){if(e.visited.includes(t))continue;e.parent[e.key]={},n.push(...Object.keys(t).map((n=>{return{key:n,ptr:(r=e.ptr,o=Pe(n),`${r}/${o}`),source:t,parent:e.parent[e.key],visited:[...e.visited,t]};var r,o})))}else e.parent[e.key]=t}}return{cloned:r,excluded:o.sort()}},compare:Oe,literalToReference:Te};var Ue=function(e){const t={},n=e.allAttributesPrivate,r=e.privateAttributes||[],o=["key","kind","_meta","anonymous"],i=["name","ip","firstName","lastName","email","avatar","country"],a=(e,t)=>{if("object"!=typeof e||null===e||Array.isArray(e))return;const{cloned:i,excluded:a}=Le.cloneExcluding(e,((e,t)=>(n||t&&e.anonymous?Object.keys(e):[...r,...e._meta&&e._meta.privateAttributes||[]]).filter((e=>!o.some((t=>Le.compare(e,t))))))(e,t));return i.key=String(i.key),a.length&&(i._meta||(i._meta={}),i._meta.redactedAttributes=a),i._meta&&(delete i._meta.privateAttributes,0===Object.keys(i._meta).length&&delete i._meta),void 0!==i.anonymous&&(i.anonymous=!!i.anonymous),i};return t.filter=(e,t=!1)=>void 0===e.kind||null===e.kind?a((e=>{const t={...e.custom||{},kind:"user",key:e.key};void 0!==e.anonymous&&(t.anonymous=!!e.anonymous);for(const n of i)delete t[n],void 0!==e[n]&&null!==e[n]&&(t[n]=String(e[n]));return void 0!==e.privateAttributeNames&&null!==e.privateAttributeNames&&(t._meta=t._meta||{},t._meta.privateAttributes=e.privateAttributeNames.map((e=>e.startsWith("/")?Le.literalToReference(e):e))),t})(e),t):"multi"===e.kind?((e,t)=>{const n={kind:e.kind},r=Object.keys(e);for(const o of r)if("kind"!==o){const r=a(e[o],t);r&&(n[o]=r)}return n})(e,t):a(e,t),t};const{getContextKeys:Ae}=Ee;var je=function(e,t,n,r=null,o=null,i=null){const a={},c=i||he(e,n,t),u=S.appendUrlPath(t.eventsUrl,"/events/bulk/"+n),l=Ue(t),d=Ce(l),f=t.samplingInterval,g=t.eventCapacity,v=t.flushInterval,p=t.logger;let m,h=[],y=0,w=!1,b=!1;function k(){return 0===f||0===Math.floor(Math.random()*f)}function E(e){const t=S.extend({},e);return"identify"===e.kind||"feature"===e.kind||"custom"===e.kind?t.context=l.filter(e.context):(t.contextKeys=Ae(e.context,p),delete t.context),"feature"===e.kind&&(delete t.trackEvents,delete t.debugEventsUntilDate),t}function D(e){h.length<g?(h.push(e),b=!1):(b||(b=!0,p.warn(ie.eventCapacityExceeded())),r&&r.incrementDroppedEvents())}return a.enqueue=function(e){if(w)return;let t=!1,n=!1;var r;if(d.summarizeEvent(e),"feature"===e.kind?k()&&(t=!!e.trackEvents,n=!!(r=e).debugEventsUntilDate&&r.debugEventsUntilDate>y&&r.debugEventsUntilDate>(new Date).getTime()):t=k(),t&&D(E(e)),n){const t=S.extend({},e,{kind:"debug"});t.context=l.filter(t.context),delete t.trackEvents,delete t.debugEventsUntilDate,D(t)}},a.flush=async function(){if(w)return Promise.resolve();const e=h;return d.getSummaries().forEach((t=>{Object.keys(t.features).length&&e.push(t)})),r&&r.setEventsInLastBatch(e.length),0===e.length?Promise.resolve():(h=[],p.debug(ie.debugPostingEvents(e.length)),c.sendEvents(e,u).then((e=>{e&&(e.serverTime&&(y=e.serverTime),s.isHttpErrorRecoverable(e.status)||(w=!0),e.status>=400&&S.onNextTick((()=>{o.maybeReportError(new s.LDUnexpectedResponseError(ie.httpErrorMessage(e.status,"event posting","some events were dropped")))})))})))},a.start=function(){const e=()=>{a.flush(),m=setTimeout(e,v)};m=setTimeout(e,v)},a.stop=function(){clearTimeout(m)},a};var Re=function(e){const t={},n={};return t.on=function(e,t,r){n[e]=n[e]||[],n[e]=n[e].concat({handler:t,context:r})},t.off=function(e,t,r){if(n[e])for(let o=0;o<n[e].length;o++)n[e][o].handler===t&&n[e][o].context===r&&(n[e]=n[e].slice(0,o).concat(n[e].slice(o+1)))},t.emit=function(e){if(!n[e])return;const t=n[e].slice(0);for(let e=0;e<t.length;e++)t[e].handler.apply(t[e].context,Array.prototype.slice.call(arguments,1))},t.getEvents=function(){return Object.keys(n)},t.getEventListenerCount=function(e){return n[e]?n[e].length:0},t.maybeReportError=function(t){t&&(n["error"]?this.emit("error",t):(e||console).error(t.message))},t};const Fe="ready",Ne="initialized",$e="failed";var Ve=function(e){let t=!1,n=!1,r=null,o=null;const i=new Promise((t=>{const n=()=>{e.off(Fe,n),t()};e.on(Fe,n)})).catch((()=>{}));return{getInitializationPromise:()=>o||(t?Promise.resolve():n?Promise.reject(r):(o=new Promise(((t,n)=>{const r=()=>{e.off(Ne,r),t()},o=t=>{e.off($e,o),n(t)};e.on(Ne,r),e.on($e,o)})),o)),getReadyPromise:()=>i,signalSuccess:()=>{t||n||(t=!0,e.emit(Ne),e.emit(Fe))},signalFailure:o=>{t||n||(n=!0,r=o,e.emit($e,o),e.emit(Fe)),e.maybeReportError(o)}}};var He=function(e,t,n,r){const o={};function i(){let e="";const o=r.getContext();return o&&(e=n||S.btoa(JSON.stringify(o))),"ld:"+t+":"+e}return o.loadFlags=()=>e.get(i()).then((e=>{if(null==e)return null;try{let t=JSON.parse(e);if(t){const e=t.$schema;void 0===e||e<1?t=S.transformValuesToVersionedValues(t):delete t.$schema}return t}catch(e){return o.clearFlags().then((()=>null))}})),o.saveFlags=t=>{const n=S.extend({},t,{$schema:1});return e.set(i(),JSON.stringify(n))},o.clearFlags=()=>e.clear(i()),o};var Me=function(e,t){const n={};let r=!1;const o=e=>{r||(r=!0,t.warn(ie.localStorageUnavailable(e)))};return n.isEnabled=()=>!!e,n.get=t=>new Promise((n=>{e?e.get(t).then(n).catch((e=>{o(e),n(void 0)})):n(void 0)})),n.set=(t,n)=>new Promise((r=>{e?e.set(t,n).then((()=>r(!0))).catch((e=>{o(e),r(!1)})):r(!1)})),n.clear=t=>new Promise((n=>{e?e.clear(t).then((()=>n(!0))).catch((e=>{o(e),n(!1)})):n(!1)})),n};const{appendUrlPath:qe,base64URLEncode:ze,objectHasOwnProperty:Ke}=S,{getLDHeaders:_e,transformHeaders:Je}=ge,{isHttpErrorRecoverable:Be}=s;var Ge=function(e,t,n,r){const o=t.streamUrl,i=t.logger,a={},s=qe(o,"/eval/"+n),c=t.useReport,u=t.evaluationReasons,l=t.streamReconnectDelay,d=_e(e,t);let f,g=!1,v=null,p=null,m=null,h=null,y=null,w=0;function b(){const e=(t=function(){const e=l*Math.pow(2,w);return e>3e4?3e4:e}(),t-Math.trunc(.5*Math.random()*t));var t;return w+=1,e}function k(e){if(e.status&&"number"==typeof e.status&&!Be(e.status))return x(),i.error(ie.unrecoverableStreamError(e)),void(p&&(clearTimeout(p),p=null));const t=b();g||(i.warn(ie.streamError(e,t)),g=!0),C(!1),x(),E(t)}function E(e){p||(e?p=setTimeout(D,e):D())}function D(){let r;p=null;let a="";const l={headers:d,readTimeoutMillis:3e5};if(e.eventSourceFactory){null!=h&&(a="h="+h),c?e.eventSourceAllowsReport?(r=s,l.method="REPORT",l.headers["Content-Type"]="application/json",l.body=JSON.stringify(m)):(r=qe(o,"/ping/"+n),a=""):r=s+"/"+ze(JSON.stringify(m)),l.headers=Je(l.headers,t),u&&(a=a+(a?"&":"")+"withReasons=true"),r=r+(a?"?":"")+a,x(),i.info(ie.streamConnecting(r)),f=(new Date).getTime(),v=e.eventSourceFactory(r,l);for(const e in y)Ke(y,e)&&v.addEventListener(e,y[e]);v.onerror=k,v.onopen=()=>{w=0}}}function x(){v&&(i.info(ie.streamClosing()),v.close(),v=null)}function C(e){f&&r&&r.recordStreamInit(f,!e,(new Date).getTime()-f),f=null}return a.connect=function(e,t,n){m=e,h=t,y={};for(const e in n||{})y[e]=function(t){g=!1,C(!0),n[e]&&n[e](t)};E()},a.disconnect=function(){clearTimeout(p),p=null,x()},a.isConnected=function(){return!!(v&&e.eventSourceIsActive&&e.eventSourceIsActive(v))},a};var We=function(e){let t,n,r,o;const i={addPromise:(i,a)=>{t=i,n&&n(),n=a,i.then((n=>{t===i&&(r(n),e&&e())}),(n=>{t===i&&(o(n),e&&e())}))}};return i.resultPromise=new Promise(((e,t)=>{r=e,o=t})),i};const{transformHeaders:Xe,getLDHeaders:Qe}=ge,Ye="application/json";var Ze=function(e,t,n){const r=t.baseUrl,o=t.useReport,i=t.evaluationReasons,a=t.logger,c={},u={};function l(n,r){if(!e.httpRequest)return new Promise(((e,t)=>{t(new s.LDFlagFetchError(ie.httpUnavailable()))}));const o=r?"REPORT":"GET",i=Qe(e,t);r&&(i["Content-Type"]=Ye);let a=u[n];a||(a=We((()=>{delete u[n]})),u[n]=a);const c=e.httpRequest(o,n,Xe(i,t),r),l=c.promise.then((e=>{if(200===e.status){if(e.header("content-type")&&e.header("content-type").substring(0,16)===Ye)return JSON.parse(e.body);{const t=ie.invalidContentType(e.header("content-type")||"");return Promise.reject(new s.LDFlagFetchError(t))}}return Promise.reject(function(e){return 404===e.status?new s.LDInvalidEnvironmentIdError(ie.environmentNotFound()):new s.LDFlagFetchError(ie.errorFetchingFlags(e.statusText||String(e.status)))}(e))}),(e=>Promise.reject(new s.LDFlagFetchError(ie.networkError(e)))));return a.addPromise(l,(()=>{c.cancel&&c.cancel()})),a.resultPromise}return c.fetchJSON=function(e){return l(S.appendUrlPath(r,e),null)},c.fetchFlagSettings=function(e,t){let s,c,u,d="";return o?(c=[r,"/sdk/evalx/",n,"/context"].join(""),u=JSON.stringify(e)):(s=S.base64URLEncode(JSON.stringify(e)),c=[r,"/sdk/evalx/",n,"/contexts/",s].join("")),t&&(d="h="+t),i&&(d=d+(d?"&":"")+"withReasons=true"),c=c+(d?"?":"")+d,a.debug(ie.debugPolling(c)),l(c,u)},c};var et=function(e,t){const n={};let r;return n.setContext=function(e){r=S.sanitizeContext(e),r&&t&&t(S.clone(r))},n.getContext=function(){return r?S.clone(r):null},e&&n.setContext(e),n};const{v1:tt}=ee,{getContextKinds:nt}=Ee;var rt=function(e){function t(e){return null==e||"user"===e?"ld:$anonUserId":`ld:$contextKey:${e}`}function n(n,r){return null!==r.key&&void 0!==r.key?(r.key=r.key.toString(),Promise.resolve(r)):r.anonymous?function(n){return e.get(t(n))}(n).then((o=>{if(o)return r.key=o,r;{const o=tt();return r.key=o,function(n,r){return e.set(t(r),n)}(o,n).then((()=>r))}})):Promise.reject(new s.LDInvalidUserError(ie.invalidContext()))}this.processContext=e=>{if(!e)return Promise.reject(new s.LDInvalidUserError(ie.contextNotSpecified()));const t=S.clone(e);if("multi"===e.kind){const e=nt(t);return Promise.all(e.map((e=>n(e,t[e])))).then((()=>t))}return n(e.kind,t)}};const{v1:ot}=ee,{baseOptionDefs:it}=de,{appendUrlPath:at}=S;var st={DiagnosticId:function(e){const t={diagnosticId:ot()};return e&&(t.sdkKeySuffix=e.length>6?e.substring(e.length-6):e),t},DiagnosticsAccumulator:function(e){let t,n,r,o;function i(e){t=e,n=0,r=0,o=[]}return i(e),{getProps:()=>({dataSinceDate:t,droppedEvents:n,eventsInLastBatch:r,streamInits:o}),setProps:e=>{t=e.dataSinceDate,n=e.droppedEvents||0,r=e.eventsInLastBatch||0,o=e.streamInits||[]},incrementDroppedEvents:()=>{n++},setEventsInLastBatch:e=>{r=e},recordStreamInit:(e,t,n)=>{const r={timestamp:e,failed:t,durationMillis:n};o.push(r)},reset:i}},DiagnosticsManager:function(e,t,n,r,o,i,a){const s=!!e.diagnosticUseCombinedEvent,c="ld:"+o+":$diagnostics",u=at(i.eventsUrl,"/events/diagnostic/"+o),l=i.diagnosticRecordingInterval,d=n;let f,g,v=!!i.streaming;const p={};function m(){return{sdk:w(),configuration:b(),platform:e.diagnosticPlatformData}}function h(e){i.logger&&i.logger.debug(ie.debugPostingDiagnosticEvent(e)),r.sendEvents(e,u,!0).then((()=>{})).catch((()=>{}))}function y(){h(function(){const e=(new Date).getTime();let t={kind:s?"diagnostic-combined":"diagnostic",id:a,creationDate:e,...d.getProps()};return s&&(t={...t,...m()}),d.reset(e),t}()),g=setTimeout(y,l),f=(new Date).getTime(),s&&function(){if(t.isEnabled()){const e={...d.getProps()};t.set(c,JSON.stringify(e))}}()}function w(){const t={...e.diagnosticSdkData};return i.wrapperName&&(t.wrapperName=i.wrapperName),i.wrapperVersion&&(t.wrapperVersion=i.wrapperVersion),t}function b(){return{customBaseURI:i.baseUrl!==it.baseUrl.default,customStreamURI:i.streamUrl!==it.streamUrl.default,customEventsURI:i.eventsUrl!==it.eventsUrl.default,eventsCapacity:i.eventCapacity,eventsFlushIntervalMillis:i.flushInterval,reconnectTimeMillis:i.streamReconnectDelay,streamingDisabled:!v,allAttributesPrivate:!!i.allAttributesPrivate,diagnosticRecordingIntervalMillis:i.diagnosticRecordingInterval,usingSecureMode:!!i.hash,bootstrapMode:!!i.bootstrap,fetchGoalsDisabled:!i.fetchGoals,sendEventsOnlyForVariation:!!i.sendEventsOnlyForVariation}}return p.start=()=>{s?function(e){if(!t.isEnabled())return e(!1);t.get(c).then((t=>{if(t)try{const e=JSON.parse(t);d.setProps(e),f=e.dataSinceDate}catch(e){}e(!0)})).catch((()=>{e(!1)}))}((e=>{if(e){const e=(f||0)+l,t=(new Date).getTime();t>=e?y():g=setTimeout(y,e-t)}else 0===Math.floor(4*Math.random())?y():g=setTimeout(y,l)})):(h({kind:"diagnostic-init",id:a,creationDate:d.getProps().dataSinceDate,...m()}),g=setTimeout(y,l))},p.stop=()=>{g&&clearTimeout(g)},p.setStreaming=e=>{v=e},p}};var ct=function(e,t){let n=!1;const r={type:e.type,name:e.name,synchronous:e.synchronous,method:(...o)=>{try{e.method(...o)}catch{n||(n=!0,t.warn(ie.inspectorMethodError(r.type,r.name)))}}};return r};const{onNextTick:ut}=S,lt={flagUsed:"flag-used",flagDetailsChanged:"flag-details-changed",flagDetailChanged:"flag-detail-changed",clientIdentityChanged:"client-identity-changed"};Object.freeze(lt);var dt={InspectorTypes:lt,InspectorManager:function(e,t){const n={},r={[lt.flagUsed]:[],[lt.flagDetailsChanged]:[],[lt.flagDetailChanged]:[],[lt.clientIdentityChanged]:[]},o={[lt.flagUsed]:[],[lt.flagDetailsChanged]:[],[lt.flagDetailChanged]:[],[lt.clientIdentityChanged]:[]},i=e&&e.map((e=>ct(e,t)));return i&&i.forEach((e=>{Object.prototype.hasOwnProperty.call(r,e.type)&&!e.synchronous?r[e.type].push(e):Object.prototype.hasOwnProperty.call(o,e.type)&&e.synchronous?o[e.type].push(e):t.warn(ie.invalidInspector(e.type,e.name))})),n.hasListeners=e=>r[e]&&r[e].length||o[e]&&o[e].length,n.onFlagUsed=(e,t,n)=>{const i=lt.flagUsed;o[i].length&&o[i].forEach((r=>r.method(e,t,n))),r[i].length&&ut((()=>{r[i].forEach((r=>r.method(e,t,n)))}))},n.onFlags=e=>{const t=lt.flagDetailsChanged;o[t].length&&o[t].forEach((t=>t.method(e))),r[t].length&&ut((()=>{r[t].forEach((t=>t.method(e)))}))},n.onFlagChanged=(e,t)=>{const n=lt.flagDetailChanged;o[n].length&&o[n].forEach((n=>n.method(e,t))),r[n].length&&ut((()=>{r[n].forEach((n=>n.method(e,t)))}))},n.onIdentityChanged=e=>{const t=lt.clientIdentityChanged;o[t].length&&o[t].forEach((t=>t.method(e))),r[t].length&&ut((()=>{r[t].forEach((t=>t.method(e)))}))},n}};const{LDTimeoutError:ft}=s;var gt=function(e,t){return new Promise(((n,r)=>{setTimeout((()=>{r(new ft(`${t} timed out after ${e} seconds.`))}),1e3*e)}))};const vt="unknown hook";function pt(e,t,n,r,o){try{return r()}catch(r){return e?.error(`An error was encountered in "${t}" of the "${n}" hook: ${r}`),o}}function mt(e,t){try{return t.getMetadata().name||vt}catch{return e.error("Exception thrown getting metadata for hook. Unable to get hook name."),vt}}var ht=function(e,t){const n=t?[...t]:[];return{withEvaluation:function(t,r,o,i){if(0===n.length)return i();const a=[...n],s={flagKey:t,context:r,defaultValue:o},c=function(e,t,n){return t.map((t=>pt(e,"beforeEvaluation",mt(e,t),(()=>t?.beforeEvaluation?.(n,{})??{}),{})))}(e,a,s),u=i();return function(e,t,n,r,o){for(let i=t.length-1;i>=0;i-=1){const a=t[i],s=r[i];pt(e,"afterEvaluation",mt(e,a),(()=>a?.afterEvaluation?.(n,s,o)??{}),{})}}(e,a,s,c,u),u},identify:function(t,r){const o=[...n],i={context:t,timeout:r},a=function(e,t,n){return t.map((t=>pt(e,"beforeIdentify",mt(e,t),(()=>t?.beforeIdentify?.(n,{})??{}),{})))}(e,o,i);return t=>{!function(e,t,n,r,o){for(let i=t.length-1;i>=0;i-=1){const a=t[i],s=r[i];pt(e,"afterIdentify",mt(e,a),(()=>a?.afterIdentify?.(n,s,o)??{}),{})}}(e,o,i,a,t)}},addHook:function(e){n.push(e)},afterTrack:function(t){if(0===n.length)return;const r=[...n];!function(e,t,n){for(let r=t.length-1;r>=0;r-=1){const o=t[r];pt(e,"afterTrack",mt(e,o),(()=>o?.afterTrack?.(n)),void 0)}}(e,r,t)}}};const yt="unknown plugin";function wt(e,t){try{return t.getMetadata().name||yt}catch(t){return e.error("Exception thrown getting metadata for plugin. Unable to get plugin name."),yt}}var bt={getPluginHooks:function(e,t,n){const r=[];return n.forEach((n=>{try{const o=n.getHooks?.(t);void 0===o?e.error(`Plugin ${wt(e,n)} returned undefined from getHooks.`):o&&o.length>0&&r.push(...o)}catch(t){e.error(`Exception thrown getting hooks for plugin ${wt(e,n)}. Unable to get hooks.`)}})),r},registerPlugins:function(e,t,n,r){r.forEach((r=>{try{r.register(n,t)}catch(t){e.error(`Exception thrown registering plugin ${wt(e,r)}.`)}}))},createPluginEnvironment:function(e,t,n){const r={};e.userAgent&&(r.name=e.userAgent),e.version&&(r.version=e.version),n.wrapperName&&(r.wrapperName=n.wrapperName),n.wrapperVersion&&(r.wrapperVersion=n.wrapperVersion);const o={};n.application&&(n.application.name&&(o.name=n.application.name),n.application.version&&(o.version=n.application.version));const i={sdk:r,clientSideId:t};return Object.keys(o).length>0&&(i.application=o),i}};const{commonBasicLogger:kt}=ne,{checkContext:Et,getContextKeys:Dt}=Ee,{InspectorTypes:xt,InspectorManager:Ct}=dt,{getPluginHooks:Pt,registerPlugins:St,createPluginEnvironment:It}=bt,Ot="change",Tt="internal-change";var Lt={initialize:function(e,t,n,r,o){const i=function(){if(n&&n.logger)return n.logger;return o&&o.logger&&o.logger.default||kt("warn")}(),a=Re(i),c=Ve(a),u=de.validate(n,a,o,i),l=Ct(u.inspectors,i),d=u.sendEvents;let f=e,g=u.hash;const v=[...u.plugins],p=It(r,e,u),m=Pt(i,p,v),h=ht(i,[...u.hooks,...m]),y=Me(r.localStorage,i),w=he(r,f,u),b=u.sendEvents&&!u.diagnosticOptOut,k=b?st.DiagnosticId(f):null,E=b?st.DiagnosticsAccumulator((new Date).getTime()):null,D=b?st.DiagnosticsManager(r,y,E,w,f,u,k):null,x=Ge(r,u,f,E),C=u.eventProcessor||je(r,u,f,E,a,w),P=Ze(r,u,f);let I,O,T,L={},U=u.streaming,A=!1,j=!1,R=!0;const F=u.stateProvider,N=et(null,(function(e){(function(e){if(F)return;e&&H({kind:"identify",context:e,creationDate:(new Date).getTime()})})(e),l.hasListeners(xt.clientIdentityChanged)&&l.onIdentityChanged(N.getContext())})),$=new rt(y),V=y.isEnabled()?He(y,f,g,N):null;function H(e){f&&(F&&F.enqueueEvent&&F.enqueueEvent(e)||(e.context?(R=!1,!d||j||r.isDoNotTrack()||(i.debug(ie.debugEnqueueingEvent(e.kind)),C.enqueue(e))):R&&(i.warn(ie.eventWithoutContext()),R=!1)))}function M(e,t){l.hasListeners(xt.flagDetailChanged)&&l.onFlagChanged(e.key,J(t))}function q(){l.hasListeners(xt.flagDetailsChanged)&&l.onFlags(Object.entries(L).map((([e,t])=>({key:e,detail:J(t)}))).reduce(((e,t)=>(e[t.key]=t.detail,e)),{}))}function z(e,t,n,r){const o=N.getContext(),i=new Date,a={kind:"feature",key:e,context:o,value:t?t.value:null,variation:t?t.variationIndex:null,default:n,creationDate:i.getTime()},s=L[e];s&&(a.version=s.flagVersion?s.flagVersion:s.version,a.trackEvents=s.trackEvents,a.debugEventsUntilDate=s.debugEventsUntilDate),(r||s&&s.trackReason)&&t&&(a.reason=t.reason),H(a)}function K(e){return Et(e,!1)?Promise.resolve(e):Promise.reject(new s.LDInvalidUserError(ie.invalidContext()))}function _(e,t,n,r,o,i){let a,s;return L&&S.objectHasOwnProperty(L,e)&&L[e]&&!L[e].deleted?(s=L[e],a=J(s),null!==s.value&&void 0!==s.value||(a.value=t)):a={value:t,variationIndex:null,reason:{kind:"ERROR",errorKind:"FLAG_NOT_FOUND"}},n&&(o||s?.prerequisites?.forEach((e=>{_(e,void 0,n,!1,!1,!1)})),z(e,a,t,r)),!o&&i&&function(e,t){l.hasListeners(xt.flagUsed)&&l.onFlagUsed(e,t,N.getContext())}(e,a),a}function J(e){return{value:e.value,variationIndex:void 0===e.variation?null:e.variation,reason:e.reason||null}}function B(){if(O=!0,!N.getContext())return;const e=e=>{try{return JSON.parse(e)}catch(e){return void a.maybeReportError(new s.LDInvalidDataError(ie.invalidData()))}};x.connect(N.getContext(),g,{ping:function(){i.debug(ie.debugStreamPing());const e=N.getContext();P.fetchFlagSettings(e,g).then((t=>{S.deepEquals(e,N.getContext())&&W(t||{})})).catch((e=>{a.maybeReportError(new s.LDFlagFetchError(ie.errorFetchingFlags(e)))}))},put:function(t){const n=e(t.data);n&&(i.debug(ie.debugStreamPut()),W(n))},patch:function(t){const n=e(t.data);if(!n)return;const r=L[n.key];if(!r||!r.version||!n.version||r.version<n.version){i.debug(ie.debugStreamPatch(n.key));const e={},t=S.extend({},n);delete t.key,L[n.key]=t;const o=J(t);e[n.key]=r?{previous:r.value,current:o}:{current:o},M(n,t),X(e)}else i.debug(ie.debugStreamPatchIgnored(n.key))},delete:function(t){const n=e(t.data);if(n)if(!L[n.key]||L[n.key].version<n.version){i.debug(ie.debugStreamDelete(n.key));const e={};L[n.key]&&!L[n.key].deleted&&(e[n.key]={previous:L[n.key].value}),L[n.key]={version:n.version,deleted:!0},M(n,L[n.key]),X(e)}else i.debug(ie.debugStreamDeleteIgnored(n.key))}})}function G(){O&&(x.disconnect(),O=!1)}function W(e){const t={};if(!e)return Promise.resolve();for(const n in L)S.objectHasOwnProperty(L,n)&&L[n]&&(e[n]&&!S.deepEquals(e[n].value,L[n].value)?t[n]={previous:L[n].value,current:J(e[n])}:e[n]&&!e[n].deleted||(t[n]={previous:L[n].value}));for(const n in e)S.objectHasOwnProperty(e,n)&&e[n]&&(!L[n]||L[n].deleted)&&(t[n]={current:J(e[n])});return L={...e},q(),X(t).catch((()=>{}))}function X(e){const t=Object.keys(e);if(t.length>0){const n={};t.forEach((t=>{const r=e[t].current,o=r?r.value:void 0,i=e[t].previous;a.emit(Ot+":"+t,o,i),n[t]=r?{current:o,previous:i}:{previous:i}})),a.emit(Ot,n),a.emit(Tt,L),u.sendEventsOnlyForVariation||F||t.forEach((t=>{z(t,e[t].current)}))}return I&&V?V.saveFlags(L):Promise.resolve()}function Q(){const e=U||T&&void 0===U;e&&!O?B():!e&&O&&G(),D&&D.setStreaming(e)}function Y(e){return e===Ot||e.substr(0,7)===Ot+":"}if("string"==typeof u.bootstrap&&"LOCALSTORAGE"===u.bootstrap.toUpperCase()&&(V?I=!0:i.warn(ie.localStorageUnavailable())),"object"==typeof u.bootstrap&&(L=function(e){const t=Object.keys(e),n="$flagsState",r="$valid",o=e[n];!o&&t.length&&i.warn(ie.bootstrapOldFormat()),!1===e[r]&&i.warn(ie.bootstrapInvalid());const a={};return t.forEach((t=>{if(t!==n&&t!==r){let n={value:e[t]};o&&o[t]?n=S.extend(n,o[t]):n.version=0,a[t]=n}})),a}(u.bootstrap)),F){const e=F.getInitialState();e?Z(e):F.on("init",Z),F.on("update",(function(e){e.context&&N.setContext(e.context);e.flags&&W(e.flags)}))}else(function(){if(!e)return Promise.reject(new s.LDInvalidEnvironmentIdError(ie.environmentNotSpecified()));let n;return $.processContext(t).then(K).then((e=>(n=S.once(h.identify(e,void 0)),e))).then((e=>(n?.({status:"completed"}),N.setContext(e),"object"==typeof u.bootstrap?ee():I?V.loadFlags().then((e=>null==e?(L={},P.fetchFlagSettings(N.getContext(),g).then((e=>W(e||{}))).then(ee).catch((e=>{te(new s.LDFlagFetchError(ie.errorFetchingFlags(e)))}))):(L=e,S.onNextTick(ee),P.fetchFlagSettings(N.getContext(),g).then((e=>W(e))).catch((e=>a.maybeReportError(e)))))):P.fetchFlagSettings(N.getContext(),g).then((e=>{L=e||{},q(),ee()})).catch((e=>{L={},te(e)}))))).catch((e=>{throw n?.({status:"error"}),e}))})().catch(te);function Z(e){f=e.environment,N.setContext(e.context),L={...e.flags},S.onNextTick(ee)}function ee(){i.info(ie.clientInitialized()),A=!0,Q(),c.signalSuccess()}function te(e){c.signalFailure(e)}const ne={waitForInitialization:function(e=void 0){if(null!=e){if("number"==typeof e)return function(e){e>5&&i.warn("The waitForInitialization function was called with a timeout greater than 5 seconds. We recommend a timeout of 5 seconds or less.");const t=c.getInitializationPromise(),n=gt(e,"waitForInitialization");return Promise.race([n,t]).catch((e=>{throw e instanceof s.LDTimeoutError&&i.error(`waitForInitialization error: ${e}`),e}))}(e);i.warn("The waitForInitialization method was provided with a non-numeric timeout.")}return i.warn("The waitForInitialization function was called without a timeout specified. In a future version a default timeout will be applied."),c.getInitializationPromise()},waitUntilReady:()=>c.getReadyPromise(),identify:function(e,t,n){if(j)return S.wrapPromiseCallback(Promise.resolve({}),n);if(F)return i.warn(ie.identifyDisabled()),S.wrapPromiseCallback(Promise.resolve(S.transformVersionedValuesToValues(L)),n);let r;const o=I&&V?V.clearFlags():Promise.resolve();return S.wrapPromiseCallback(o.then((()=>$.processContext(e))).then(K).then((e=>(r=S.once(h.identify(e,void 0)),e))).then((e=>P.fetchFlagSettings(e,t).then((n=>{const r=S.transformVersionedValuesToValues(n);return N.setContext(e),g=t,n?W(n).then((()=>r)):r})))).then((e=>(r?.({status:"completed"}),O&&B(),e))).catch((e=>(r?.({status:"error"}),a.maybeReportError(e),Promise.reject(e)))),n)},getContext:function(){return N.getContext()},variation:function(e,t){const{value:n}=h.withEvaluation(e,N.getContext(),t,(()=>_(e,t,!0,!1,!1,!0)));return n},variationDetail:function(e,t){return h.withEvaluation(e,N.getContext(),t,(()=>_(e,t,!0,!0,!1,!0)))},track:function(e,t,n){if("string"!=typeof e)return void a.maybeReportError(new s.LDInvalidEventKeyError(ie.unknownCustomEventKey(e)));void 0!==n&&"number"!=typeof n&&i.warn(ie.invalidMetricValue(typeof n)),r.customEventFilter&&!r.customEventFilter(e)&&i.warn(ie.unknownCustomEventKey(e));const o=N.getContext(),c={kind:"custom",key:e,context:o,url:r.getCurrentUrl(),creationDate:(new Date).getTime()};o&&o.anonymous&&(c.contextKind=o.anonymous?"anonymousUser":"user"),null!=t&&(c.data=t),null!=n&&(c.metricValue=n),H(c),h.afterTrack({context:o,key:e,data:t,metricValue:n})},on:function(e,t,n){Y(e)?(T=!0,A&&Q(),a.on(e,t,n)):a.on(...arguments)},off:function(e){if(a.off(...arguments),Y(e)){let e=!1;a.getEvents().forEach((t=>{Y(t)&&a.getEventListenerCount(t)>0&&(e=!0)})),e||(T=!1,O&&void 0===U&&G())}},setStreaming:function(e){const t=null===e?void 0:e;t!==U&&(U=t,Q())},flush:function(e){return S.wrapPromiseCallback(d?C.flush():Promise.resolve(),e)},allFlags:function(){const e={};if(!L)return e;for(const t in L)S.objectHasOwnProperty(L,t)&&!L[t].deleted&&(e[t]=_(t,null,!u.sendEventsOnlyForVariation,!1,!0,!1).value);return e},close:function(e){if(j)return S.wrapPromiseCallback(Promise.resolve(),e);const t=()=>{j=!0,L={}},n=Promise.resolve().then((()=>{if(G(),D&&D.stop(),d)return C.stop(),C.flush()})).then(t).catch(t);return S.wrapPromiseCallback(n,e)},addHook:function(e){h.addHook(e)}};return St(i,p,ne,v),{client:ne,options:u,emitter:a,ident:N,logger:i,requestor:P,start:function(){d&&(D&&D.start(),C.start())},enqueueEvent:H,getFlagsInternal:function(){return L},getEnvironmentId:()=>f,internalChangeEventName:Tt}},commonBasicLogger:kt,errors:s,messages:ie,utils:S,getContextKeys:Dt},Ut=Lt.initialize,At=Lt.errors,jt=Lt.messages;function Rt(e,t,n){return(t=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ft(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Nt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ft(Object(n),!0).forEach((function(t){Rt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ft(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var $t=Lt.commonBasicLogger;var Vt=function(e){return $t(Nt({destination:console.log},e))};var Ht={promise:Promise.resolve({status:200,header:function(){return null},body:null})};function Mt(e,t,n,r,o){if(o&&!function(){var e=window.navigator&&window.navigator.userAgent;if(e){var t=e.match(/Chrom(e|ium)\/([0-9]+)\./);if(t)return parseInt(t[2],10)<73}return!0}())return Ht;var i=new window.XMLHttpRequest;for(var a in i.open(e,t,!o),n||{})Object.prototype.hasOwnProperty.call(n,a)&&i.setRequestHeader(a,n[a]);if(o){try{i.send(r)}catch(e){}return Ht}var s,c=new Promise((function(e,t){i.addEventListener("load",(function(){s||e({status:i.status,header:function(e){return i.getResponseHeader(e)},body:i.responseText})})),i.addEventListener("error",(function(){s||t(new Error)})),i.send(r)}));return{promise:c,cancel:function(){s=!0,i.abort()}}}var qt=e=>{if("string"!=typeof e)throw new TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")};function zt(e,t,n,r){var o,i,a=(("substring"===e.kind||"regex"===e.kind)&&r.includes("/")?t:t.replace(r,"")).replace(n,"");switch(e.kind){case"exact":i=t,o=new RegExp("^"+qt(e.url)+"/?$");break;case"canonical":i=a,o=new RegExp("^"+qt(e.url)+"/?$");break;case"substring":i=a,o=new RegExp(".*"+qt(e.substring)+".*$");break;case"regex":i=a,o=new RegExp(e.pattern);break;default:return!1}return o.test(i)}function Kt(e,t){for(var n={},r=null,o=[],i=0;i<e.length;i++)for(var a=e[i],s=a.urls||[],c=0;c<s.length;c++)if(zt(s[c],window.location.href,window.location.search,window.location.hash)){"pageview"===a.kind?t("pageview",a):(o.push(a),t("click_pageview",a));break}return o.length>0&&(r=function(e){for(var n=function(e,t){for(var n=[],r=0;r<t.length;r++)for(var o=e.target,i=t[r],a=i.selector,s=document.querySelectorAll(a);o&&s.length>0;){for(var c=0;c<s.length;c++)o===s[c]&&n.push(i);o=o.parentNode}return n}(e,o),r=0;r<n.length;r++)t("click",n[r])},document.addEventListener("click",r)),n.dispose=function(){document.removeEventListener("click",r)},n}function _t(e,t){var n,r;function o(){r&&r.dispose(),n&&n.length&&(r=Kt(n,i))}function i(t,n){var r=e.ident.getContext(),o={kind:t,key:n.key,data:null,url:window.location.href,creationDate:(new Date).getTime(),context:r};return"click"===t&&(o.selector=n.selector),e.enqueueEvent(o)}return e.requestor.fetchJSON("/sdk/goals/"+e.getEnvironmentId()).then((function(e){e&&e.length>0&&(r=Kt(n=e,i),function(e,t){var n,r=window.location.href;function o(){(n=window.location.href)!==r&&(r=n,t())}!function e(t,n){t(),setTimeout((function(){e(t,n)}),n)}(o,e),window.history&&window.history.pushState?window.addEventListener("popstate",o):window.addEventListener("hashchange",o)}(300,o)),t()})).catch((function(n){e.emitter.maybeReportError(new At.LDUnexpectedResponseError((n&&n.message,n.message))),t()})),{}}var Jt="goalsReady",Bt={fetchGoals:{default:!0},hash:{type:"string"},eventProcessor:{type:"object"},eventUrlTransformer:{type:"function"},disableSyncEventPost:{default:!1}};function Gt(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=function(e){var t,n={userAgentHeaderName:"X-LaunchDarkly-User-Agent",synchronousFlush:!1};if(window.XMLHttpRequest){var r=e&&e.disableSyncEventPost;n.httpRequest=function(e,t,o,i){var a=n.synchronousFlush&!r;return n.synchronousFlush=!1,Mt(e,t,o,i,a)}}n.httpAllowsPost=function(){return void 0===t&&(t=!!window.XMLHttpRequest&&"withCredentials"in new window.XMLHttpRequest),t},n.httpFallbackPing=function(e){(new window.Image).src=e};var o,i=e&&e.eventUrlTransformer;n.getCurrentUrl=function(){return i?i(window.location.href):window.location.href},n.isDoNotTrack=function(){var e;return 1===(e=window.navigator&&void 0!==window.navigator.doNotTrack?window.navigator.doNotTrack:window.navigator&&void 0!==window.navigator.msDoNotTrack?window.navigator.msDoNotTrack:window.doNotTrack)||!0===e||"1"===e||"yes"===e};try{window.localStorage&&(n.localStorage={get:function(e){return new Promise((function(t){t(window.localStorage.getItem(e))}))},set:function(e,t){return new Promise((function(n){window.localStorage.setItem(e,t),n()}))},clear:function(e){return new Promise((function(t){window.localStorage.removeItem(e),t()}))}})}catch(e){n.localStorage=null}if(e&&e.useReport&&"function"==typeof window.EventSourcePolyfill&&window.EventSourcePolyfill.supportedOptions&&window.EventSourcePolyfill.supportedOptions.method?(n.eventSourceAllowsReport=!0,o=window.EventSourcePolyfill):(n.eventSourceAllowsReport=!1,o=window.EventSource),window.EventSource){var a=3e5;n.eventSourceFactory=function(e,t){var n=Nt(Nt({},{heartbeatTimeout:a,silentTimeout:a,skipDefaultHeaders:!0}),t);return new o(e,n)},n.eventSourceIsActive=function(e){return e.readyState===window.EventSource.OPEN||e.readyState===window.EventSource.CONNECTING}}return n.userAgent="JSClient",n.version="3.8.1",n.diagnosticSdkData={name:"js-client-sdk",version:"3.8.1"},n.diagnosticPlatformData={name:"JS"},n.diagnosticUseCombinedEvent=!0,n}(n),o=Ut(e,t,n,r,Bt),i=o.client,a=o.options,s=o.emitter,c=new Promise((function(e){var t=s.on(Jt,(function(){s.off(Jt,t),e()}))}));i.waitUntilGoalsReady=function(){return c},a.fetchGoals?_t(o,(function(){return s.emit(Jt)})):s.emit(Jt),"complete"!==document.readyState?window.addEventListener("load",o.start):o.start();var u=function(){r.synchronousFlush=!0,i.flush().catch((function(){})),r.synchronousFlush=!1};return document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&u()})),window.addEventListener("pagehide",u),i}var Wt=Vt,Xt="3.8.1";var Qt={initialize:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return console&&console.warn&&console.warn(jt.deprecated("default export","named LDClient export")),Gt(e,t,n)},version:Xt};exports.basicLogger=Wt,exports.createConsoleLogger=undefined,exports.default=Qt,exports.initialize=Gt,exports.version=Xt;
//# sourceMappingURL=ldclient.cjs.js.map
