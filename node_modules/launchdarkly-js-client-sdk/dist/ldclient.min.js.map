{"version": 3, "file": "ldclient.min.js", "sources": ["../node_modules/launchdarkly-js-sdk-common/src/errors.js", "../node_modules/base64-js/index.js", "../node_modules/launchdarkly-js-sdk-common/node_modules/fast-deep-equal/index.js", "../node_modules/launchdarkly-js-sdk-common/src/utils.js", "../node_modules/uuid/dist/esm-browser/rng.js", "../node_modules/uuid/dist/esm-browser/regex.js", "../node_modules/uuid/dist/esm-browser/validate.js", "../node_modules/uuid/dist/esm-browser/stringify.js", "../node_modules/uuid/dist/esm-browser/v1.js", "../node_modules/uuid/dist/esm-browser/parse.js", "../node_modules/uuid/dist/esm-browser/v35.js", "../node_modules/uuid/dist/esm-browser/md5.js", "../node_modules/uuid/dist/esm-browser/v3.js", "../node_modules/uuid/dist/esm-browser/sha1.js", "../node_modules/uuid/dist/esm-browser/v5.js", "../node_modules/uuid/dist/esm-browser/v4.js", "../node_modules/uuid/dist/esm-browser/nil.js", "../node_modules/uuid/dist/esm-browser/version.js", "../node_modules/launchdarkly-js-sdk-common/src/loggers.js", "../node_modules/launchdarkly-js-sdk-common/src/messages.js", "../node_modules/launchdarkly-js-sdk-common/src/configuration.js", "../node_modules/launchdarkly-js-sdk-common/src/headers.js", "../node_modules/launchdarkly-js-sdk-common/src/EventSender.js", "../node_modules/launchdarkly-js-sdk-common/src/canonicalize.js", "../node_modules/launchdarkly-js-sdk-common/src/context.js", "../node_modules/launchdarkly-js-sdk-common/src/EventSummarizer.js", "../node_modules/launchdarkly-js-sdk-common/src/MultiEventSummarizer.js", "../node_modules/launchdarkly-js-sdk-common/src/attributeReference.js", "../node_modules/launchdarkly-js-sdk-common/src/ContextFilter.js", "../node_modules/launchdarkly-js-sdk-common/src/EventProcessor.js", "../node_modules/launchdarkly-js-sdk-common/src/EventEmitter.js", "../node_modules/launchdarkly-js-sdk-common/src/InitializationState.js", "../node_modules/launchdarkly-js-sdk-common/src/PersistentFlagStore.js", "../node_modules/launchdarkly-js-sdk-common/src/PersistentStorage.js", "../node_modules/launchdarkly-js-sdk-common/src/Stream.js", "../node_modules/launchdarkly-js-sdk-common/src/promiseCoalescer.js", "../node_modules/launchdarkly-js-sdk-common/src/Requestor.js", "../node_modules/launchdarkly-js-sdk-common/src/Identity.js", "../node_modules/launchdarkly-js-sdk-common/src/AnonymousContextProcessor.js", "../node_modules/launchdarkly-js-sdk-common/src/diagnosticEvents.js", "../node_modules/launchdarkly-js-sdk-common/src/SafeInspector.js", "../node_modules/launchdarkly-js-sdk-common/src/InspectorManager.js", "../node_modules/launchdarkly-js-sdk-common/src/timedPromise.js", "../node_modules/launchdarkly-js-sdk-common/src/HookRunner.js", "../node_modules/launchdarkly-js-sdk-common/src/plugins.js", "../node_modules/launchdarkly-js-sdk-common/src/index.js", "../src/basicLogger.js", "../src/httpRequest.js", "../node_modules/escape-string-regexp/index.js", "../src/GoalTracker.js", "../src/GoalManager.js", "../src/index.js", "../src/browserPlatform.js"], "sourcesContent": ["function createCustomError(name) {\n  function CustomError(message, code) {\n    Error.captureStackTrace && Error.captureStackTrace(this, this.constructor);\n    this.message = message;\n    this.code = code;\n  }\n\n  CustomError.prototype = new Error();\n  CustomError.prototype.name = name;\n  CustomError.prototype.constructor = CustomError;\n\n  return CustomError;\n}\n\nconst LDUnexpectedResponseError = createCustomError('LaunchDarklyUnexpectedResponseError');\nconst LDInvalidEnvironmentIdError = createCustomError('LaunchDarklyInvalidEnvironmentIdError');\nconst LDInvalidUserError = createCustomError('LaunchDarklyInvalidUserError');\nconst LDInvalidEventKeyError = createCustomError('LaunchDarklyInvalidEventKeyError');\nconst LDInvalidArgumentError = createCustomError('LaunchDarklyInvalidArgumentError');\nconst LDFlagFetchError = createCustomError('LaunchDarklyFlagFetchError');\nconst LDInvalidDataError = createCustomError('LaunchDarklyInvalidDataError');\nconst LDTimeoutError = createCustomError('LaunchDarklyTimeoutError');\n\nfunction isHttpErrorRecoverable(status) {\n  if (status >= 400 && status < 500) {\n    return status === 400 || status === 408 || status === 429;\n  }\n  return true;\n}\n\nmodule.exports = {\n  LDUnexpectedResponseError,\n  LDInvalidEnvironmentIdError,\n  LDInvalidUserError,\n  LDInvalidEventKeyError,\n  LDInvalidArgumentError,\n  LDInvalidDataError,\n  LDFlagFetchError,\n  LDTimeoutError,\n  isHttpErrorRecoverable,\n};\n", "'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n", "'use strict';\n\nvar isArray = Array.isArray;\nvar keyList = Object.keys;\nvar hasProp = Object.prototype.hasOwnProperty;\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    var arrA = isArray(a)\n      , arrB = isArray(b)\n      , i\n      , length\n      , key;\n\n    if (arrA && arrB) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n    if (arrA != arrB) return false;\n\n    var dateA = a instanceof Date\n      , dateB = b instanceof Date;\n    if (dateA != dateB) return false;\n    if (dateA && dateB) return a.getTime() == b.getTime();\n\n    var regexpA = a instanceof RegExp\n      , regexpB = b instanceof RegExp;\n    if (regexpA != regexpB) return false;\n    if (regexpA && regexpB) return a.toString() == b.toString();\n\n    var keys = keyList(a);\n    length = keys.length;\n\n    if (length !== keyList(b).length)\n      return false;\n\n    for (i = length; i-- !== 0;)\n      if (!hasProp.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      key = keys[i];\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  return a!==a && b!==b;\n};\n", "const base64 = require('base64-js');\nconst fastDeepEqual = require('fast-deep-equal');\n\nconst userAttrsToStringify = ['key', 'ip', 'country', 'email', 'firstName', 'lastName', 'avatar', 'name'];\n\nfunction appendUrlPath(baseUrl, path) {\n  // Ensure that URL concatenation is done correctly regardless of whether the\n  // base URL has a trailing slash or not.\n  const trimBaseUrl = baseUrl.endsWith('/') ? baseUrl.substring(0, baseUrl.length - 1) : baseUrl;\n  return trimBaseUrl + (path.startsWith('/') ? '' : '/') + path;\n}\n\n// See http://ecmanaut.blogspot.com/2006/07/encoding-decoding-utf8-in-javascript.html\nfunction btoa(s) {\n  const escaped = unescape(encodeURIComponent(s));\n  return base64.fromByteArray(stringToBytes(escaped));\n}\n\nfunction stringToBytes(s) {\n  const b = [];\n  for (let i = 0; i < s.length; i++) {\n    b.push(s.charCodeAt(i));\n  }\n  return b;\n}\n\nfunction base64URLEncode(s) {\n  return (\n    btoa(s)\n      // eslint-disable-next-line\n      .replace(/=/g, '')\n      .replace(/\\+/g, '-')\n      .replace(/\\//g, '_')\n  );\n}\n\nfunction clone(obj) {\n  return JSON.parse(JSON.stringify(obj));\n}\n\nfunction deepEquals(a, b) {\n  return fastDeepEqual(a, b);\n}\n\n// Events emitted in LDClient's initialize method will happen before the consumer\n// can register a listener, so defer them to next tick.\nfunction onNextTick(cb) {\n  setTimeout(cb, 0);\n}\n\n/**\n * Wrap a promise to invoke an optional callback upon resolution or rejection.\n *\n * This function assumes the callback follows the Node.js callback type: (err, value) => void\n *\n * If a callback is provided:\n *   - if the promise is resolved, invoke the callback with (null, value)\n *   - if the promise is rejected, invoke the callback with (error, null)\n *\n * @param {Promise<any>} promise\n * @param {Function} callback\n * @returns Promise<any> | undefined\n */\nfunction wrapPromiseCallback(promise, callback) {\n  const ret = promise.then(\n    value => {\n      if (callback) {\n        setTimeout(() => {\n          callback(null, value);\n        }, 0);\n      }\n      return value;\n    },\n    error => {\n      if (callback) {\n        setTimeout(() => {\n          callback(error, null);\n        }, 0);\n      } else {\n        return Promise.reject(error);\n      }\n    }\n  );\n\n  return !callback ? ret : undefined;\n}\n\n/**\n * Takes a map of flag keys to values, and returns the more verbose structure used by the\n * client stream.\n */\nfunction transformValuesToVersionedValues(flags) {\n  const ret = {};\n  for (const key in flags) {\n    if (objectHasOwnProperty(flags, key)) {\n      ret[key] = { value: flags[key], version: 0 };\n    }\n  }\n  return ret;\n}\n\n/**\n * Converts the internal flag state map to a simple map of flag keys to values.\n */\nfunction transformVersionedValuesToValues(flagsState) {\n  const ret = {};\n  for (const key in flagsState) {\n    if (objectHasOwnProperty(flagsState, key)) {\n      ret[key] = flagsState[key].value;\n    }\n  }\n  return ret;\n}\n\nfunction getLDUserAgentString(platform) {\n  const version = platform.version || '?';\n  return platform.userAgent + '/' + version;\n}\n\nfunction extend(...objects) {\n  return objects.reduce((acc, obj) => ({ ...acc, ...obj }), {});\n}\n\nfunction objectHasOwnProperty(object, name) {\n  return Object.prototype.hasOwnProperty.call(object, name);\n}\n\nfunction sanitizeContext(context) {\n  if (!context) {\n    return context;\n  }\n  let newContext;\n  // Only stringify user attributes for legacy users.\n  if (context.kind === null || context.kind === undefined) {\n    userAttrsToStringify.forEach(attr => {\n      const value = context[attr];\n      if (value !== undefined && typeof value !== 'string') {\n        newContext = newContext || { ...context };\n        newContext[attr] = String(value);\n      }\n    });\n  }\n\n  return newContext || context;\n}\n\n/**\n * Creates a function that will invoke the provided function only once.\n *\n * If the function returns a value, then that returned value will be re-used for subsequent invocations.\n *\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n */\nfunction once(func) {\n  let called = false;\n  let result;\n  return function(...args) {\n    if (!called) {\n      called = true;\n      result = func.apply(this, args);\n    }\n    return result;\n  };\n}\n\nmodule.exports = {\n  appendUrlPath,\n  base64URLEncode,\n  btoa,\n  clone,\n  deepEquals,\n  extend,\n  getLDUserAgentString,\n  objectHasOwnProperty,\n  onNextTick,\n  sanitizeContext,\n  transformValuesToVersionedValues,\n  transformVersionedValuesToValues,\n  wrapPromiseCallback,\n  once,\n};\n", "// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nvar getRandomValues;\nvar rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation. Also,\n    // find the complete implementation of crypto (msCrypto) on IE11.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto) || typeof msCrypto !== 'undefined' && typeof msCrypto.getRandomValues === 'function' && msCrypto.getRandomValues.bind(msCrypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}", "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;", "import REGEX from './regex.js';\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && REGEX.test(uuid);\n}\n\nexport default validate;", "import validate from './validate.js';\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nvar byteToHex = [];\n\nfor (var i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).substr(1));\n}\n\nfunction stringify(arr) {\n  var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  var uuid = (byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]]).toLowerCase(); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!validate(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\nexport default stringify;", "import rng from './rng.js';\nimport stringify from './stringify.js'; // **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\n\nvar _nodeId;\n\nvar _clockseq; // Previous uuid creation time\n\n\nvar _lastMSecs = 0;\nvar _lastNSecs = 0; // See https://github.com/uuidjs/uuid for API details\n\nfunction v1(options, buf, offset) {\n  var i = buf && offset || 0;\n  var b = buf || new Array(16);\n  options = options || {};\n  var node = options.node || _nodeId;\n  var clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq; // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n\n  if (node == null || clockseq == null) {\n    var seedBytes = options.random || (options.rng || rng)();\n\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];\n    }\n\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  } // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n\n\n  var msecs = options.msecs !== undefined ? options.msecs : Date.now(); // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n\n  var nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1; // Time since last uuid creation (in msecs)\n\n  var dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000; // Per 4.2.1.2, Bump clockseq on clock regression\n\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  } // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n\n\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  } // Per 4.2.1.2 Throw error if too many uuids are requested\n\n\n  if (nsecs >= 10000) {\n    throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");\n  }\n\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq; // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n\n  msecs += 12219292800000; // `time_low`\n\n  var tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff; // `time_mid`\n\n  var tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff; // `time_high_and_version`\n\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n\n  b[i++] = tmh >>> 16 & 0xff; // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n\n  b[i++] = clockseq >>> 8 | 0x80; // `clock_seq_low`\n\n  b[i++] = clockseq & 0xff; // `node`\n\n  for (var n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n\n  return buf || stringify(b);\n}\n\nexport default v1;", "import validate from './validate.js';\n\nfunction parse(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  var v;\n  var arr = new Uint8Array(16); // Parse ########-....-....-....-............\n\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff; // Parse ........-####-....-....-............\n\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff; // Parse ........-....-####-....-............\n\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff; // Parse ........-....-....-####-............\n\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff; // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\n\nexport default parse;", "import stringify from './stringify.js';\nimport parse from './parse.js';\n\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  var bytes = [];\n\n  for (var i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n\n  return bytes;\n}\n\nexport var DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport var URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function (name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n\n    if (typeof namespace === 'string') {\n      namespace = parse(namespace);\n    }\n\n    if (namespace.length !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n\n    var bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n\n    if (buf) {\n      offset = offset || 0;\n\n      for (var i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n\n      return buf;\n    }\n\n    return stringify(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}", "/*\n * Browser-compatible JavaScript MD5\n *\n * Modification of JavaScript MD5\n * https://github.com/blueimp/JavaScript-MD5\n *\n * Copyright 2011, <PERSON>\n * https://blueimp.net\n *\n * Licensed under the MIT license:\n * https://opensource.org/licenses/MIT\n *\n * Based on\n * A JavaScript implementation of the RSA Data Security, Inc. MD5 Message\n * Digest Algorithm, as defined in RFC 1321.\n * Version 2.2 Copyright (C) <PERSON> 1999 - 2009\n * Other contributors: <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Lostinet\n * Distributed under the BSD License\n * See http://pajhome.org.uk/crypt/md5 for more info.\n */\nfunction md5(bytes) {\n  if (typeof bytes === 'string') {\n    var msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = new Uint8Array(msg.length);\n\n    for (var i = 0; i < msg.length; ++i) {\n      bytes[i] = msg.charCodeAt(i);\n    }\n  }\n\n  return md5ToHexEncodedArray(wordsToMd5(bytesToWords(bytes), bytes.length * 8));\n}\n/*\n * Convert an array of little-endian words to an array of bytes\n */\n\n\nfunction md5ToHexEncodedArray(input) {\n  var output = [];\n  var length32 = input.length * 32;\n  var hexTab = '0123456789abcdef';\n\n  for (var i = 0; i < length32; i += 8) {\n    var x = input[i >> 5] >>> i % 32 & 0xff;\n    var hex = parseInt(hexTab.charAt(x >>> 4 & 0x0f) + hexTab.charAt(x & 0x0f), 16);\n    output.push(hex);\n  }\n\n  return output;\n}\n/**\n * Calculate output length with padding and bit length\n */\n\n\nfunction getOutputLength(inputLength8) {\n  return (inputLength8 + 64 >>> 9 << 4) + 14 + 1;\n}\n/*\n * Calculate the MD5 of an array of little-endian words, and a bit length.\n */\n\n\nfunction wordsToMd5(x, len) {\n  /* append padding */\n  x[len >> 5] |= 0x80 << len % 32;\n  x[getOutputLength(len) - 1] = len;\n  var a = 1732584193;\n  var b = -271733879;\n  var c = -1732584194;\n  var d = 271733878;\n\n  for (var i = 0; i < x.length; i += 16) {\n    var olda = a;\n    var oldb = b;\n    var oldc = c;\n    var oldd = d;\n    a = md5ff(a, b, c, d, x[i], 7, -680876936);\n    d = md5ff(d, a, b, c, x[i + 1], 12, -389564586);\n    c = md5ff(c, d, a, b, x[i + 2], 17, 606105819);\n    b = md5ff(b, c, d, a, x[i + 3], 22, -1044525330);\n    a = md5ff(a, b, c, d, x[i + 4], 7, -176418897);\n    d = md5ff(d, a, b, c, x[i + 5], 12, 1200080426);\n    c = md5ff(c, d, a, b, x[i + 6], 17, -1473231341);\n    b = md5ff(b, c, d, a, x[i + 7], 22, -45705983);\n    a = md5ff(a, b, c, d, x[i + 8], 7, 1770035416);\n    d = md5ff(d, a, b, c, x[i + 9], 12, -1958414417);\n    c = md5ff(c, d, a, b, x[i + 10], 17, -42063);\n    b = md5ff(b, c, d, a, x[i + 11], 22, -1990404162);\n    a = md5ff(a, b, c, d, x[i + 12], 7, 1804603682);\n    d = md5ff(d, a, b, c, x[i + 13], 12, -40341101);\n    c = md5ff(c, d, a, b, x[i + 14], 17, -1502002290);\n    b = md5ff(b, c, d, a, x[i + 15], 22, 1236535329);\n    a = md5gg(a, b, c, d, x[i + 1], 5, -165796510);\n    d = md5gg(d, a, b, c, x[i + 6], 9, -1069501632);\n    c = md5gg(c, d, a, b, x[i + 11], 14, 643717713);\n    b = md5gg(b, c, d, a, x[i], 20, -373897302);\n    a = md5gg(a, b, c, d, x[i + 5], 5, -701558691);\n    d = md5gg(d, a, b, c, x[i + 10], 9, 38016083);\n    c = md5gg(c, d, a, b, x[i + 15], 14, -660478335);\n    b = md5gg(b, c, d, a, x[i + 4], 20, -405537848);\n    a = md5gg(a, b, c, d, x[i + 9], 5, 568446438);\n    d = md5gg(d, a, b, c, x[i + 14], 9, -1019803690);\n    c = md5gg(c, d, a, b, x[i + 3], 14, -187363961);\n    b = md5gg(b, c, d, a, x[i + 8], 20, 1163531501);\n    a = md5gg(a, b, c, d, x[i + 13], 5, -1444681467);\n    d = md5gg(d, a, b, c, x[i + 2], 9, -51403784);\n    c = md5gg(c, d, a, b, x[i + 7], 14, 1735328473);\n    b = md5gg(b, c, d, a, x[i + 12], 20, -1926607734);\n    a = md5hh(a, b, c, d, x[i + 5], 4, -378558);\n    d = md5hh(d, a, b, c, x[i + 8], 11, -2022574463);\n    c = md5hh(c, d, a, b, x[i + 11], 16, 1839030562);\n    b = md5hh(b, c, d, a, x[i + 14], 23, -35309556);\n    a = md5hh(a, b, c, d, x[i + 1], 4, -1530992060);\n    d = md5hh(d, a, b, c, x[i + 4], 11, 1272893353);\n    c = md5hh(c, d, a, b, x[i + 7], 16, -155497632);\n    b = md5hh(b, c, d, a, x[i + 10], 23, -1094730640);\n    a = md5hh(a, b, c, d, x[i + 13], 4, 681279174);\n    d = md5hh(d, a, b, c, x[i], 11, -358537222);\n    c = md5hh(c, d, a, b, x[i + 3], 16, -722521979);\n    b = md5hh(b, c, d, a, x[i + 6], 23, 76029189);\n    a = md5hh(a, b, c, d, x[i + 9], 4, -640364487);\n    d = md5hh(d, a, b, c, x[i + 12], 11, -421815835);\n    c = md5hh(c, d, a, b, x[i + 15], 16, 530742520);\n    b = md5hh(b, c, d, a, x[i + 2], 23, -995338651);\n    a = md5ii(a, b, c, d, x[i], 6, -198630844);\n    d = md5ii(d, a, b, c, x[i + 7], 10, 1126891415);\n    c = md5ii(c, d, a, b, x[i + 14], 15, -1416354905);\n    b = md5ii(b, c, d, a, x[i + 5], 21, -57434055);\n    a = md5ii(a, b, c, d, x[i + 12], 6, 1700485571);\n    d = md5ii(d, a, b, c, x[i + 3], 10, -1894986606);\n    c = md5ii(c, d, a, b, x[i + 10], 15, -1051523);\n    b = md5ii(b, c, d, a, x[i + 1], 21, -2054922799);\n    a = md5ii(a, b, c, d, x[i + 8], 6, 1873313359);\n    d = md5ii(d, a, b, c, x[i + 15], 10, -30611744);\n    c = md5ii(c, d, a, b, x[i + 6], 15, -1560198380);\n    b = md5ii(b, c, d, a, x[i + 13], 21, 1309151649);\n    a = md5ii(a, b, c, d, x[i + 4], 6, -145523070);\n    d = md5ii(d, a, b, c, x[i + 11], 10, -1120210379);\n    c = md5ii(c, d, a, b, x[i + 2], 15, 718787259);\n    b = md5ii(b, c, d, a, x[i + 9], 21, -343485551);\n    a = safeAdd(a, olda);\n    b = safeAdd(b, oldb);\n    c = safeAdd(c, oldc);\n    d = safeAdd(d, oldd);\n  }\n\n  return [a, b, c, d];\n}\n/*\n * Convert an array bytes to an array of little-endian words\n * Characters >255 have their high-byte silently ignored.\n */\n\n\nfunction bytesToWords(input) {\n  if (input.length === 0) {\n    return [];\n  }\n\n  var length8 = input.length * 8;\n  var output = new Uint32Array(getOutputLength(length8));\n\n  for (var i = 0; i < length8; i += 8) {\n    output[i >> 5] |= (input[i / 8] & 0xff) << i % 32;\n  }\n\n  return output;\n}\n/*\n * Add integers, wrapping at 2^32. This uses 16-bit operations internally\n * to work around bugs in some JS interpreters.\n */\n\n\nfunction safeAdd(x, y) {\n  var lsw = (x & 0xffff) + (y & 0xffff);\n  var msw = (x >> 16) + (y >> 16) + (lsw >> 16);\n  return msw << 16 | lsw & 0xffff;\n}\n/*\n * Bitwise rotate a 32-bit number to the left.\n */\n\n\nfunction bitRotateLeft(num, cnt) {\n  return num << cnt | num >>> 32 - cnt;\n}\n/*\n * These functions implement the four basic operations the algorithm uses.\n */\n\n\nfunction md5cmn(q, a, b, x, s, t) {\n  return safeAdd(bitRotateLeft(safeAdd(safeAdd(a, q), safeAdd(x, t)), s), b);\n}\n\nfunction md5ff(a, b, c, d, x, s, t) {\n  return md5cmn(b & c | ~b & d, a, b, x, s, t);\n}\n\nfunction md5gg(a, b, c, d, x, s, t) {\n  return md5cmn(b & d | c & ~d, a, b, x, s, t);\n}\n\nfunction md5hh(a, b, c, d, x, s, t) {\n  return md5cmn(b ^ c ^ d, a, b, x, s, t);\n}\n\nfunction md5ii(a, b, c, d, x, s, t) {\n  return md5cmn(c ^ (b | ~d), a, b, x, s, t);\n}\n\nexport default md5;", "import v35 from './v35.js';\nimport md5 from './md5.js';\nvar v3 = v35('v3', 0x30, md5);\nexport default v3;", "// Adapted from <PERSON>' SHA1 code at\n// http://www.movable-type.co.uk/scripts/sha1.html\nfunction f(s, x, y, z) {\n  switch (s) {\n    case 0:\n      return x & y ^ ~x & z;\n\n    case 1:\n      return x ^ y ^ z;\n\n    case 2:\n      return x & y ^ x & z ^ y & z;\n\n    case 3:\n      return x ^ y ^ z;\n  }\n}\n\nfunction ROTL(x, n) {\n  return x << n | x >>> 32 - n;\n}\n\nfunction sha1(bytes) {\n  var K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];\n  var H = [0x67452301, 0xefcdab89, 0x98badcfe, 0x10325476, 0xc3d2e1f0];\n\n  if (typeof bytes === 'string') {\n    var msg = unescape(encodeURIComponent(bytes)); // UTF8 escape\n\n    bytes = [];\n\n    for (var i = 0; i < msg.length; ++i) {\n      bytes.push(msg.charCodeAt(i));\n    }\n  } else if (!Array.isArray(bytes)) {\n    // Convert Array-like to Array\n    bytes = Array.prototype.slice.call(bytes);\n  }\n\n  bytes.push(0x80);\n  var l = bytes.length / 4 + 2;\n  var N = Math.ceil(l / 16);\n  var M = new Array(N);\n\n  for (var _i = 0; _i < N; ++_i) {\n    var arr = new Uint32Array(16);\n\n    for (var j = 0; j < 16; ++j) {\n      arr[j] = bytes[_i * 64 + j * 4] << 24 | bytes[_i * 64 + j * 4 + 1] << 16 | bytes[_i * 64 + j * 4 + 2] << 8 | bytes[_i * 64 + j * 4 + 3];\n    }\n\n    M[_i] = arr;\n  }\n\n  M[N - 1][14] = (bytes.length - 1) * 8 / Math.pow(2, 32);\n  M[N - 1][14] = Math.floor(M[N - 1][14]);\n  M[N - 1][15] = (bytes.length - 1) * 8 & 0xffffffff;\n\n  for (var _i2 = 0; _i2 < N; ++_i2) {\n    var W = new Uint32Array(80);\n\n    for (var t = 0; t < 16; ++t) {\n      W[t] = M[_i2][t];\n    }\n\n    for (var _t = 16; _t < 80; ++_t) {\n      W[_t] = ROTL(W[_t - 3] ^ W[_t - 8] ^ W[_t - 14] ^ W[_t - 16], 1);\n    }\n\n    var a = H[0];\n    var b = H[1];\n    var c = H[2];\n    var d = H[3];\n    var e = H[4];\n\n    for (var _t2 = 0; _t2 < 80; ++_t2) {\n      var s = Math.floor(_t2 / 20);\n      var T = ROTL(a, 5) + f(s, b, c, d) + e + K[s] + W[_t2] >>> 0;\n      e = d;\n      d = c;\n      c = ROTL(b, 30) >>> 0;\n      b = a;\n      a = T;\n    }\n\n    H[0] = H[0] + a >>> 0;\n    H[1] = H[1] + b >>> 0;\n    H[2] = H[2] + c >>> 0;\n    H[3] = H[3] + d >>> 0;\n    H[4] = H[4] + e >>> 0;\n  }\n\n  return [H[0] >> 24 & 0xff, H[0] >> 16 & 0xff, H[0] >> 8 & 0xff, H[0] & 0xff, H[1] >> 24 & 0xff, H[1] >> 16 & 0xff, H[1] >> 8 & 0xff, H[1] & 0xff, H[2] >> 24 & 0xff, H[2] >> 16 & 0xff, H[2] >> 8 & 0xff, H[2] & 0xff, H[3] >> 24 & 0xff, H[3] >> 16 & 0xff, H[3] >> 8 & 0xff, H[3] & 0xff, H[4] >> 24 & 0xff, H[4] >> 16 & 0xff, H[4] >> 8 & 0xff, H[4] & 0xff];\n}\n\nexport default sha1;", "import v35 from './v35.js';\nimport sha1 from './sha1.js';\nvar v5 = v35('v5', 0x50, sha1);\nexport default v5;", "import rng from './rng.js';\nimport stringify from './stringify.js';\n\nfunction v4(options, buf, offset) {\n  options = options || {};\n  var rnds = options.random || (options.rng || rng)(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (var i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return stringify(rnds);\n}\n\nexport default v4;", "export default '00000000-0000-0000-0000-000000000000';", "import validate from './validate.js';\n\nfunction version(uuid) {\n  if (!validate(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  return parseInt(uuid.substr(14, 1), 16);\n}\n\nexport default version;", "const logLevels = ['debug', 'info', 'warn', 'error', 'none'];\n\n/**\n * A simple logger that writes to stderr.\n */\nfunction commonBasicLogger(options, formatFn) {\n  if (options && options.destination && typeof options.destination !== 'function') {\n    throw new Error('destination for basicLogger was set to a non-function');\n  }\n\n  function toConsole(methodName) {\n    // The global console variable is not guaranteed to be defined at all times in all browsers:\n    // https://www.beyondjava.net/console-log-surprises-with-internet-explorer-11-and-edge\n    return function(line) {\n      if (console && console[methodName]) {\n        console[methodName].call(console, line);\n      }\n    };\n  }\n  const destinations =\n    options && options.destination\n      ? [options.destination, options.destination, options.destination, options.destination]\n      : [toConsole('log'), toConsole('info'), toConsole('warn'), toConsole('error')];\n  const prependLevelToMessage = !!(options && options.destination); // if we're writing to console.warn, etc. we don't need the prefix\n  const prefix =\n    !options || options.prefix === undefined || options.prefix === null ? '[LaunchDarkly] ' : options.prefix;\n\n  let minLevel = 1; // default is 'info'\n  if (options && options.level) {\n    for (let i = 0; i < logLevels.length; i++) {\n      if (logLevels[i] === options.level) {\n        minLevel = i;\n      }\n    }\n  }\n\n  function write(levelIndex, levelName, args) {\n    if (args.length < 1) {\n      return;\n    }\n    let line;\n    const fullPrefix = prependLevelToMessage ? levelName + ': ' + prefix : prefix;\n    if (args.length === 1 || !formatFn) {\n      line = fullPrefix + args[0];\n    } else {\n      const tempArgs = [...args];\n      tempArgs[0] = fullPrefix + tempArgs[0];\n      line = formatFn(...tempArgs);\n    }\n    try {\n      destinations[levelIndex](line);\n    } catch (err) {\n      console &&\n        console.log &&\n        console.log(\"[LaunchDarkly] Configured logger's \" + levelName + ' method threw an exception: ' + err);\n    }\n  }\n\n  const logger = {};\n  for (let i = 0; i < logLevels.length; i++) {\n    const levelName = logLevels[i];\n    if (levelName !== 'none') {\n      if (i < minLevel) {\n        logger[levelName] = () => {};\n      } else {\n        const levelIndex = i;\n        logger[levelName] = function() {\n          // can't use arrow function with \"arguments\"\n          write(levelIndex, levelName, arguments);\n        };\n      }\n    }\n  }\n\n  return logger;\n}\n\nfunction validateLogger(logger) {\n  logLevels.forEach(level => {\n    if (level !== 'none' && (!logger[level] || typeof logger[level] !== 'function')) {\n      throw new Error('Provided logger instance must support logger.' + level + '(...) method');\n      // Note that the SDK normally does not throw exceptions to the application, but that rule\n      // does not apply to LDClient.init() which will throw an exception if the parameters are so\n      // invalid that we cannot proceed with creating the client. An invalid logger meets those\n      // criteria since the SDK calls the logger during nearly all of its operations.\n    }\n  });\n}\n\nmodule.exports = {\n  commonBasicLogger,\n  validateLogger,\n};\n", "const errors = require('./errors');\n\nfunction errorString(err) {\n  if (err && err.message) {\n    return err.message;\n  }\n  if (typeof err === 'string' || err instanceof String) {\n    return err;\n  }\n  return JSON.stringify(err);\n}\n\nconst clientInitialized = function() {\n  return 'LaunchDarkly client initialized';\n};\n\nconst docLink =\n  ' Please see https://docs.launchdarkly.com/sdk/client-side/javascript#initialize-the-client for instructions on SDK initialization.';\n\nconst clientNotReady = function() {\n  return 'LaunchDarkly client is not ready';\n};\n\nconst eventCapacityExceeded = function() {\n  return 'Exceeded event queue capacity. Increase capacity to avoid dropping events.';\n};\n\nconst eventWithoutContext = function() {\n  return 'Be sure to call `identify` in the LaunchDarkly client: https://docs.launchdarkly.com/sdk/features/identify#javascript';\n};\n\nconst invalidContentType = function(contentType) {\n  return 'Expected application/json content type but got \"' + contentType + '\"';\n};\n\nconst invalidKey = function() {\n  return 'Event key must be a string';\n};\n\nconst localStorageUnavailable = function(err) {\n  return 'local storage is unavailable: ' + errorString(err);\n};\n\nconst networkError = e => 'network error' + (e ? ' (' + e + ')' : '');\n\n// We should remove unknownCustomEventKey in the future - see comments in track() in index.js\nconst unknownCustomEventKey = function(key) {\n  return 'Custom event \"' + key + '\" does not exist';\n};\n\nconst environmentNotFound = function() {\n  return 'Environment not found. Double check that you specified a valid environment/client-side ID.' + docLink;\n};\n\nconst environmentNotSpecified = function() {\n  return 'No environment/client-side ID was specified.' + docLink;\n};\n\nconst errorFetchingFlags = function(err) {\n  return 'Error fetching flag settings: ' + errorString(err);\n};\n\nconst contextNotSpecified = function() {\n  return 'No context specified.' + docLink;\n};\n\nconst invalidContext = function() {\n  return 'Invalid context specified.' + docLink;\n};\n\nconst invalidData = function() {\n  return 'Invalid data received from LaunchDarkly; connection may have been interrupted';\n};\n\nconst bootstrapOldFormat = function() {\n  return (\n    'LaunchDarkly client was initialized with bootstrap data that did not include flag metadata. ' +\n    'Events may not be sent correctly.' +\n    docLink\n  );\n};\n\nconst bootstrapInvalid = function() {\n  return 'LaunchDarkly bootstrap data is not available because the back end could not read the flags.';\n};\n\nconst deprecated = function(oldName, newName) {\n  if (newName) {\n    return '\"' + oldName + '\" is deprecated, please use \"' + newName + '\"';\n  }\n  return '\"' + oldName + '\" is deprecated';\n};\n\nconst httpErrorMessage = function(status, context, retryMessage) {\n  return (\n    'Received error ' +\n    status +\n    (status === 401 ? ' (invalid SDK key)' : '') +\n    ' for ' +\n    context +\n    ' - ' +\n    (errors.isHttpErrorRecoverable(status) ? retryMessage : 'giving up permanently')\n  );\n};\n\nconst httpUnavailable = function() {\n  return 'Cannot make HTTP requests in this environment.' + docLink;\n};\n\nconst identifyDisabled = function() {\n  return 'identify() has no effect here; it must be called on the main client instance';\n};\n\nconst streamClosing = function() {\n  return 'Closing stream connection';\n};\n\nconst streamConnecting = function(url) {\n  return 'Opening stream connection to ' + url;\n};\n\nconst streamError = function(err, streamReconnectDelay) {\n  return (\n    'Error on stream connection: ' +\n    errorString(err) +\n    ', will continue retrying after ' +\n    streamReconnectDelay +\n    ' milliseconds.'\n  );\n};\n\nconst unknownOption = name => 'Ignoring unknown config option \"' + name + '\"';\n\nconst unrecoverableStreamError = err => `Error on stream connection ${errorString(err)}, giving up permanently`;\n\nconst wrongOptionType = (name, expectedType, actualType) =>\n  'Config option \"' + name + '\" should be of type ' + expectedType + ', got ' + actualType + ', using default value';\n\nconst wrongOptionTypeBoolean = (name, actualType) =>\n  'Config option \"' + name + '\" should be a boolean, got ' + actualType + ', converting to boolean';\n\nconst optionBelowMinimum = (name, value, minimum) =>\n  'Config option \"' + name + '\" was set to ' + value + ', changing to minimum value of ' + minimum;\n\nconst debugPolling = function(url) {\n  return 'polling for feature flags at ' + url;\n};\n\nconst debugStreamPing = function() {\n  return 'received ping message from stream';\n};\n\nconst debugStreamPut = function() {\n  return 'received streaming update for all flags';\n};\n\nconst debugStreamPatch = function(key) {\n  return 'received streaming update for flag \"' + key + '\"';\n};\n\nconst debugStreamPatchIgnored = function(key) {\n  return 'received streaming update for flag \"' + key + '\" but ignored due to version check';\n};\n\nconst debugStreamDelete = function(key) {\n  return 'received streaming deletion for flag \"' + key + '\"';\n};\n\nconst debugStreamDeleteIgnored = function(key) {\n  return 'received streaming deletion for flag \"' + key + '\" but ignored due to version check';\n};\n\nconst debugEnqueueingEvent = function(kind) {\n  return 'enqueueing \"' + kind + '\" event';\n};\n\nconst debugPostingEvents = function(count) {\n  return 'sending ' + count + ' events';\n};\n\nconst debugPostingDiagnosticEvent = function(event) {\n  return 'sending diagnostic event (' + event.kind + ')';\n};\n\nconst invalidInspector = (type, name) => `an inspector: \"${name}\" of an invalid type (${type}) was configured`;\n\nconst inspectorMethodError = (type, name) => `an inspector: \"${name}\" of type: \"${type}\" generated an exception`;\n\nconst invalidTagValue = name => `Config option \"${name}\" must only contain letters, numbers, ., _ or -.`;\n\nconst tagValueTooLong = name => `Value of \"${name}\" was longer than 64 characters and was discarded.`;\n\nconst invalidMetricValue = badType =>\n  `The track function was called with a non-numeric \"metricValue\" (${badType}), only numeric metric values are supported.`;\n\nmodule.exports = {\n  bootstrapInvalid,\n  bootstrapOldFormat,\n  clientInitialized,\n  clientNotReady,\n  debugEnqueueingEvent,\n  debugPostingDiagnosticEvent,\n  debugPostingEvents,\n  debugStreamDelete,\n  debugStreamDeleteIgnored,\n  debugStreamPatch,\n  debugStreamPatchIgnored,\n  debugStreamPing,\n  debugPolling,\n  debugStreamPut,\n  deprecated,\n  environmentNotFound,\n  environmentNotSpecified,\n  errorFetchingFlags,\n  eventCapacityExceeded,\n  eventWithoutContext,\n  httpErrorMessage,\n  httpUnavailable,\n  identifyDisabled,\n  inspectorMethodError,\n  invalidContentType,\n  invalidData,\n  invalidInspector,\n  invalidKey,\n  invalidMetricValue,\n  invalidContext,\n  invalidTagValue,\n  localStorageUnavailable,\n  networkError,\n  optionBelowMinimum,\n  streamClosing,\n  streamConnecting,\n  streamError,\n  tagValueTooLong,\n  unknownCustomEventKey,\n  unknownOption,\n  contextNotSpecified,\n  unrecoverableStreamError,\n  wrongOptionType,\n  wrongOptionTypeBoolean,\n};\n", "const errors = require('./errors');\nconst { validateLogger } = require('./loggers');\nconst messages = require('./messages');\nconst utils = require('./utils');\n\n// baseOptionDefs should contain an entry for each supported configuration option in the common package.\n// Each entry can have three properties:\n// - \"default\": the default value if any\n// - \"type\": a type constraint used if the type can't be inferred from the default value). The allowable\n//   values are \"boolean\", \"string\", \"number\", \"array\", \"object\", \"function\", or several of these OR'd\n//   together with \"|\" (\"function|object\").\n// - \"minimum\": minimum value if any for numeric properties\n//\n// The extraOptionDefs parameter to validate() uses the same format.\nconst baseOptionDefs = {\n  baseUrl: { default: 'https://app.launchdarkly.com' },\n  streamUrl: { default: 'https://clientstream.launchdarkly.com' },\n  eventsUrl: { default: 'https://events.launchdarkly.com' },\n  sendEvents: { default: true },\n  streaming: { type: 'boolean' }, // default for this is undefined, which is different from false\n  sendLDHeaders: { default: true },\n  requestHeaderTransform: { type: 'function' },\n  sendEventsOnlyForVariation: { default: false },\n  useReport: { default: false },\n  evaluationReasons: { default: false },\n  eventCapacity: { default: 100, minimum: 1 },\n  flushInterval: { default: 2000, minimum: 2000 },\n  samplingInterval: { default: 0, minimum: 0 },\n  streamReconnectDelay: { default: 1000, minimum: 0 },\n  allAttributesPrivate: { default: false },\n  privateAttributes: { default: [] },\n  bootstrap: { type: 'string|object' },\n  diagnosticRecordingInterval: { default: 900000, minimum: 2000 },\n  diagnosticOptOut: { default: false },\n  wrapperName: { type: 'string' },\n  wrapperVersion: { type: 'string' },\n  stateProvider: { type: 'object' }, // not a public option, used internally\n  application: { validator: applicationConfigValidator },\n  inspectors: { default: [] },\n  hooks: { default: [] },\n  plugins: { default: [] },\n};\n\n/**\n * Expression to validate characters that are allowed in tag keys and values.\n */\nconst allowedTagCharacters = /^(\\w|\\.|-)+$/;\n\nfunction canonicalizeUrl(url) {\n  return url && url.replace(/\\/+$/, '');\n}\n\n/**\n * Verify that a value meets the requirements for a tag value.\n * @param {string} tagValue\n * @param {Object} logger\n */\nfunction validateTagValue(name, tagValue, logger) {\n  if (typeof tagValue !== 'string' || !tagValue.match(allowedTagCharacters)) {\n    logger.warn(messages.invalidTagValue(name));\n    return undefined;\n  }\n  if (tagValue.length > 64) {\n    logger.warn(messages.tagValueTooLong(name));\n    return undefined;\n  }\n  return tagValue;\n}\n\nfunction applicationConfigValidator(name, value, logger) {\n  const validated = {};\n  if (value.id) {\n    validated.id = validateTagValue(`${name}.id`, value.id, logger);\n  }\n  if (value.version) {\n    validated.version = validateTagValue(`${name}.version`, value.version, logger);\n  }\n  return validated;\n}\n\nfunction validate(options, emitter, extraOptionDefs, logger) {\n  const optionDefs = utils.extend({ logger: { default: logger } }, baseOptionDefs, extraOptionDefs);\n\n  const deprecatedOptions = {\n    // As of the latest major version, there are no deprecated options. Next time we deprecate\n    // something, add an item here where the property name is the deprecated name, and the\n    // property value is the preferred name if any, or null/undefined if there is no replacement.\n  };\n\n  function checkDeprecatedOptions(config) {\n    const opts = config;\n    Object.keys(deprecatedOptions).forEach(oldName => {\n      if (opts[oldName] !== undefined) {\n        const newName = deprecatedOptions[oldName];\n        logger && logger.warn(messages.deprecated(oldName, newName));\n        if (newName) {\n          if (opts[newName] === undefined) {\n            opts[newName] = opts[oldName];\n          }\n          delete opts[oldName];\n        }\n      }\n    });\n  }\n\n  function applyDefaults(config) {\n    // This works differently from utils.extend() in that it *will not* override a default value\n    // if the provided value is explicitly set to null. This provides backward compatibility\n    // since in the past we only used the provided values if they were truthy.\n    const ret = utils.extend({}, config);\n    Object.keys(optionDefs).forEach(name => {\n      if (ret[name] === undefined || ret[name] === null) {\n        ret[name] = optionDefs[name] && optionDefs[name].default;\n      }\n    });\n    return ret;\n  }\n\n  function validateTypesAndNames(config) {\n    const ret = utils.extend({}, config);\n    const typeDescForValue = value => {\n      if (value === null) {\n        return 'any';\n      }\n      if (value === undefined) {\n        return undefined;\n      }\n      if (Array.isArray(value)) {\n        return 'array';\n      }\n      const t = typeof value;\n      if (t === 'boolean' || t === 'string' || t === 'number' || t === 'function') {\n        return t;\n      }\n      return 'object';\n    };\n    Object.keys(config).forEach(name => {\n      const value = config[name];\n      if (value !== null && value !== undefined) {\n        const optionDef = optionDefs[name];\n        if (optionDef === undefined) {\n          reportArgumentError(messages.unknownOption(name));\n        } else {\n          const expectedType = optionDef.type || typeDescForValue(optionDef.default);\n          const validator = optionDef.validator;\n          if (validator) {\n            const validated = validator(name, config[name], logger);\n            if (validated !== undefined) {\n              ret[name] = validated;\n            } else {\n              delete ret[name];\n            }\n          } else if (expectedType !== 'any') {\n            const allowedTypes = expectedType.split('|');\n            const actualType = typeDescForValue(value);\n            if (allowedTypes.indexOf(actualType) < 0) {\n              if (expectedType === 'boolean') {\n                ret[name] = !!value;\n                reportArgumentError(messages.wrongOptionTypeBoolean(name, actualType));\n              } else {\n                reportArgumentError(messages.wrongOptionType(name, expectedType, actualType));\n                ret[name] = optionDef.default;\n              }\n            } else {\n              if (actualType === 'number' && optionDef.minimum !== undefined && value < optionDef.minimum) {\n                reportArgumentError(messages.optionBelowMinimum(name, value, optionDef.minimum));\n                ret[name] = optionDef.minimum;\n              }\n            }\n          }\n        }\n      }\n    });\n\n    ret.baseUrl = canonicalizeUrl(ret.baseUrl);\n    ret.streamUrl = canonicalizeUrl(ret.streamUrl);\n    ret.eventsUrl = canonicalizeUrl(ret.eventsUrl);\n\n    return ret;\n  }\n\n  function reportArgumentError(message) {\n    utils.onNextTick(() => {\n      emitter && emitter.maybeReportError(new errors.LDInvalidArgumentError(message));\n    });\n  }\n\n  let config = utils.extend({}, options || {});\n\n  checkDeprecatedOptions(config);\n\n  config = applyDefaults(config);\n  config = validateTypesAndNames(config);\n  validateLogger(config.logger);\n\n  return config;\n}\n\n/**\n * Get tags for the specified configuration.\n *\n * If any additional tags are added to the configuration, then the tags from\n * this method should be extended with those.\n * @param {Object} config The already valiated configuration.\n * @returns {Object} The tag configuration.\n */\nfunction getTags(config) {\n  const tags = {};\n  if (config) {\n    if (config.application && config.application.id !== undefined && config.application.id !== null) {\n      tags['application-id'] = [config.application.id];\n    }\n    if (config.application && config.application.version !== undefined && config.application.id !== null) {\n      tags['application-version'] = [config.application.version];\n    }\n  }\n\n  return tags;\n}\n\nmodule.exports = {\n  baseOptionDefs,\n  validate,\n  getTags,\n};\n", "const { getLDUserAgentString } = require('./utils');\nconst configuration = require('./configuration');\n\nfunction getLDHeaders(platform, options) {\n  if (options && !options.sendLDHeaders) {\n    return {};\n  }\n  const h = {};\n  h[platform.userAgentHeaderName || 'User-Agent'] = getLDUserAgentString(platform);\n  if (options && options.wrapperName) {\n    h['X-LaunchDarkly-Wrapper'] = options.wrapperVersion\n      ? options.wrapperName + '/' + options.wrapperVersion\n      : options.wrapperName;\n  }\n  const tags = configuration.getTags(options);\n  const tagKeys = Object.keys(tags);\n  if (tagKeys.length) {\n    h['x-launchdarkly-tags'] = tagKeys\n      .sort()\n      .map(key =>\n        Array.isArray(tags[key]) ? tags[key].sort().map(value => `${key}/${value}`) : [`${key}/${tags[key]}`]\n      )\n      .reduce((flattened, item) => flattened.concat(item), [])\n      .join(' ');\n  }\n  return h;\n}\n\nfunction transformHeaders(headers, options) {\n  if (!options || !options.requestHeaderTransform) {\n    return headers;\n  }\n  return options.requestHeaderTransform({ ...headers });\n}\n\nmodule.exports = {\n  getLDHeaders,\n  transformHeaders,\n};\n", "const errors = require('./errors');\nconst utils = require('./utils');\nconst { v1: uuidv1 } = require('uuid');\nconst { getLDHeaders, transformHeaders } = require('./headers');\n\nfunction EventSender(platform, environmentId, options) {\n  const baseHeaders = utils.extend({ 'Content-Type': 'application/json' }, getLDHeaders(platform, options));\n  const sender = {};\n\n  function getResponseInfo(result) {\n    const ret = { status: result.status };\n    const dateStr = result.header('date');\n    if (dateStr) {\n      const time = Date.parse(dateStr);\n      if (time) {\n        ret.serverTime = time;\n      }\n    }\n    return ret;\n  }\n\n  sender.sendEvents = (events, url, isDiagnostic) => {\n    if (!platform.httpRequest) {\n      return Promise.resolve();\n    }\n\n    const jsonBody = JSON.stringify(events);\n    const payloadId = isDiagnostic ? null : uuidv1();\n\n    function doPostRequest(canRetry) {\n      const headers = isDiagnostic\n        ? baseHeaders\n        : utils.extend({}, baseHeaders, {\n            'X-LaunchDarkly-Event-Schema': '4',\n            'X-LaunchDarkly-Payload-ID': payloadId,\n          });\n      return platform\n        .httpRequest('POST', url, transformHeaders(headers, options), jsonBody)\n        .promise.then(result => {\n          if (!result) {\n            // This was a response from a fire-and-forget request, so we won't have a status.\n            return;\n          }\n          if (result.status >= 400 && errors.isHttpErrorRecoverable(result.status) && canRetry) {\n            return doPostRequest(false);\n          } else {\n            return getResponseInfo(result);\n          }\n        })\n        .catch(() => {\n          if (canRetry) {\n            return doPostRequest(false);\n          }\n          return Promise.reject();\n        });\n    }\n\n    return doPostRequest(true).catch(() => {});\n  };\n\n  return sender;\n}\n\nmodule.exports = EventSender;\n", "/**\n * Given some object to serialize product a canonicalized JSON string.\n * https://www.rfc-editor.org/rfc/rfc8785.html\n *\n * We do not support custom toJSON methods on objects. Objects should be limited to basic types.\n *\n * @param {any} object The object to serialize.\n * @param {any[]?} visited The list of objects that have already been visited to avoid cycles.\n * @returns {string} The canonicalized JSON string.\n */\nfunction canonicalize(object, visited = []) {\n  // For JavaScript the default JSON serialization will produce canonicalized output for basic types.\n  if (object === null || typeof object !== 'object') {\n    return JSON.stringify(object);\n  }\n\n  if (visited.includes(object)) {\n    throw new Error('Cycle detected');\n  }\n\n  if (Array.isArray(object)) {\n    const values = object\n      .map(item => canonicalize(item, [...visited, object]))\n      .map(item => (item === undefined ? 'null' : item));\n    return `[${values.join(',')}]`;\n  }\n\n  const values = Object.keys(object)\n    .sort()\n    .map(key => {\n      const value = canonicalize(object[key], [...visited, object]);\n      if (value !== undefined) {\n        return `${JSON.stringify(key)}:${value}`;\n      }\n      return undefined;\n    })\n    .filter(item => item !== undefined);\n  return `{${values.join(',')}}`;\n}\n\nmodule.exports = canonicalize;\n", "const { commonBasicLogger } = require('./loggers');\n\n/**\n * Validate a context kind.\n * @param {string} kind\n * @returns true if the kind is valid.\n */\nfunction validKind(kind) {\n  return typeof kind === 'string' && kind !== 'kind' && kind.match(/^(\\w|\\.|-)+$/);\n}\n\n/**\n * Perform a check of basic context requirements.\n * @param {Object} context\n * @param {boolean} allowLegacyKey If true, then a legacy user can have an\n * empty or non-string key. A legacy user is a context without a kind.\n * @returns true if the context meets basic requirements.\n */\nfunction checkContext(context, allowLegacyKey) {\n  if (context) {\n    if (allowLegacyKey && (context.kind === undefined || context.kind === null)) {\n      return context.key !== undefined && context.key !== null;\n    }\n    const key = context.key;\n    const kind = context.kind === undefined ? 'user' : context.kind;\n    const kindValid = validKind(kind);\n    const keyValid = kind === 'multi' || (key !== undefined && key !== null && key !== '');\n    if (kind === 'multi') {\n      const kinds = Object.keys(context).filter(key => key !== 'kind');\n      return (\n        keyValid &&\n        kinds.every(key => validKind(key)) &&\n        kinds.every(key => {\n          const contextKey = context[key].key;\n          return contextKey !== undefined && contextKey !== null && contextKey !== '';\n        })\n      );\n    }\n    return keyValid && kindValid;\n  }\n  return false;\n}\n\n/**\n * For a given context get a list of context kinds.\n * @param {Object} context\n * @returns {string[]} A list of kinds in the context.\n */\nfunction getContextKinds(context) {\n  if (context) {\n    if (context.kind === null || context.kind === undefined) {\n      return ['user'];\n    }\n    if (context.kind !== 'multi') {\n      return [context.kind];\n    }\n    return Object.keys(context).filter(kind => kind !== 'kind');\n  }\n  return [];\n}\n\n/**\n * The partial URL encoding is needed because : is a valid character in context keys.\n *\n * Partial encoding is the replacement of all colon (:) characters with the URL\n * encoded equivalent (%3A) and all percent (%) characters with the URL encoded\n * equivalent (%25).\n * @param {string} key The key to encode.\n * @returns {string} Partially URL encoded key.\n */\nfunction encodeKey(key) {\n  if (key.includes('%') || key.includes(':')) {\n    return key.replace(/%/g, '%25').replace(/:/g, '%3A');\n  }\n  return key;\n}\n\nfunction getCanonicalKey(context) {\n  if (context) {\n    if ((context.kind === undefined || context.kind === null || context.kind === 'user') && context.key) {\n      return context.key;\n    } else if (context.kind !== 'multi' && context.key) {\n      return `${context.kind}:${encodeKey(context.key)}`;\n    } else if (context.kind === 'multi') {\n      return Object.keys(context)\n        .sort()\n        .filter(key => key !== 'kind')\n        .map(key => `${key}:${encodeKey(context[key].key)}`)\n        .join(':');\n    }\n  }\n}\n\nfunction getContextKeys(context, logger = commonBasicLogger()) {\n  if (!context) {\n    return undefined;\n  }\n\n  const keys = {};\n  const { kind, key } = context;\n\n  switch (kind) {\n    case undefined:\n      keys.user = `${key}`;\n      break;\n    case 'multi':\n      Object.entries(context)\n        .filter(([key]) => key !== 'kind')\n        .forEach(([key, value]) => {\n          if (value && value.key) {\n            keys[key] = value.key;\n          }\n        });\n      break;\n    case null:\n      logger.warn(`null is not a valid context kind: ${context}`);\n      break;\n    case '':\n      logger.warn(`'' is not a valid context kind: ${context}`);\n      break;\n    default:\n      keys[kind] = `${key}`;\n      break;\n  }\n\n  return keys;\n}\n\nmodule.exports = {\n  checkContext,\n  getContextKeys,\n  getContextKinds,\n  getCanonicalKey,\n};\n", "const { getContextKinds } = require('./context');\n\nfunction getKinds(event) {\n  if (event.context) {\n    return getContextKinds(event.context);\n  }\n  if (event.contextKeys) {\n    return Object.keys(event.contextKeys);\n  }\n  return [];\n}\n\nfunction EventSummarizer() {\n  const es = {};\n\n  let startDate = 0,\n    endDate = 0,\n    counters = {},\n    contextKinds = {};\n\n  es.summarizeEvent = event => {\n    if (event.kind === 'feature') {\n      const counterKey =\n        event.key +\n        ':' +\n        (event.variation !== null && event.variation !== undefined ? event.variation : '') +\n        ':' +\n        (event.version !== null && event.version !== undefined ? event.version : '');\n      const counterVal = counters[counterKey];\n      let kinds = contextKinds[event.key];\n      if (!kinds) {\n        kinds = new Set();\n        contextKinds[event.key] = kinds;\n      }\n      getKinds(event).forEach(kind => kinds.add(kind));\n\n      if (counterVal) {\n        counterVal.count = counterVal.count + 1;\n      } else {\n        counters[counterKey] = {\n          count: 1,\n          key: event.key,\n          version: event.version,\n          variation: event.variation,\n          value: event.value,\n          default: event.default,\n        };\n      }\n      if (startDate === 0 || event.creationDate < startDate) {\n        startDate = event.creationDate;\n      }\n      if (event.creationDate > endDate) {\n        endDate = event.creationDate;\n      }\n    }\n  };\n\n  es.getSummary = () => {\n    const flagsOut = {};\n    let empty = true;\n    for (const c of Object.values(counters)) {\n      let flag = flagsOut[c.key];\n      if (!flag) {\n        flag = {\n          default: c.default,\n          counters: [],\n          contextKinds: [...contextKinds[c.key]],\n        };\n        flagsOut[c.key] = flag;\n      }\n      const counterOut = {\n        value: c.value,\n        count: c.count,\n      };\n      if (c.variation !== undefined && c.variation !== null) {\n        counterOut.variation = c.variation;\n      }\n      if (c.version !== undefined && c.version !== null) {\n        counterOut.version = c.version;\n      } else {\n        counterOut.unknown = true;\n      }\n      flag.counters.push(counterOut);\n      empty = false;\n    }\n    return empty\n      ? null\n      : {\n          startDate,\n          endDate,\n          features: flagsOut,\n          kind: 'summary',\n        };\n  };\n\n  es.clearSummary = () => {\n    startDate = 0;\n    endDate = 0;\n    counters = {};\n    contextKinds = {};\n  };\n\n  return es;\n}\n\nmodule.exports = EventSummarizer;\n", "const canonicalize = require('./canonicalize');\nconst EventSummarizer = require('./EventSummarizer');\n\n/**\n * Construct a multi-event summarizer. This summarizer produces a summary event for each unique context.\n * @param {{filter: (context: any) => any}} contextFilter\n */\nfunction MultiEventSummarizer(contextFilter) {\n  let summarizers = {};\n  let contexts = {};\n\n  /**\n   * Summarize the given event.\n   * @param {{\n   *   kind: string,\n   *   context?: any,\n   * }} event\n   */\n  function summarizeEvent(event) {\n    if (event.kind === 'feature') {\n      const key = canonicalize(event.context);\n      if (!key) {\n        return;\n      }\n\n      let summarizer = summarizers[key];\n      if (!summarizer) {\n        summarizers[key] = EventSummarizer();\n        summarizer = summarizers[key];\n        contexts[key] = event.context;\n      }\n\n      summarizer.summarizeEvent(event);\n    }\n  }\n\n  /**\n   * Get the summaries of the events that have been summarized.\n   * @returns {any[]}\n   */\n  function getSummaries() {\n    const summarizersToFlush = summarizers;\n    const contextsForSummaries = contexts;\n\n    summarizers = {};\n    contexts = {};\n    return Object.entries(summarizersToFlush).map(([key, summarizer]) => {\n      const summary = summarizer.getSummary();\n      summary.context = contextFilter.filter(contextsForSummaries[key]);\n      return summary;\n    });\n  }\n\n  return {\n    summarizeEvent,\n    getSummaries,\n  };\n}\n\nmodule.exports = MultiEventSummarizer;\n", "/**\n * Take a key string and escape the characters to allow it to be used as a reference.\n * @param {string} key\n * @returns {string} The processed key.\n */\nfunction processEscapeCharacters(key) {\n  return key.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n\n/**\n * @param {string} reference The reference to get the components of.\n * @returns {string[]} The components of the reference. Escape characters will be converted to their representative values.\n */\nfunction getComponents(reference) {\n  const referenceWithoutPrefix = reference.startsWith('/') ? reference.substring(1) : reference;\n  return referenceWithoutPrefix\n    .split('/')\n    .map(component => (component.indexOf('~') >= 0 ? component.replace(/~1/g, '/').replace(/~0/g, '~') : component));\n}\n\n/**\n * @param {string} reference The reference to check if it is a literal.\n * @returns true if the reference is a literal.\n */\nfunction isLiteral(reference) {\n  return !reference.startsWith('/');\n}\n\n/**\n * Compare two references and determine if they are equivalent.\n * @param {string} a\n * @param {string} b\n */\nfunction compare(a, b) {\n  const aIsLiteral = isLiteral(a);\n  const bIsLiteral = isLiteral(b);\n  if (aIsLiteral && bIsLiteral) {\n    return a === b;\n  }\n  if (aIsLiteral) {\n    const bComponents = getComponents(b);\n    if (bComponents.length !== 1) {\n      return false;\n    }\n    return a === bComponents[0];\n  }\n  if (bIsLiteral) {\n    const aComponents = getComponents(a);\n    if (aComponents.length !== 1) {\n      return false;\n    }\n    return b === aComponents[0];\n  }\n  return a === b;\n}\n\n/**\n * @param {string} a\n * @param {string} b\n * @returns The two strings joined by '/'.\n */\nfunction join(a, b) {\n  return `${a}/${b}`;\n}\n\n/**\n * There are cases where a field could have been named with a preceeding '/'.\n * If that attribute was private, then the literal would appear to be a reference.\n * This method can be used to convert a literal to a reference in such situations.\n * @param {string} literal The literal to convert to a reference.\n * @returns A literal which has been converted to a reference.\n */\nfunction literalToReference(literal) {\n  return `/${processEscapeCharacters(literal)}`;\n}\n\n/**\n * Clone an object excluding the values referenced by a list of references.\n * @param {Object} target The object to clone.\n * @param {string[]} references A list of references from the cloned object.\n * @returns {{cloned: Object, excluded: string[]}} The cloned object and a list of excluded values.\n */\nfunction cloneExcluding(target, references) {\n  const stack = [];\n  const cloned = {};\n  const excluded = [];\n\n  stack.push(\n    ...Object.keys(target).map(key => ({\n      key,\n      ptr: literalToReference(key),\n      source: target,\n      parent: cloned,\n      visited: [target],\n    }))\n  );\n\n  while (stack.length) {\n    const item = stack.pop();\n    if (!references.some(ptr => compare(ptr, item.ptr))) {\n      const value = item.source[item.key];\n\n      // Handle null because it overlaps with object, which we will want to handle later.\n      if (value === null) {\n        item.parent[item.key] = value;\n      } else if (Array.isArray(value)) {\n        item.parent[item.key] = [...value];\n      } else if (typeof value === 'object') {\n        //Arrays and null must already be handled.\n\n        //Prevent cycles by not visiting the same object\n        //with in the same branch. Parallel branches\n        //may contain the same object.\n        if (item.visited.includes(value)) {\n          continue;\n        }\n\n        item.parent[item.key] = {};\n\n        stack.push(\n          ...Object.keys(value).map(key => ({\n            key,\n            ptr: join(item.ptr, processEscapeCharacters(key)),\n            source: value,\n            parent: item.parent[item.key],\n            visited: [...item.visited, value],\n          }))\n        );\n      } else {\n        item.parent[item.key] = value;\n      }\n    } else {\n      excluded.push(item.ptr);\n    }\n  }\n  return { cloned, excluded: excluded.sort() };\n}\n\nmodule.exports = {\n  cloneExcluding,\n  compare,\n  literalToReference,\n};\n", "const AttributeReference = require('./attributeReference');\n\nfunction ContextFilter(config) {\n  const filter = {};\n\n  const allAttributesPrivate = config.allAttributesPrivate;\n  const privateAttributes = config.privateAttributes || [];\n\n  // These attributes cannot be removed via a private attribute.\n  const protectedAttributes = ['key', 'kind', '_meta', 'anonymous'];\n\n  const legacyTopLevelCopyAttributes = ['name', 'ip', 'firstName', 'lastName', 'email', 'avatar', 'country'];\n\n  /**\n   * For the given context and configuration get a list of attributes to filter.\n   * @param {Object} context\n   * @returns {string[]} A list of the attributes to filter.\n   */\n  const getAttributesToFilter = (context, redactAnonymous) =>\n    (allAttributesPrivate || (redactAnonymous && context.anonymous)\n      ? Object.keys(context)\n      : [...privateAttributes, ...((context._meta && context._meta.privateAttributes) || [])]\n    ).filter(attr => !protectedAttributes.some(protectedAttr => AttributeReference.compare(attr, protectedAttr)));\n\n  /**\n   * @param {Object} context\n   * @param {boolean} redactAnonymous\n   * @returns {Object} A copy of the context with private attributes removed,\n   * and the redactedAttributes meta populated.\n   */\n  const filterSingleKind = (context, redactAnonymous) => {\n    if (typeof context !== 'object' || context === null || Array.isArray(context)) {\n      return undefined;\n    }\n\n    const { cloned, excluded } = AttributeReference.cloneExcluding(\n      context,\n      getAttributesToFilter(context, redactAnonymous)\n    );\n    cloned.key = String(cloned.key);\n    if (excluded.length) {\n      if (!cloned._meta) {\n        cloned._meta = {};\n      }\n      cloned._meta.redactedAttributes = excluded;\n    }\n    if (cloned._meta) {\n      delete cloned._meta['privateAttributes'];\n      if (Object.keys(cloned._meta).length === 0) {\n        delete cloned._meta;\n      }\n    }\n    // Make sure anonymous is boolean if present.\n    // Null counts as present, and would be falsy, which is the default.\n    if (cloned.anonymous !== undefined) {\n      cloned.anonymous = !!cloned.anonymous;\n    }\n\n    return cloned;\n  };\n\n  /**\n   * @param {Object} context\n   * @param {boolean} redactAnonymous\n   * @returns {Object} A copy of the context with the private attributes removed,\n   * and the redactedAttributes meta populated for each sub-context.\n   */\n  const filterMultiKind = (context, redactAnonymous) => {\n    const filtered = {\n      kind: context.kind,\n    };\n    const contextKeys = Object.keys(context);\n\n    for (const contextKey of contextKeys) {\n      if (contextKey !== 'kind') {\n        const filteredContext = filterSingleKind(context[contextKey], redactAnonymous);\n        if (filteredContext) {\n          filtered[contextKey] = filteredContext;\n        }\n      }\n    }\n    return filtered;\n  };\n\n  /**\n   * Convert the LDUser object into an LDContext object.\n   * @param {Object} user The LDUser to produce an LDContext for.\n   * @returns {Object} A single kind context based on the provided user.\n   */\n  const legacyToSingleKind = user => {\n    const filtered = {\n      /* Destructure custom items into the top level.\n         Duplicate keys will be overridden by previously\n         top level items.\n      */\n      ...(user.custom || {}),\n\n      // Implicity a user kind.\n      kind: 'user',\n\n      key: user.key,\n    };\n\n    if (user.anonymous !== undefined) {\n      filtered.anonymous = !!user.anonymous;\n    }\n\n    // Copy top level keys and convert them to strings.\n    // Remove keys that may have been destructured from `custom`.\n    for (const key of legacyTopLevelCopyAttributes) {\n      delete filtered[key];\n      if (user[key] !== undefined && user[key] !== null) {\n        filtered[key] = String(user[key]);\n      }\n    }\n\n    if (user.privateAttributeNames !== undefined && user.privateAttributeNames !== null) {\n      filtered._meta = filtered._meta || {};\n      // If any private attributes started with '/' we need to convert them to references, otherwise the '/' will\n      // cause the literal to incorrectly be treated as a reference.\n      filtered._meta.privateAttributes = user.privateAttributeNames.map(literal =>\n        literal.startsWith('/') ? AttributeReference.literalToReference(literal) : literal\n      );\n    }\n\n    return filtered;\n  };\n\n  filter.filter = (context, redactAnonymous = false) => {\n    if (context.kind === undefined || context.kind === null) {\n      return filterSingleKind(legacyToSingleKind(context), redactAnonymous);\n    } else if (context.kind === 'multi') {\n      return filterMultiKind(context, redactAnonymous);\n    } else {\n      return filterSingleKind(context, redactAnonymous);\n    }\n  };\n\n  return filter;\n}\n\nmodule.exports = ContextFilter;\n", "const EventSender = require('./EventSender');\nconst MultiEventSummarizer = require('./MultiEventSummarizer');\nconst ContextFilter = require('./ContextFilter');\nconst errors = require('./errors');\nconst messages = require('./messages');\nconst utils = require('./utils');\nconst { getContextKeys } = require('./context');\n\nfunction EventProcessor(\n  platform,\n  options,\n  environmentId,\n  diagnosticsAccumulator = null,\n  emitter = null,\n  sender = null\n) {\n  const processor = {};\n  const eventSender = sender || EventSender(platform, environmentId, options);\n  const mainEventsUrl = utils.appendUrlPath(options.eventsUrl, '/events/bulk/' + environmentId);\n  const contextFilter = ContextFilter(options);\n  const summarizer = MultiEventSummarizer(contextFilter);\n  const samplingInterval = options.samplingInterval;\n  const eventCapacity = options.eventCapacity;\n  const flushInterval = options.flushInterval;\n  const logger = options.logger;\n  let queue = [];\n  let lastKnownPastTime = 0;\n  let disabled = false;\n  let exceededCapacity = false;\n  let flushTimer;\n\n  function shouldSampleEvent() {\n    return samplingInterval === 0 || Math.floor(Math.random() * samplingInterval) === 0;\n  }\n\n  function shouldDebugEvent(e) {\n    if (e.debugEventsUntilDate) {\n      // The \"last known past time\" comes from the last HTTP response we got from the server.\n      // In case the client's time is set wrong, at least we know that any expiration date\n      // earlier than that point is definitely in the past.  If there's any discrepancy, we\n      // want to err on the side of cutting off event debugging sooner.\n      return e.debugEventsUntilDate > lastKnownPastTime && e.debugEventsUntilDate > new Date().getTime();\n    }\n    return false;\n  }\n\n  // Transform an event from its internal format to the format we use when sending a payload.\n  function makeOutputEvent(e) {\n    const ret = utils.extend({}, e);\n\n    // Identify, feature, and custom events should all include the full context.\n    // Debug events do as well, but are not handled by this code path.\n    if (e.kind === 'identify' || e.kind === 'feature' || e.kind === 'custom') {\n      ret.context = contextFilter.filter(e.context);\n    } else {\n      ret.contextKeys = getContextKeysFromEvent(e);\n      delete ret['context'];\n    }\n\n    if (e.kind === 'feature') {\n      delete ret['trackEvents'];\n      delete ret['debugEventsUntilDate'];\n    }\n    return ret;\n  }\n\n  function getContextKeysFromEvent(event) {\n    return getContextKeys(event.context, logger);\n  }\n\n  function addToOutbox(event) {\n    if (queue.length < eventCapacity) {\n      queue.push(event);\n      exceededCapacity = false;\n    } else {\n      if (!exceededCapacity) {\n        exceededCapacity = true;\n        logger.warn(messages.eventCapacityExceeded());\n      }\n      if (diagnosticsAccumulator) {\n        // For diagnostic events, we track how many times we had to drop an event due to exceeding the capacity.\n        diagnosticsAccumulator.incrementDroppedEvents();\n      }\n    }\n  }\n\n  processor.enqueue = function(event) {\n    if (disabled) {\n      return;\n    }\n    let addFullEvent = false;\n    let addDebugEvent = false;\n\n    // Add event to the summary counters if appropriate\n    summarizer.summarizeEvent(event);\n\n    // Decide whether to add the event to the payload. Feature events may be added twice, once for\n    // the event (if tracked) and once for debugging.\n    if (event.kind === 'feature') {\n      if (shouldSampleEvent()) {\n        addFullEvent = !!event.trackEvents;\n        addDebugEvent = shouldDebugEvent(event);\n      }\n    } else {\n      addFullEvent = shouldSampleEvent();\n    }\n\n    if (addFullEvent) {\n      addToOutbox(makeOutputEvent(event));\n    }\n    if (addDebugEvent) {\n      const debugEvent = utils.extend({}, event, { kind: 'debug' });\n      debugEvent.context = contextFilter.filter(debugEvent.context);\n      delete debugEvent['trackEvents'];\n      delete debugEvent['debugEventsUntilDate'];\n      addToOutbox(debugEvent);\n    }\n  };\n\n  processor.flush = async function() {\n    if (disabled) {\n      return Promise.resolve();\n    }\n    const eventsToSend = queue;\n    const summaries = summarizer.getSummaries();\n\n    summaries.forEach(summary => {\n      if (Object.keys(summary.features).length) {\n        eventsToSend.push(summary);\n      }\n    });\n\n    if (diagnosticsAccumulator) {\n      // For diagnostic events, we record how many events were in the queue at the last flush (since \"how\n      // many events happened to be in the queue at the moment we decided to send a diagnostic event\" would\n      // not be a very useful statistic).\n      diagnosticsAccumulator.setEventsInLastBatch(eventsToSend.length);\n    }\n    if (eventsToSend.length === 0) {\n      return Promise.resolve();\n    }\n    queue = [];\n    logger.debug(messages.debugPostingEvents(eventsToSend.length));\n    return eventSender.sendEvents(eventsToSend, mainEventsUrl).then(responseInfo => {\n      if (responseInfo) {\n        if (responseInfo.serverTime) {\n          lastKnownPastTime = responseInfo.serverTime;\n        }\n        if (!errors.isHttpErrorRecoverable(responseInfo.status)) {\n          disabled = true;\n        }\n        if (responseInfo.status >= 400) {\n          utils.onNextTick(() => {\n            emitter.maybeReportError(\n              new errors.LDUnexpectedResponseError(\n                messages.httpErrorMessage(responseInfo.status, 'event posting', 'some events were dropped')\n              )\n            );\n          });\n        }\n      }\n    });\n  };\n\n  processor.start = function() {\n    const flushTick = () => {\n      processor.flush();\n      flushTimer = setTimeout(flushTick, flushInterval);\n    };\n    flushTimer = setTimeout(flushTick, flushInterval);\n  };\n\n  processor.stop = function() {\n    clearTimeout(flushTimer);\n  };\n\n  return processor;\n}\n\nmodule.exports = EventProcessor;\n", "function EventEmitter(logger) {\n  const emitter = {};\n  const events = {};\n\n  const listeningTo = event => !!events[event];\n\n  emitter.on = function(event, handler, context) {\n    events[event] = events[event] || [];\n    events[event] = events[event].concat({\n      handler: handler,\n      context: context,\n    });\n  };\n\n  emitter.off = function(event, handler, context) {\n    if (!events[event]) {\n      return;\n    }\n    for (let i = 0; i < events[event].length; i++) {\n      if (events[event][i].handler === handler && events[event][i].context === context) {\n        events[event] = events[event].slice(0, i).concat(events[event].slice(i + 1));\n      }\n    }\n  };\n\n  emitter.emit = function(event) {\n    if (!events[event]) {\n      return;\n    }\n    // Copy the list of handlers before iterating, in case any handler adds or removes another handler.\n    // Any such changes should not affect what we do here-- we want to notify every handler that existed\n    // at the moment that the event was fired.\n    const copiedHandlers = events[event].slice(0);\n    for (let i = 0; i < copiedHandlers.length; i++) {\n      copiedHandlers[i].handler.apply(copiedHandlers[i].context, Array.prototype.slice.call(arguments, 1));\n    }\n  };\n\n  emitter.getEvents = function() {\n    return Object.keys(events);\n  };\n\n  emitter.getEventListenerCount = function(event) {\n    return events[event] ? events[event].length : 0;\n  };\n\n  emitter.maybeReportError = function(error) {\n    if (!error) {\n      return;\n    }\n    if (listeningTo('error')) {\n      this.emit('error', error);\n    } else {\n      (logger || console).error(error.message);\n    }\n  };\n  return emitter;\n}\n\nmodule.exports = EventEmitter;\n", "// This file provides an abstraction of the client's startup state.\n//\n// Startup can either succeed or fail exactly once; calling signalSuccess() or signalFailure()\n// after that point has no effect.\n//\n// On success, we fire both an \"initialized\" event and a \"ready\" event. Both the waitForInitialization()\n// promise and the waitUntilReady() promise are resolved in this case.\n//\n// On failure, we fire both a \"failed\" event (with the error as a parameter) and a \"ready\" event.\n// The waitForInitialization() promise is rejected, but the waitUntilReady() promise is resolved.\n//\n// To complicate things, we must *not* create the waitForInitialization() promise unless it is\n// requested, because otherwise failures would cause an *unhandled* rejection which can be a\n// serious problem in some environments. So we use a somewhat roundabout system for tracking the\n// initialization state and lazily creating this promise.\n\nconst readyEvent = 'ready',\n  successEvent = 'initialized',\n  failureEvent = 'failed';\n\nfunction InitializationStateTracker(eventEmitter) {\n  let succeeded = false,\n    failed = false,\n    failureValue = null,\n    initializationPromise = null;\n\n  const readyPromise = new Promise(resolve => {\n    const onReady = () => {\n      eventEmitter.off(readyEvent, onReady); // we can't use \"once\" because it's not available on some JS platforms\n      resolve();\n    };\n    eventEmitter.on(readyEvent, onReady);\n  }).catch(() => {}); // this Promise should never be rejected, but the catch handler is a safety measure\n\n  return {\n    getInitializationPromise: () => {\n      if (initializationPromise) {\n        return initializationPromise;\n      }\n      if (succeeded) {\n        return Promise.resolve();\n      }\n      if (failed) {\n        return Promise.reject(failureValue);\n      }\n      initializationPromise = new Promise((resolve, reject) => {\n        const onSuccess = () => {\n          eventEmitter.off(successEvent, onSuccess);\n          resolve();\n        };\n        const onFailure = err => {\n          eventEmitter.off(failureEvent, onFailure);\n          reject(err);\n        };\n        eventEmitter.on(successEvent, onSuccess);\n        eventEmitter.on(failureEvent, onFailure);\n      });\n      return initializationPromise;\n    },\n\n    getReadyPromise: () => readyPromise,\n\n    signalSuccess: () => {\n      if (!succeeded && !failed) {\n        succeeded = true;\n        eventEmitter.emit(successEvent);\n        eventEmitter.emit(readyEvent);\n      }\n    },\n\n    signalFailure: err => {\n      if (!succeeded && !failed) {\n        failed = true;\n        failureValue = err;\n        eventEmitter.emit(failureEvent, err);\n        eventEmitter.emit(readyEvent);\n      }\n      eventEmitter.maybeReportError(err); // the \"error\" event can be emitted more than once, unlike the others\n    },\n  };\n}\n\nmodule.exports = InitializationStateTracker;\n", "const utils = require('./utils');\n\nfunction PersistentFlagStore(storage, environment, hash, ident) {\n  const store = {};\n\n  function getFlagsKey() {\n    let key = '';\n    const context = ident.getContext();\n    if (context) {\n      key = hash || utils.btoa(JSON.stringify(context));\n    }\n    return 'ld:' + environment + ':' + key;\n  }\n\n  // Returns a Promise which will be resolved with a parsed JSON value if a stored value was available,\n  // or resolved with null if there was no value or if storage was not available.\n  store.loadFlags = () =>\n    storage.get(getFlagsKey()).then(dataStr => {\n      if (dataStr === null || dataStr === undefined) {\n        return null;\n      }\n      try {\n        let data = JSON.parse(dataStr);\n        if (data) {\n          const schema = data.$schema;\n          if (schema === undefined || schema < 1) {\n            data = utils.transformValuesToVersionedValues(data);\n          } else {\n            delete data['$schema'];\n          }\n        }\n        return data;\n      } catch (ex) {\n        return store.clearFlags().then(() => null);\n      }\n    });\n\n  // Resolves with true if successful, or false if storage is unavailable. Never rejects.\n  store.saveFlags = flags => {\n    const data = utils.extend({}, flags, { $schema: 1 });\n    return storage.set(getFlagsKey(), JSON.stringify(data));\n  };\n\n  // Resolves with true if successful, or false if storage is unavailable. Never rejects.\n  store.clearFlags = () => storage.clear(getFlagsKey());\n\n  return store;\n}\n\nmodule.exports = PersistentFlagStore;\n", "const messages = require('./messages');\n\n// The localStorageProvider is provided by the platform object. It should have the following\n// methods, each of which should return a Promise:\n// - get(key): Gets the string value, if any, for the given key\n// - set(key, value): Stores a string value for the given key\n// - remove(key): Removes the given key\n//\n// Storage is just a light wrapper of the localStorageProvider, adding error handling and\n// ensuring that we don't call it if it's unavailable. The get method will simply resolve\n// with an undefined value if there is an error or if there is no localStorageProvider.\n// None of the promises returned by Storage will ever be rejected.\n//\n// It is always possible that the underlying platform storage mechanism might fail or be\n// disabled. If so, it's likely that it will keep failing, so we will only log one warning\n// instead of repetitive warnings.\nfunction PersistentStorage(localStorageProvider, logger) {\n  const storage = {};\n  let loggedError = false;\n\n  const logError = err => {\n    if (!loggedError) {\n      loggedError = true;\n      logger.warn(messages.localStorageUnavailable(err));\n    }\n  };\n\n  storage.isEnabled = () => !!localStorageProvider;\n\n  // Resolves with a value, or undefined if storage is unavailable. Never rejects.\n  storage.get = key =>\n    new Promise(resolve => {\n      if (!localStorageProvider) {\n        resolve(undefined);\n        return;\n      }\n      localStorageProvider\n        .get(key)\n        .then(resolve)\n        .catch(err => {\n          logError(err);\n          resolve(undefined);\n        });\n    });\n\n  // Resolves with true if successful, or false if storage is unavailable. Never rejects.\n  storage.set = (key, value) =>\n    new Promise(resolve => {\n      if (!localStorageProvider) {\n        resolve(false);\n        return;\n      }\n      localStorageProvider\n        .set(key, value)\n        .then(() => resolve(true))\n        .catch(err => {\n          logError(err);\n          resolve(false);\n        });\n    });\n\n  // Resolves with true if successful, or false if storage is unavailable. Never rejects.\n  storage.clear = key =>\n    new Promise(resolve => {\n      if (!localStorageProvider) {\n        resolve(false);\n        return;\n      }\n      localStorageProvider\n        .clear(key)\n        .then(() => resolve(true))\n        .catch(err => {\n          logError(err);\n          resolve(false);\n        });\n    });\n\n  return storage;\n}\n\nmodule.exports = PersistentStorage;\n", "const messages = require('./messages');\nconst { appendUrlPath, base64URLEncode, objectHasOwnProperty } = require('./utils');\nconst { getLDHeaders, transformHeaders } = require('./headers');\nconst { isHttpErrorRecoverable } = require('./errors');\n\n// The underlying event source implementation is abstracted via the platform object, which should\n// have these three properties:\n// eventSourceFactory(): a function that takes a URL and optional config object and returns an object\n//   with the same methods as the regular HTML5 EventSource object. The properties in the config\n//   object are those supported by the launchdarkly-eventsource package; browser EventSource\n//   implementations don't have any config options.\n// eventSourceIsActive(): a function that takes an EventSource-compatible object and returns true if\n//   it is in an active state (connected or connecting).\n// eventSourceAllowsReport: true if REPORT is supported.\n\n// The read timeout for the stream is a fixed value that is set to be slightly longer than the expected\n// interval between heartbeats from the LaunchDarkly streaming server. If this amount of time elapses\n// with no new data, the connection will be cycled.\nconst streamReadTimeoutMillis = 5 * 60 * 1000; // 5 minutes\nconst maxRetryDelay = 30 * 1000; // Maximum retry delay 30 seconds.\nconst jitterRatio = 0.5; // Delay should be 50%-100% of calculated time.\n\nfunction Stream(platform, config, environment, diagnosticsAccumulator) {\n  const baseUrl = config.streamUrl;\n  const logger = config.logger;\n  const stream = {};\n  const evalUrlPrefix = appendUrlPath(baseUrl, '/eval/' + environment);\n  const useReport = config.useReport;\n  const withReasons = config.evaluationReasons;\n  const baseReconnectDelay = config.streamReconnectDelay;\n  const headers = getLDHeaders(platform, config);\n  let firstConnectionErrorLogged = false;\n  let es = null;\n  let reconnectTimeoutReference = null;\n  let connectionAttemptStartTime;\n  let context = null;\n  let hash = null;\n  let handlers = null;\n  let retryCount = 0;\n\n  function backoff() {\n    const delay = baseReconnectDelay * Math.pow(2, retryCount);\n    return delay > maxRetryDelay ? maxRetryDelay : delay;\n  }\n\n  function jitter(computedDelayMillis) {\n    return computedDelayMillis - Math.trunc(Math.random() * jitterRatio * computedDelayMillis);\n  }\n\n  function getNextRetryDelay() {\n    const delay = jitter(backoff());\n    retryCount += 1;\n    return delay;\n  }\n\n  stream.connect = function(newContext, newHash, newHandlers) {\n    context = newContext;\n    hash = newHash;\n    handlers = {};\n    for (const key in newHandlers || {}) {\n      handlers[key] = function(e) {\n        // Reset the state for logging the first connection error so that the first\n        // connection error following a successful connection will once again be logged.\n        // We will decorate *all* handlers to do this to keep this abstraction agnostic\n        // for different stream implementations.\n        firstConnectionErrorLogged = false;\n        logConnectionResult(true);\n        newHandlers[key] && newHandlers[key](e);\n      };\n    }\n    tryConnect();\n  };\n\n  stream.disconnect = function() {\n    clearTimeout(reconnectTimeoutReference);\n    reconnectTimeoutReference = null;\n    closeConnection();\n  };\n\n  stream.isConnected = function() {\n    return !!(es && platform.eventSourceIsActive && platform.eventSourceIsActive(es));\n  };\n\n  function handleError(err) {\n    // The event source may not produce a status. But the LaunchDarkly\n    // polyfill can. If we can get the status, then we should stop retrying\n    // on certain error codes.\n    if (err.status && typeof err.status === 'number' && !isHttpErrorRecoverable(err.status)) {\n      // If we encounter an unrecoverable condition, then we do not want to\n      // retry anymore.\n      closeConnection();\n      logger.error(messages.unrecoverableStreamError(err));\n      // Ensure any pending retry attempts are not done.\n      if (reconnectTimeoutReference) {\n        clearTimeout(reconnectTimeoutReference);\n        reconnectTimeoutReference = null;\n      }\n      return;\n    }\n\n    const delay = getNextRetryDelay();\n\n    if (!firstConnectionErrorLogged) {\n      logger.warn(messages.streamError(err, delay));\n      firstConnectionErrorLogged = true;\n    }\n    logConnectionResult(false);\n    closeConnection();\n    tryConnect(delay);\n  }\n\n  function tryConnect(delay) {\n    if (!reconnectTimeoutReference) {\n      if (delay) {\n        reconnectTimeoutReference = setTimeout(openConnection, delay);\n      } else {\n        openConnection();\n      }\n    }\n  }\n\n  function openConnection() {\n    reconnectTimeoutReference = null;\n    let url;\n    let query = '';\n    const options = { headers, readTimeoutMillis: streamReadTimeoutMillis };\n    if (platform.eventSourceFactory) {\n      if (hash !== null && hash !== undefined) {\n        query = 'h=' + hash;\n      }\n      if (useReport) {\n        if (platform.eventSourceAllowsReport) {\n          url = evalUrlPrefix;\n          options.method = 'REPORT';\n          options.headers['Content-Type'] = 'application/json';\n          options.body = JSON.stringify(context);\n        } else {\n          // if we can't do REPORT, fall back to the old ping-based stream\n          url = appendUrlPath(baseUrl, '/ping/' + environment);\n          query = '';\n        }\n      } else {\n        url = evalUrlPrefix + '/' + base64URLEncode(JSON.stringify(context));\n      }\n      options.headers = transformHeaders(options.headers, config);\n      if (withReasons) {\n        query = query + (query ? '&' : '') + 'withReasons=true';\n      }\n      url = url + (query ? '?' : '') + query;\n\n      closeConnection();\n      logger.info(messages.streamConnecting(url));\n      logConnectionStarted();\n\n      es = platform.eventSourceFactory(url, options);\n      for (const key in handlers) {\n        if (objectHasOwnProperty(handlers, key)) {\n          es.addEventListener(key, handlers[key]);\n        }\n      }\n\n      es.onerror = handleError;\n\n      es.onopen = () => {\n        // If the connection is a success, then reset the retryCount.\n        retryCount = 0;\n      };\n    }\n  }\n\n  function closeConnection() {\n    if (es) {\n      logger.info(messages.streamClosing());\n      es.close();\n      es = null;\n    }\n  }\n\n  function logConnectionStarted() {\n    connectionAttemptStartTime = new Date().getTime();\n  }\n\n  function logConnectionResult(success) {\n    if (connectionAttemptStartTime && diagnosticsAccumulator) {\n      diagnosticsAccumulator.recordStreamInit(\n        connectionAttemptStartTime,\n        !success,\n        new Date().getTime() - connectionAttemptStartTime\n      );\n    }\n    connectionAttemptStartTime = null;\n  }\n\n  return stream;\n}\n\nmodule.exports = Stream;\n", "// This function allows a series of Promises to be coalesced such that only the most recently\n// added one actually matters. For instance, if several HTTP requests are made to the same\n// endpoint and we want to ensure that whoever made each one always gets the latest data, each\n// can be passed to addPromise (on the same coalescer) and each caller can wait on the\n// coalescer.resultPromise; all three will then receive the result (or error) from the *last*\n// request, and the results of the first two will be discarded.\n//\n// The cancelFn callback, if present, will be called whenever an existing promise is being\n// discarded. This can be used for instance to abort an HTTP request that's now obsolete.\n//\n// The finallyFn callback, if present, is called on completion of the whole thing. This is\n// different from calling coalescer.resultPromise.finally() because it is executed before any\n// other handlers. Its purpose is to tell the caller that this coalescer should no longer be used.\n\nfunction promiseCoalescer(finallyFn) {\n  let currentPromise;\n  let currentCancelFn;\n  let finalResolve;\n  let finalReject;\n\n  const coalescer = {};\n\n  coalescer.addPromise = (p, cancelFn) => {\n    currentPromise = p;\n    currentCancelFn && currentCancelFn();\n    currentCancelFn = cancelFn;\n\n    p.then(\n      result => {\n        if (currentPromise === p) {\n          finalResolve(result);\n          finallyFn && finallyFn();\n        }\n      },\n      error => {\n        if (currentPromise === p) {\n          finalReject(error);\n          finallyFn && finallyFn();\n        }\n      }\n    );\n  };\n\n  coalescer.resultPromise = new Promise((resolve, reject) => {\n    finalResolve = resolve;\n    finalReject = reject;\n  });\n\n  return coalescer;\n}\n\nmodule.exports = promiseCoalescer;\n", "const utils = require('./utils');\nconst errors = require('./errors');\nconst messages = require('./messages');\nconst promiseCoalescer = require('./promiseCoalescer');\nconst { transformHeaders, getLDHeaders } = require('./headers');\n\nconst jsonContentType = 'application/json';\n\nfunction getResponseError(result) {\n  if (result.status === 404) {\n    return new errors.LDInvalidEnvironmentIdError(messages.environmentNotFound());\n  } else {\n    return new errors.LDFlagFetchError(messages.errorFetchingFlags(result.statusText || String(result.status)));\n  }\n}\n\nfunction Requestor(platform, options, environment) {\n  const baseUrl = options.baseUrl;\n  const useReport = options.useReport;\n  const withReasons = options.evaluationReasons;\n  const logger = options.logger;\n\n  const requestor = {};\n\n  const activeRequests = {}; // map of URLs to promiseCoalescers\n\n  function fetchJSON(endpoint, body) {\n    if (!platform.httpRequest) {\n      return new Promise((resolve, reject) => {\n        reject(new errors.LDFlagFetchError(messages.httpUnavailable()));\n      });\n    }\n\n    const method = body ? 'REPORT' : 'GET';\n    const headers = getLDHeaders(platform, options);\n    if (body) {\n      headers['Content-Type'] = jsonContentType;\n    }\n\n    let coalescer = activeRequests[endpoint];\n    if (!coalescer) {\n      coalescer = promiseCoalescer(() => {\n        // this will be called once there are no more active requests for the same endpoint\n        delete activeRequests[endpoint];\n      });\n      activeRequests[endpoint] = coalescer;\n    }\n\n    const req = platform.httpRequest(method, endpoint, transformHeaders(headers, options), body);\n    const p = req.promise.then(\n      result => {\n        if (result.status === 200) {\n          // We're using substring here because using startsWith would require a polyfill in IE.\n          if (\n            result.header('content-type') &&\n            result.header('content-type').substring(0, jsonContentType.length) === jsonContentType\n          ) {\n            return JSON.parse(result.body);\n          } else {\n            const message = messages.invalidContentType(result.header('content-type') || '');\n            return Promise.reject(new errors.LDFlagFetchError(message));\n          }\n        } else {\n          return Promise.reject(getResponseError(result));\n        }\n      },\n      e => Promise.reject(new errors.LDFlagFetchError(messages.networkError(e)))\n    );\n    coalescer.addPromise(p, () => {\n      // this will be called if another request for the same endpoint supersedes this one\n      req.cancel && req.cancel();\n    });\n    return coalescer.resultPromise;\n  }\n\n  // Performs a GET request to an arbitrary path under baseUrl. Returns a Promise which will resolve\n  // with the parsed JSON response, or will be rejected if the request failed.\n  requestor.fetchJSON = function(path) {\n    return fetchJSON(utils.appendUrlPath(baseUrl, path), null);\n  };\n\n  // Requests the current state of all flags for the given context from LaunchDarkly. Returns a Promise\n  // which will resolve with the parsed JSON response, or will be rejected if the request failed.\n  requestor.fetchFlagSettings = function(context, hash) {\n    let data;\n    let endpoint;\n    let query = '';\n    let body;\n\n    if (useReport) {\n      endpoint = [baseUrl, '/sdk/evalx/', environment, '/context'].join('');\n      body = JSON.stringify(context);\n    } else {\n      data = utils.base64URLEncode(JSON.stringify(context));\n      endpoint = [baseUrl, '/sdk/evalx/', environment, '/contexts/', data].join('');\n    }\n    if (hash) {\n      query = 'h=' + hash;\n    }\n    if (withReasons) {\n      query = query + (query ? '&' : '') + 'withReasons=true';\n    }\n    endpoint = endpoint + (query ? '?' : '') + query;\n    logger.debug(messages.debugPolling(endpoint));\n\n    return fetchJSON(endpoint, body);\n  };\n\n  return requestor;\n}\n\nmodule.exports = Requestor;\n", "const utils = require('./utils');\n\nfunction Identity(initialContext, onChange) {\n  const ident = {};\n  let context;\n\n  ident.setContext = function(c) {\n    context = utils.sanitizeContext(c);\n    if (context && onChange) {\n      onChange(utils.clone(context));\n    }\n  };\n\n  ident.getContext = function() {\n    return context ? utils.clone(context) : null;\n  };\n\n  if (initialContext) {\n    ident.setContext(initialContext);\n  }\n\n  return ident;\n}\n\nmodule.exports = Identity;\n", "const { v1: uuidv1 } = require('uuid');\nconst { getContextKinds } = require('./context');\n\nconst errors = require('./errors');\nconst messages = require('./messages');\nconst utils = require('./utils');\n\nconst ldUserIdKey = 'ld:$anonUserId';\n\n/**\n * Create an object which can process a context and populate any required keys\n * for anonymous objects.\n *\n * @param {Object} persistentStorage The persistent storage from which to store\n * and access persisted anonymous context keys.\n * @returns An AnonymousContextProcessor.\n */\nfunction AnonymousContextProcessor(persistentStorage) {\n  function getContextKeyIdString(kind) {\n    if (kind === undefined || kind === null || kind === 'user') {\n      return ldUserIdKey;\n    }\n    return `ld:$contextKey:${kind}`;\n  }\n\n  function getCachedContextKey(kind) {\n    return persistentStorage.get(getContextKeyIdString(kind));\n  }\n\n  function setCachedContextKey(id, kind) {\n    return persistentStorage.set(getContextKeyIdString(kind), id);\n  }\n\n  /**\n   * Process a single kind context, or a single context within a multi-kind context.\n   * @param {string} kind The kind of the context. Independent because the kind is not prevent\n   * within a context in a multi-kind context.\n   * @param {Object} context\n   * @returns {Promise} a promise that resolves to a processed contexts, or rejects\n   * a context which cannot be processed.\n   */\n  function processSingleKindContext(kind, context) {\n    // We are working on a copy of an original context, so we want to re-assign\n    // versus duplicating it again.\n\n    /* eslint-disable no-param-reassign */\n    if (context.key !== null && context.key !== undefined) {\n      context.key = context.key.toString();\n      return Promise.resolve(context);\n    }\n\n    if (context.anonymous) {\n      // If the key doesn't exist, then the persistent storage will resolve\n      // with undefined.\n      return getCachedContextKey(kind).then(cachedId => {\n        if (cachedId) {\n          context.key = cachedId;\n          return context;\n        } else {\n          const id = uuidv1();\n          context.key = id;\n          return setCachedContextKey(id, kind).then(() => context);\n        }\n      });\n    } else {\n      return Promise.reject(new errors.LDInvalidUserError(messages.invalidContext()));\n    }\n    /* eslint-enable no-param-reassign */\n  }\n\n  /**\n   * Process the context, returning a Promise that resolves to the processed context, or rejects if there is an error.\n   * @param {Object} context\n   * @returns {Promise} A promise which resolves to a processed context, or a rejection if the context cannot be\n   * processed. The context should still be checked for overall validity after being processed.\n   */\n  this.processContext = context => {\n    if (!context) {\n      return Promise.reject(new errors.LDInvalidUserError(messages.contextNotSpecified()));\n    }\n\n    const processedContext = utils.clone(context);\n\n    if (context.kind === 'multi') {\n      const kinds = getContextKinds(processedContext);\n\n      return Promise.all(kinds.map(kind => processSingleKindContext(kind, processedContext[kind]))).then(\n        () => processedContext\n      );\n    }\n    return processSingleKindContext(context.kind, processedContext);\n  };\n}\n\nmodule.exports = AnonymousContextProcessor;\n", "const { v1: uuidv1 } = require('uuid');\n// Note that in the diagnostic events spec, these IDs are to be generated with UUID v4. However,\n// in JS we were already using v1 for unique context keys, so to avoid bringing in two packages we\n// will use v1 here as well.\n\nconst { baseOptionDefs } = require('./configuration');\nconst messages = require('./messages');\nconst { appendUrlPath } = require('./utils');\n\nfunction DiagnosticId(sdkKey) {\n  const ret = {\n    diagnosticId: uuidv1(),\n  };\n  if (sdkKey) {\n    ret.sdkKeySuffix = sdkKey.length > 6 ? sdkKey.substring(sdkKey.length - 6) : sdkKey;\n  }\n  return ret;\n}\n\n// A stateful object holding statistics that will go into diagnostic events.\n\nfunction DiagnosticsAccumulator(startTime) {\n  let dataSinceDate, droppedEvents, eventsInLastBatch, streamInits;\n\n  function reset(time) {\n    dataSinceDate = time;\n    droppedEvents = 0;\n    eventsInLastBatch = 0;\n    streamInits = [];\n  }\n\n  reset(startTime);\n\n  return {\n    getProps: () => ({\n      dataSinceDate,\n      droppedEvents,\n      eventsInLastBatch,\n      streamInits,\n      // omit deduplicatedUsers for the JS SDKs because they don't deduplicate users\n    }),\n    setProps: props => {\n      dataSinceDate = props.dataSinceDate;\n      droppedEvents = props.droppedEvents || 0;\n      eventsInLastBatch = props.eventsInLastBatch || 0;\n      streamInits = props.streamInits || [];\n    },\n    incrementDroppedEvents: () => {\n      droppedEvents++;\n    },\n    setEventsInLastBatch: n => {\n      eventsInLastBatch = n;\n    },\n    recordStreamInit: (timestamp, failed, durationMillis) => {\n      const info = { timestamp, failed, durationMillis };\n      streamInits.push(info);\n    },\n    reset,\n  };\n}\n\n// An object that maintains information that will go into diagnostic events, and knows how to format\n// those events. It is instantiated by the SDK client, and shared with the event processor.\n//\n// The JS-based SDKs have two modes for diagnostic events. By default, the behavior is basically the\n// same as the server-side SDKs: a \"diagnostic-init\" event is sent on startup, and then \"diagnostic\"\n// events with operating statistics are sent periodically. However, in a browser environment this is\n// undesirable because the page may be reloaded frequently. In that case, setting the property\n// \"platform.diagnosticUseCombinedEvent\" to true enables an alternate mode in which a combination of\n// both kinds of event is sent at intervals, relative to the last time this was done (if any) which\n// is cached in local storage.\n\nfunction DiagnosticsManager(\n  platform,\n  persistentStorage,\n  accumulator,\n  eventSender,\n  environmentId,\n  config,\n  diagnosticId\n) {\n  const combinedMode = !!platform.diagnosticUseCombinedEvent;\n  const localStorageKey = 'ld:' + environmentId + ':$diagnostics';\n  const diagnosticEventsUrl = appendUrlPath(config.eventsUrl, '/events/diagnostic/' + environmentId);\n  const periodicInterval = config.diagnosticRecordingInterval;\n  const acc = accumulator;\n  const initialEventSamplingInterval = 4; // used only in combined mode - see start()\n  let streamingEnabled = !!config.streaming;\n  let eventSentTime;\n  let periodicTimer;\n  const manager = {};\n\n  function makeInitProperties() {\n    return {\n      sdk: makeSdkData(),\n      configuration: makeConfigData(),\n      platform: platform.diagnosticPlatformData,\n    };\n  }\n\n  // Send a diagnostic event and do not wait for completion.\n  function sendDiagnosticEvent(event) {\n    config.logger && config.logger.debug(messages.debugPostingDiagnosticEvent(event));\n    eventSender\n      .sendEvents(event, diagnosticEventsUrl, true)\n      .then(() => undefined)\n      .catch(() => undefined);\n  }\n\n  function loadProperties(callback) {\n    if (!persistentStorage.isEnabled()) {\n      return callback(false); // false indicates that local storage is not available\n    }\n    persistentStorage\n      .get(localStorageKey)\n      .then(data => {\n        if (data) {\n          try {\n            const props = JSON.parse(data);\n            acc.setProps(props);\n            eventSentTime = props.dataSinceDate;\n          } catch (e) {\n            // disregard malformed cached data\n          }\n        }\n        callback(true);\n      })\n      .catch(() => {\n        callback(false);\n      });\n  }\n\n  function saveProperties() {\n    if (persistentStorage.isEnabled()) {\n      const props = { ...acc.getProps() };\n      persistentStorage.set(localStorageKey, JSON.stringify(props));\n    }\n  }\n\n  // Creates the initial event that is sent by the event processor when the SDK starts up. This will not\n  // be repeated during the lifetime of the SDK client. In combined mode, we don't send this.\n  function createInitEvent() {\n    return {\n      kind: 'diagnostic-init',\n      id: diagnosticId,\n      creationDate: acc.getProps().dataSinceDate,\n      ...makeInitProperties(),\n    };\n  }\n\n  // Creates a periodic event containing time-dependent stats, and resets the state of the manager with\n  // regard to those stats. In combined mode (browser SDK) this also contains the configuration data.\n  function createPeriodicEventAndReset() {\n    const currentTime = new Date().getTime();\n    let ret = {\n      kind: combinedMode ? 'diagnostic-combined' : 'diagnostic',\n      id: diagnosticId,\n      creationDate: currentTime,\n      ...acc.getProps(),\n    };\n    if (combinedMode) {\n      ret = { ...ret, ...makeInitProperties() };\n    }\n    acc.reset(currentTime);\n    return ret;\n  }\n\n  function sendPeriodicEvent() {\n    sendDiagnosticEvent(createPeriodicEventAndReset());\n    periodicTimer = setTimeout(sendPeriodicEvent, periodicInterval);\n    eventSentTime = new Date().getTime();\n    if (combinedMode) {\n      saveProperties();\n    }\n  }\n\n  function makeSdkData() {\n    const sdkData = { ...platform.diagnosticSdkData };\n    if (config.wrapperName) {\n      sdkData.wrapperName = config.wrapperName;\n    }\n    if (config.wrapperVersion) {\n      sdkData.wrapperVersion = config.wrapperVersion;\n    }\n    return sdkData;\n  }\n\n  function makeConfigData() {\n    const configData = {\n      customBaseURI: config.baseUrl !== baseOptionDefs.baseUrl.default,\n      customStreamURI: config.streamUrl !== baseOptionDefs.streamUrl.default,\n      customEventsURI: config.eventsUrl !== baseOptionDefs.eventsUrl.default,\n      eventsCapacity: config.eventCapacity,\n      eventsFlushIntervalMillis: config.flushInterval,\n      reconnectTimeMillis: config.streamReconnectDelay,\n      streamingDisabled: !streamingEnabled,\n      allAttributesPrivate: !!config.allAttributesPrivate,\n      diagnosticRecordingIntervalMillis: config.diagnosticRecordingInterval,\n      // The following extra properties are only provided by client-side JS SDKs:\n      usingSecureMode: !!config.hash,\n      bootstrapMode: !!config.bootstrap,\n      fetchGoalsDisabled: !config.fetchGoals,\n      sendEventsOnlyForVariation: !!config.sendEventsOnlyForVariation,\n    };\n    // Client-side JS SDKs do not have the following properties which other SDKs have:\n    // connectTimeoutMillis\n    // pollingIntervalMillis\n    // samplingInterval\n    // socketTimeoutMillis\n    // startWaitMillis\n    // userKeysCapacity\n    // userKeysFlushIntervalMillis\n    // usingProxy\n    // usingProxyAuthenticator\n    // usingRelayDaemon\n\n    return configData;\n  }\n\n  // Called when the SDK is starting up. Either send an init event immediately, or, in the alternate\n  // mode, check for cached local storage properties and send an event only if we haven't done so\n  // recently.\n  manager.start = () => {\n    if (combinedMode) {\n      loadProperties(localStorageAvailable => {\n        if (localStorageAvailable) {\n          const nextEventTime = (eventSentTime || 0) + periodicInterval;\n          const timeNow = new Date().getTime();\n          if (timeNow >= nextEventTime) {\n            sendPeriodicEvent();\n          } else {\n            periodicTimer = setTimeout(sendPeriodicEvent, nextEventTime - timeNow);\n          }\n        } else {\n          // We don't have the ability to cache anything in local storage, so we don't know if we\n          // recently sent an event before this page load, but we would still prefer not to send one\n          // on *every* page load. So, as a rough heuristic, we'll decide semi-randomly.\n          if (Math.floor(Math.random() * initialEventSamplingInterval) === 0) {\n            sendPeriodicEvent();\n          } else {\n            periodicTimer = setTimeout(sendPeriodicEvent, periodicInterval);\n          }\n        }\n      });\n    } else {\n      sendDiagnosticEvent(createInitEvent());\n      periodicTimer = setTimeout(sendPeriodicEvent, periodicInterval);\n    }\n  };\n\n  manager.stop = () => {\n    periodicTimer && clearTimeout(periodicTimer);\n  };\n\n  // Called when streaming mode is turned on or off dynamically.\n  manager.setStreaming = enabled => {\n    streamingEnabled = enabled;\n  };\n\n  return manager;\n}\n\nmodule.exports = {\n  DiagnosticId,\n  DiagnosticsAccumulator,\n  DiagnosticsManager,\n};\n", "const messages = require('./messages');\n\n/**\n * Wrap an inspector ensuring that calling its methods are safe.\n * @param {object} inspector Inspector to wrap.\n */\nfunction SafeInspector(inspector, logger) {\n  let errorLogged = false;\n  const wrapper = {\n    type: inspector.type,\n    name: inspector.name,\n    synchronous: inspector.synchronous,\n  };\n\n  wrapper.method = (...args) => {\n    try {\n      inspector.method(...args);\n    } catch {\n      // If something goes wrong in an inspector we want to log that something\n      // went wrong. We don't want to flood the logs, so we only log something\n      // the first time that something goes wrong.\n      // We do not include the exception in the log, because we do not know what\n      // kind of data it may contain.\n      if (!errorLogged) {\n        errorLogged = true;\n        logger.warn(messages.inspectorMethodError(wrapper.type, wrapper.name));\n      }\n      // Prevent errors.\n    }\n  };\n\n  return wrapper;\n}\n\nmodule.exports = SafeInspector;\n", "const messages = require('./messages');\nconst SafeInspector = require('./SafeInspector');\nconst { onNextTick } = require('./utils');\n\n/**\n * The types of supported inspectors.\n */\nconst InspectorTypes = {\n  flagUsed: 'flag-used',\n  flagDetailsChanged: 'flag-details-changed',\n  flagDetailChanged: 'flag-detail-changed',\n  clientIdentityChanged: 'client-identity-changed',\n};\n\nObject.freeze(InspectorTypes);\n\n/**\n * Manages dispatching of inspection data to registered inspectors.\n */\nfunction InspectorManager(inspectors, logger) {\n  const manager = {};\n\n  /**\n   * Collection of inspectors keyed by type.\n   *\n   * Inspectors are async by default.\n   *\n   * @type {{[type: string]: object[]}}\n   */\n  const inspectorsByType = {\n    [InspectorTypes.flagUsed]: [],\n    [InspectorTypes.flagDetailsChanged]: [],\n    [InspectorTypes.flagDetailChanged]: [],\n    [InspectorTypes.clientIdentityChanged]: [],\n  };\n  /**\n   * Collection synchronous of inspectors keyed by type.\n   *\n   * @type {{[type: string]: object[]}}\n   */\n  const synchronousInspectorsByType = {\n    [InspectorTypes.flagUsed]: [],\n    [InspectorTypes.flagDetailsChanged]: [],\n    [InspectorTypes.flagDetailChanged]: [],\n    [InspectorTypes.clientIdentityChanged]: [],\n  };\n\n  const safeInspectors = inspectors && inspectors.map(inspector => SafeInspector(inspector, logger));\n\n  safeInspectors &&\n    safeInspectors.forEach(safeInspector => {\n      // Only add inspectors of supported types.\n      if (Object.prototype.hasOwnProperty.call(inspectorsByType, safeInspector.type) && !safeInspector.synchronous) {\n        inspectorsByType[safeInspector.type].push(safeInspector);\n      } else if (\n        Object.prototype.hasOwnProperty.call(synchronousInspectorsByType, safeInspector.type) &&\n        safeInspector.synchronous\n      ) {\n        synchronousInspectorsByType[safeInspector.type].push(safeInspector);\n      } else {\n        logger.warn(messages.invalidInspector(safeInspector.type, safeInspector.name));\n      }\n    });\n\n  /**\n   * Check if there is an inspector of a specific type registered.\n   *\n   * @param {string} type The type of the inspector to check.\n   * @returns True if there are any inspectors of that type registered.\n   */\n  manager.hasListeners = type =>\n    (inspectorsByType[type] && inspectorsByType[type].length) ||\n    (synchronousInspectorsByType[type] && synchronousInspectorsByType[type].length);\n\n  /**\n   * Notify registered inspectors of a flag being used.\n   *\n   * The notification itself will be dispatched asynchronously.\n   *\n   * @param {string} flagKey The key for the flag.\n   * @param {Object} detail The LDEvaluationDetail for the flag.\n   * @param {Object} context The LDContext for the flag.\n   */\n  manager.onFlagUsed = (flagKey, detail, context) => {\n    const type = InspectorTypes.flagUsed;\n    if (synchronousInspectorsByType[type].length) {\n      synchronousInspectorsByType[type].forEach(inspector => inspector.method(flagKey, detail, context));\n    }\n    if (inspectorsByType[type].length) {\n      onNextTick(() => {\n        inspectorsByType[type].forEach(inspector => inspector.method(flagKey, detail, context));\n      });\n    }\n  };\n\n  /**\n   * Notify registered inspectors that the flags have been replaced.\n   *\n   * The notification itself will be dispatched asynchronously.\n   *\n   * @param {Record<string, Object>} flags The current flags as a Record<string, LDEvaluationDetail>.\n   */\n  manager.onFlags = flags => {\n    const type = InspectorTypes.flagDetailsChanged;\n    if (synchronousInspectorsByType[type].length) {\n      synchronousInspectorsByType[type].forEach(inspector => inspector.method(flags));\n    }\n    if (inspectorsByType[type].length) {\n      onNextTick(() => {\n        inspectorsByType[type].forEach(inspector => inspector.method(flags));\n      });\n    }\n  };\n\n  /**\n   * Notify registered inspectors that a flag value has changed.\n   *\n   * The notification itself will be dispatched asynchronously.\n   *\n   * @param {string} flagKey The key for the flag that changed.\n   * @param {Object} flag An `LDEvaluationDetail` for the flag.\n   */\n  manager.onFlagChanged = (flagKey, flag) => {\n    const type = InspectorTypes.flagDetailChanged;\n    if (synchronousInspectorsByType[type].length) {\n      synchronousInspectorsByType[type].forEach(inspector => inspector.method(flagKey, flag));\n    }\n    if (inspectorsByType[type].length) {\n      onNextTick(() => {\n        inspectorsByType[type].forEach(inspector => inspector.method(flagKey, flag));\n      });\n    }\n  };\n\n  /**\n   * Notify the registered inspectors that the context identity has changed.\n   *\n   * The notification itself will be dispatched asynchronously.\n   *\n   * @param {Object} context The `LDContext` which is now identified.\n   */\n  manager.onIdentityChanged = context => {\n    const type = InspectorTypes.clientIdentityChanged;\n    if (synchronousInspectorsByType[type].length) {\n      synchronousInspectorsByType[type].forEach(inspector => inspector.method(context));\n    }\n    if (inspectorsByType[type].length) {\n      onNextTick(() => {\n        inspectorsByType[type].forEach(inspector => inspector.method(context));\n      });\n    }\n  };\n\n  return manager;\n}\n\nmodule.exports = { InspectorTypes, InspectorManager };\n", "const { LDTimeoutError } = require('./errors');\n\n/**\n * Returns a promise which errors after t seconds.\n *\n * @param t Timeout in seconds.\n * @param taskName Name of task being timed for logging and error reporting.\n */\nfunction timedPromise(t, taskName) {\n  return new Promise((_res, reject) => {\n    setTimeout(() => {\n      const e = `${taskName} timed out after ${t} seconds.`;\n      reject(new LDTimeoutError(e));\n    }, t * 1000);\n  });\n}\nmodule.exports = timedPromise;\n", "const UNKNOWN_HOOK_NAME = 'unknown hook';\nconst BEFORE_EVALUATION_STAGE_NAME = 'beforeEvaluation';\nconst AFTER_EVALUATION_STAGE_NAME = 'afterEvaluation';\nconst BEFORE_IDENTIFY_STAGE_NAME = 'beforeIdentify';\nconst AFTER_IDENTIFY_STAGE_NAME = 'afterIdentify';\nconst AFTER_TRACK_STAGE_NAME = 'afterTrack';\n\n/**\n * Safely executes a hook stage function, logging any errors.\n * @param {{ error: (message: string) => void } | undefined} logger The logger instance.\n * @param {string} method The name of the hook stage being executed (e.g., 'beforeEvaluation').\n * @param {string} hookName The name of the hook.\n * @param {() => any} stage The function representing the hook stage to execute.\n * @param {any} def The default value to return if the stage function throws an error.\n * @returns {any} The result of the stage function, or the default value if an error occurred.\n */\nfunction tryExecuteStage(logger, method, hookName, stage, def) {\n  try {\n    return stage();\n  } catch (err) {\n    logger?.error(`An error was encountered in \"${method}\" of the \"${hookName}\" hook: ${err}`);\n    return def;\n  }\n}\n\n/**\n * Safely gets the name of a hook from its metadata.\n * @param {{ error: (message: string) => void }} logger The logger instance.\n * @param {{ getMetadata: () => { name?: string } }} hook The hook instance.\n * @returns {string} The name of the hook, or 'unknown hook' if unable to retrieve it.\n */\nfunction getHookName(logger, hook) {\n  try {\n    return hook.getMetadata().name || UNKNOWN_HOOK_NAME;\n  } catch {\n    logger.error(`Exception thrown getting metadata for hook. Unable to get hook name.`);\n    return UNKNOWN_HOOK_NAME;\n  }\n}\n\n/**\n * Executes the 'beforeEvaluation' stage for all registered hooks.\n * @param {{ error: (message: string) => void }} logger The logger instance.\n * @param {Array<{ beforeEvaluation?: (hookContext: object, data: object) => object }>} hooks The array of hook instances.\n * @param {{ flagKey: string, context: object, defaultValue: any }} hookContext The context for the evaluation series.\n * @returns {Array<object>} An array containing the data returned by each hook's 'beforeEvaluation' stage.\n */\nfunction executeBeforeEvaluation(logger, hooks, hookContext) {\n  return hooks.map(hook =>\n    tryExecuteStage(\n      logger,\n      BEFORE_EVALUATION_STAGE_NAME,\n      getHookName(logger, hook),\n      () => hook?.beforeEvaluation?.(hookContext, {}) ?? {},\n      {}\n    )\n  );\n}\n\n/**\n * Executes the 'afterEvaluation' stage for all registered hooks in reverse order.\n * @param {{ error: (message: string) => void }} logger The logger instance.\n * @param {Array<{ afterEvaluation?: (hookContext: object, data: object, result: object) => object }>} hooks The array of hook instances.\n * @param {{ flagKey: string, context: object, defaultValue: any }} hookContext The context for the evaluation series.\n * @param {Array<object>} updatedData The data collected from the 'beforeEvaluation' stages.\n * @param {{ value: any, variationIndex?: number, reason?: object }} result The result of the flag evaluation.\n * @returns {void}\n */\nfunction executeAfterEvaluation(logger, hooks, hookContext, updatedData, result) {\n  // This iterates in reverse, versus reversing a shallow copy of the hooks,\n  // for efficiency.\n  for (let hookIndex = hooks.length - 1; hookIndex >= 0; hookIndex -= 1) {\n    const hook = hooks[hookIndex];\n    const data = updatedData[hookIndex];\n    tryExecuteStage(\n      logger,\n      AFTER_EVALUATION_STAGE_NAME,\n      getHookName(logger, hook),\n      () => hook?.afterEvaluation?.(hookContext, data, result) ?? {},\n      {}\n    );\n  }\n}\n\n/**\n * Executes the 'beforeIdentify' stage for all registered hooks.\n * @param {{ error: (message: string) => void }} logger The logger instance.\n * @param {Array<{ beforeIdentify?: (hookContext: object, data: object) => object }>} hooks The array of hook instances.\n * @param {{ context: object, timeout?: number }} hookContext The context for the identify series.\n * @returns {Array<object>} An array containing the data returned by each hook's 'beforeIdentify' stage.\n */\nfunction executeBeforeIdentify(logger, hooks, hookContext) {\n  return hooks.map(hook =>\n    tryExecuteStage(\n      logger,\n      BEFORE_IDENTIFY_STAGE_NAME,\n      getHookName(logger, hook),\n      () => hook?.beforeIdentify?.(hookContext, {}) ?? {},\n      {}\n    )\n  );\n}\n\n/**\n * Executes the 'afterIdentify' stage for all registered hooks in reverse order.\n * @param {{ error: (message: string) => void }} logger The logger instance.\n * @param {Array<{ afterIdentify?: (hookContext: object, data: object, result: object) => object }>} hooks The array of hook instances.\n * @param {{ context: object, timeout?: number }} hookContext The context for the identify series.\n * @param {Array<object>} updatedData The data collected from the 'beforeIdentify' stages.\n * @param {{ status: string }} result The result of the identify operation.\n * @returns {void}\n */\nfunction executeAfterIdentify(logger, hooks, hookContext, updatedData, result) {\n  // This iterates in reverse, versus reversing a shallow copy of the hooks,\n  // for efficiency.\n  for (let hookIndex = hooks.length - 1; hookIndex >= 0; hookIndex -= 1) {\n    const hook = hooks[hookIndex];\n    const data = updatedData[hookIndex];\n    tryExecuteStage(\n      logger,\n      AFTER_IDENTIFY_STAGE_NAME,\n      getHookName(logger, hook),\n      () => hook?.afterIdentify?.(hookContext, data, result) ?? {},\n      {}\n    );\n  }\n}\n\n/**\n * Executes the 'afterTrack' stage for all registered hooks in reverse order.\n * @param {{ error: (message: string) => void }} logger The logger instance.\n * @param {Array<{ afterTrack?: (hookContext: { context: object, data: object, metricValue: number }) => void }>} hooks The array of hook instances.\n * @param {{ context: object, data: object, metricValue: number }} hookContext The context for the track operation.\n * @returns {void}\n */\nfunction executeAfterTrack(logger, hooks, hookContext) {\n  // This iterates in reverse, versus reversing a shallow copy of the hooks,\n  // for efficiency.\n  for (let hookIndex = hooks.length - 1; hookIndex >= 0; hookIndex -= 1) {\n    const hook = hooks[hookIndex];\n    tryExecuteStage(\n      logger,\n      AFTER_TRACK_STAGE_NAME,\n      getHookName(logger, hook),\n      () => hook?.afterTrack?.(hookContext),\n      undefined\n    );\n  }\n}\n\n/**\n * Factory function to create a HookRunner instance.\n * Manages the execution of hooks for flag evaluations and identify operations.\n * @param {{ error: (message: string) => void }} logger The logger instance.\n * @param {Array<object> | undefined} initialHooks An optional array of hooks to initialize with.\n * @returns {{\n *  withEvaluation: (key: string, context: object, defaultValue: any, method: () => { value: any, variationIndex?: number, reason?: object }) => { value: any, variationIndex?: number, reason?: object },\n *  identify: (context: object, timeout?: number) => (result: { status: string }) => void,\n *  addHook: (hook: object) => void\n * }} The hook runner object with methods to manage and execute hooks.\n */\nfunction createHookRunner(logger, initialHooks) {\n  // Use local variable instead of instance property\n  const hooksInternal = initialHooks ? [...initialHooks] : [];\n\n  /**\n   * Wraps a flag evaluation method with before/after hook stages.\n   * @param {string} key The flag key.\n   * @param {object} context The evaluation context.\n   * @param {any} defaultValue The default value for the flag.\n   * @param {() => { value: any, variationIndex?: number, reason?: object }} method The function that performs the actual flag evaluation.\n   * @returns {{ value: any, variationIndex?: number, reason?: object }} The result of the flag evaluation.\n   */\n  function withEvaluation(key, context, defaultValue, method) {\n    if (hooksInternal.length === 0) {\n      return method();\n    }\n    const hooks = [...hooksInternal];\n    /** @type {{ flagKey: string, context: object, defaultValue: any }} */\n    const hookContext = {\n      flagKey: key,\n      context,\n      defaultValue,\n    };\n\n    // Use the logger passed into the factory\n    const hookData = executeBeforeEvaluation(logger, hooks, hookContext);\n    const result = method();\n    executeAfterEvaluation(logger, hooks, hookContext, hookData, result);\n    return result;\n  }\n\n  /**\n   * Wraps the identify operation with before/after hook stages.\n   * Executes the 'beforeIdentify' stage immediately and returns a function\n   * to execute the 'afterIdentify' stage later.\n   * @param {object} context The context being identified.\n   * @param {number | undefined} timeout Optional timeout for the identify operation.\n   * @returns {(result: { status: string }) => void} A function to call after the identify operation completes.\n   */\n  function identify(context, timeout) {\n    const hooks = [...hooksInternal];\n    /** @type {{ context: object, timeout?: number }} */\n    const hookContext = {\n      context,\n      timeout,\n    };\n    // Use the logger passed into the factory\n    const hookData = executeBeforeIdentify(logger, hooks, hookContext);\n    /**\n     * Executes the 'afterIdentify' hook stage.\n     * @param {{ status: string }} result The result of the identify operation.\n     */\n    return result => {\n      executeAfterIdentify(logger, hooks, hookContext, hookData, result);\n    };\n  }\n\n  /**\n   * Adds a new hook to the runner.\n   * @param {object} hook The hook instance to add.\n   * @returns {void}\n   */\n  function addHook(hook) {\n    // Mutate the internal hooks array\n    hooksInternal.push(hook);\n  }\n\n  /**\n   * Executes the 'afterTrack' stage for all registered hooks in reverse order.\n   * @param {{ context: object, data: object, metricValue: number }} hookContext The context for the track operation.\n   * @returns {void}\n   */\n  function afterTrack(hookContext) {\n    if (hooksInternal.length === 0) {\n      return;\n    }\n    const hooks = [...hooksInternal];\n    executeAfterTrack(logger, hooks, hookContext);\n  }\n\n  return {\n    withEvaluation,\n    identify,\n    addHook,\n    afterTrack,\n  };\n}\n\nmodule.exports = createHookRunner;\n", "const UNKNOWN_PLUGIN_NAME = 'unknown plugin';\n\n/**\n * Safely gets the name of a plugin with error handling\n * @param {{ error: (message: string) => void }} logger - The logger instance\n * @param {{getMetadata: () => {name: string}}} plugin - Plugin object that may have a name property\n * @returns {string} The plugin name or 'unknown' if not available\n */\nfunction getPluginName(logger, plugin) {\n  try {\n    return plugin.getMetadata().name || UNKNOWN_PLUGIN_NAME;\n  } catch (error) {\n    logger.error(`Exception thrown getting metadata for plugin. Unable to get plugin name.`);\n    return UNKNOWN_PLUGIN_NAME;\n  }\n}\n\n/**\n * Safely retrieves hooks from plugins with error handling\n * @param {Object} logger - The logger instance\n * @param {Object} environmentMetadata - Metadata about the environment for plugin initialization\n * @param {Array<{getHooks: (environmentMetadata: object) => Hook[]}>} plugins - Array of plugin objects that may implement getHooks\n * @returns {Array<Hook>} Array of hook objects collected from all plugins\n */\nfunction getPluginHooks(logger, environmentMetadata, plugins) {\n  const hooks = [];\n  plugins.forEach(plugin => {\n    try {\n      const pluginHooks = plugin.getHooks?.(environmentMetadata);\n      if (pluginHooks === undefined) {\n        logger.error(`Plugin ${getPluginName(logger, plugin)} returned undefined from getHooks.`);\n      } else if (pluginHooks && pluginHooks.length > 0) {\n        hooks.push(...pluginHooks);\n      }\n    } catch (error) {\n      logger.error(`Exception thrown getting hooks for plugin ${getPluginName(logger, plugin)}. Unable to get hooks.`);\n    }\n  });\n  return hooks;\n}\n\n/**\n * Registers plugins with the SDK\n * @param {{ error: (message: string) => void }} logger - The logger instance\n * @param {Object} environmentMetadata - Metadata about the environment for plugin initialization\n * @param {Object} client - The SDK client instance\n * @param {Array<{register: (client: object, environmentMetadata: object) => void}>} plugins - Array of plugin objects that implement register\n */\nfunction registerPlugins(logger, environmentMetadata, client, plugins) {\n  plugins.forEach(plugin => {\n    try {\n      plugin.register(client, environmentMetadata);\n    } catch (error) {\n      logger.error(`Exception thrown registering plugin ${getPluginName(logger, plugin)}.`);\n    }\n  });\n}\n\n/**\n * Creates a plugin environment object\n * @param {{userAgent: string, version: string}} platform - The platform object\n * @param {string} env - The environment\n * @param {{application: {name: string, version: string}, wrapperName: string, wrapperVersion: string}} options - The options\n * @returns {{sdk: {name: string, version: string, wrapperName: string, wrapperVersion: string}, application: {name: string, version: string}, clientSideId: string}} The plugin environment\n */\nfunction createPluginEnvironment(platform, env, options) {\n  const pluginSdkMetadata = {};\n\n  if (platform.userAgent) {\n    pluginSdkMetadata.name = platform.userAgent;\n  }\n  if (platform.version) {\n    pluginSdkMetadata.version = platform.version;\n  }\n  if (options.wrapperName) {\n    pluginSdkMetadata.wrapperName = options.wrapperName;\n  }\n  if (options.wrapperVersion) {\n    pluginSdkMetadata.wrapperVersion = options.wrapperVersion;\n  }\n\n  const pluginApplicationMetadata = {};\n\n  if (options.application) {\n    if (options.application.name) {\n      pluginApplicationMetadata.name = options.application.name;\n    }\n    if (options.application.version) {\n      pluginApplicationMetadata.version = options.application.version;\n    }\n  }\n\n  const pluginEnvironment = {\n    sdk: pluginSdkMetadata,\n    clientSideId: env,\n  };\n\n  if (Object.keys(pluginApplicationMetadata).length > 0) {\n    pluginEnvironment.application = pluginApplicationMetadata;\n  }\n\n  return pluginEnvironment;\n}\n\nmodule.exports = {\n  getPluginHooks,\n  registerPlugins,\n  createPluginEnvironment,\n};\n", "const EventProcessor = require('./EventProcessor');\nconst EventEmitter = require('./EventEmitter');\nconst EventSender = require('./EventSender');\nconst InitializationStateTracker = require('./InitializationState');\nconst PersistentFlagStore = require('./PersistentFlagStore');\nconst PersistentStorage = require('./PersistentStorage');\nconst Stream = require('./Stream');\nconst Requestor = require('./Requestor');\nconst Identity = require('./Identity');\nconst AnonymousContextProcessor = require('./AnonymousContextProcessor');\nconst configuration = require('./configuration');\nconst diagnostics = require('./diagnosticEvents');\nconst { commonBasicLogger } = require('./loggers');\nconst utils = require('./utils');\nconst errors = require('./errors');\nconst messages = require('./messages');\nconst { checkContext, getContextKeys } = require('./context');\nconst { InspectorTypes, InspectorManager } = require('./InspectorManager');\nconst timedPromise = require('./timedPromise');\nconst createHookRunner = require('./HookRunner');\nconst { getPluginHooks, registerPlugins, createPluginEnvironment } = require('./plugins');\nconst changeEvent = 'change';\nconst internalChangeEvent = 'internal-change';\nconst highTimeoutThreshold = 5;\n\n// This is called by the per-platform initialize functions to create the base client object that we\n// may also extend with additional behavior. It returns an object with these properties:\n//   client: the actual client object\n//   options: the configuration (after any appropriate defaults have been applied)\n// If we need to give the platform-specific clients access to any internals here, we should add those\n// as properties of the return object, not public properties of the client.\n//\n// For definitions of the API in the platform object, see stubPlatform.js in the test code.\n\nfunction initialize(env, context, specifiedOptions, platform, extraOptionDefs) {\n  const logger = createLogger();\n  const emitter = EventEmitter(logger);\n  const initializationStateTracker = InitializationStateTracker(emitter);\n  const options = configuration.validate(specifiedOptions, emitter, extraOptionDefs, logger);\n  const inspectorManager = InspectorManager(options.inspectors, logger);\n  const sendEvents = options.sendEvents;\n  let environment = env;\n  let hash = options.hash;\n  const plugins = [...options.plugins];\n\n  const pluginEnvironment = createPluginEnvironment(platform, env, options);\n\n  const pluginHooks = getPluginHooks(logger, pluginEnvironment, plugins);\n\n  const hookRunner = createHookRunner(logger, [...options.hooks, ...pluginHooks]);\n\n  const persistentStorage = PersistentStorage(platform.localStorage, logger);\n\n  const eventSender = EventSender(platform, environment, options);\n\n  const diagnosticsEnabled = options.sendEvents && !options.diagnosticOptOut;\n  const diagnosticId = diagnosticsEnabled ? diagnostics.DiagnosticId(environment) : null;\n  const diagnosticsAccumulator = diagnosticsEnabled ? diagnostics.DiagnosticsAccumulator(new Date().getTime()) : null;\n  const diagnosticsManager = diagnosticsEnabled\n    ? diagnostics.DiagnosticsManager(\n        platform,\n        persistentStorage,\n        diagnosticsAccumulator,\n        eventSender,\n        environment,\n        options,\n        diagnosticId\n      )\n    : null;\n\n  const stream = Stream(platform, options, environment, diagnosticsAccumulator);\n\n  const events =\n    options.eventProcessor ||\n    EventProcessor(platform, options, environment, diagnosticsAccumulator, emitter, eventSender);\n\n  const requestor = Requestor(platform, options, environment);\n\n  let flags = {};\n  let useLocalStorage;\n  let streamActive;\n  let streamForcedState = options.streaming;\n  let subscribedToChangeEvents;\n  let inited = false;\n  let closed = false;\n  let firstEvent = true;\n\n  // The \"stateProvider\" object is used in the Electron SDK, to allow one client instance to take partial\n  // control of another. If present, it has the following contract:\n  // - getInitialState() returns the initial client state if it is already available. The state is an\n  //   object whose properties are \"environment\", \"context\", and \"flags\".\n  // - on(\"init\", listener) triggers an event when the initial client state becomes available, passing\n  //   the state object to the listener.\n  // - on(\"update\", listener) triggers an event when flag values change and/or the current context changes.\n  //   The parameter is an object that *may* contain \"context\" and/or \"flags\".\n  // - enqueueEvent(event) accepts an analytics event object and returns true if the stateProvider will\n  //   be responsible for delivering it, or false if we still should deliver it ourselves.\n  const stateProvider = options.stateProvider;\n\n  const ident = Identity(null, onIdentifyChange);\n  const anonymousContextProcessor = new AnonymousContextProcessor(persistentStorage);\n  const persistentFlagStore = persistentStorage.isEnabled()\n    ? PersistentFlagStore(persistentStorage, environment, hash, ident, logger)\n    : null;\n\n  function createLogger() {\n    if (specifiedOptions && specifiedOptions.logger) {\n      return specifiedOptions.logger;\n    }\n    return (extraOptionDefs && extraOptionDefs.logger && extraOptionDefs.logger.default) || commonBasicLogger('warn');\n  }\n\n  function readFlagsFromBootstrap(data) {\n    // If the bootstrap data came from an older server-side SDK, we'll have just a map of keys to values.\n    // Newer SDKs that have an allFlagsState method will provide an extra \"$flagsState\" key that contains\n    // the rest of the metadata we want. We do it this way for backward compatibility with older JS SDKs.\n    const keys = Object.keys(data);\n    const metadataKey = '$flagsState';\n    const validKey = '$valid';\n    const metadata = data[metadataKey];\n    if (!metadata && keys.length) {\n      logger.warn(messages.bootstrapOldFormat());\n    }\n    if (data[validKey] === false) {\n      logger.warn(messages.bootstrapInvalid());\n    }\n    const ret = {};\n    keys.forEach(key => {\n      if (key !== metadataKey && key !== validKey) {\n        let flag = { value: data[key] };\n        if (metadata && metadata[key]) {\n          flag = utils.extend(flag, metadata[key]);\n        } else {\n          flag.version = 0;\n        }\n        ret[key] = flag;\n      }\n    });\n    return ret;\n  }\n\n  function shouldEnqueueEvent() {\n    return sendEvents && !closed && !platform.isDoNotTrack();\n  }\n\n  function enqueueEvent(event) {\n    if (!environment) {\n      // We're in paired mode and haven't been initialized with an environment or context yet\n      return;\n    }\n    if (stateProvider && stateProvider.enqueueEvent && stateProvider.enqueueEvent(event)) {\n      return; // it'll be handled elsewhere\n    }\n\n    if (!event.context) {\n      if (firstEvent) {\n        logger.warn(messages.eventWithoutContext());\n        firstEvent = false;\n      }\n      return;\n    }\n    firstEvent = false;\n\n    if (shouldEnqueueEvent()) {\n      logger.debug(messages.debugEnqueueingEvent(event.kind));\n      events.enqueue(event);\n    }\n  }\n\n  function notifyInspectionFlagUsed(key, detail) {\n    if (inspectorManager.hasListeners(InspectorTypes.flagUsed)) {\n      inspectorManager.onFlagUsed(key, detail, ident.getContext());\n    }\n  }\n\n  function notifyInspectionIdentityChanged() {\n    if (inspectorManager.hasListeners(InspectorTypes.clientIdentityChanged)) {\n      inspectorManager.onIdentityChanged(ident.getContext());\n    }\n  }\n\n  function notifyInspectionFlagChanged(data, newFlag) {\n    if (inspectorManager.hasListeners(InspectorTypes.flagDetailChanged)) {\n      inspectorManager.onFlagChanged(data.key, getFlagDetail(newFlag));\n    }\n  }\n\n  function notifyInspectionFlagsChanged() {\n    if (inspectorManager.hasListeners(InspectorTypes.flagDetailsChanged)) {\n      inspectorManager.onFlags(\n        Object.entries(flags)\n          .map(([key, value]) => ({ key, detail: getFlagDetail(value) }))\n          .reduce((acc, cur) => {\n            // eslint-disable-next-line no-param-reassign\n            acc[cur.key] = cur.detail;\n            return acc;\n          }, {})\n      );\n    }\n  }\n\n  function onIdentifyChange(context) {\n    sendIdentifyEvent(context);\n    notifyInspectionIdentityChanged();\n  }\n\n  function sendIdentifyEvent(context) {\n    if (stateProvider) {\n      // In paired mode, the other client is responsible for sending identify events\n      return;\n    }\n    if (context) {\n      enqueueEvent({\n        kind: 'identify',\n        context,\n        creationDate: new Date().getTime(),\n      });\n    }\n  }\n\n  function sendFlagEvent(key, detail, defaultValue, includeReason) {\n    const context = ident.getContext();\n    const now = new Date();\n    const value = detail ? detail.value : null;\n\n    const event = {\n      kind: 'feature',\n      key: key,\n      context,\n      value: value,\n      variation: detail ? detail.variationIndex : null,\n      default: defaultValue,\n      creationDate: now.getTime(),\n    };\n    const flag = flags[key];\n    if (flag) {\n      event.version = flag.flagVersion ? flag.flagVersion : flag.version;\n      event.trackEvents = flag.trackEvents;\n      event.debugEventsUntilDate = flag.debugEventsUntilDate;\n    }\n    if ((includeReason || (flag && flag.trackReason)) && detail) {\n      event.reason = detail.reason;\n    }\n\n    enqueueEvent(event);\n  }\n\n  function verifyContext(context) {\n    // The context will already have been processed to have a string key, so we\n    // do not need to allow for legacy keys in the check.\n    if (checkContext(context, false)) {\n      return Promise.resolve(context);\n    } else {\n      return Promise.reject(new errors.LDInvalidUserError(messages.invalidContext()));\n    }\n  }\n\n  function identify(context, newHash, onDone) {\n    if (closed) {\n      return utils.wrapPromiseCallback(Promise.resolve({}), onDone);\n    }\n    if (stateProvider) {\n      // We're being controlled by another client instance, so only that instance is allowed to change the context\n      logger.warn(messages.identifyDisabled());\n      return utils.wrapPromiseCallback(Promise.resolve(utils.transformVersionedValuesToValues(flags)), onDone);\n    }\n    let afterIdentify;\n    const clearFirst = useLocalStorage && persistentFlagStore ? persistentFlagStore.clearFlags() : Promise.resolve();\n    return utils.wrapPromiseCallback(\n      clearFirst\n        .then(() => anonymousContextProcessor.processContext(context))\n        .then(verifyContext)\n        .then(context => {\n          afterIdentify = utils.once(hookRunner.identify(context, undefined));\n          return context;\n        })\n        .then(validatedContext =>\n          requestor\n            .fetchFlagSettings(validatedContext, newHash)\n            // the following then() is nested within this one so we can use realUser from the previous closure\n            .then(requestedFlags => {\n              const flagValueMap = utils.transformVersionedValuesToValues(requestedFlags);\n              ident.setContext(validatedContext);\n              hash = newHash;\n              if (requestedFlags) {\n                return replaceAllFlags(requestedFlags).then(() => flagValueMap);\n              } else {\n                return flagValueMap;\n              }\n            })\n        )\n        .then(flagValueMap => {\n          afterIdentify?.({ status: 'completed' });\n          if (streamActive) {\n            connectStream();\n          }\n          return flagValueMap;\n        })\n        .catch(err => {\n          afterIdentify?.({ status: 'error' });\n          emitter.maybeReportError(err);\n          return Promise.reject(err);\n        }),\n      onDone\n    );\n  }\n\n  function getContext() {\n    return ident.getContext();\n  }\n\n  function flush(onDone) {\n    return utils.wrapPromiseCallback(sendEvents ? events.flush() : Promise.resolve(), onDone);\n  }\n\n  function variation(key, defaultValue) {\n    const { value } = hookRunner.withEvaluation(key, ident.getContext(), defaultValue, () =>\n      variationDetailInternal(key, defaultValue, true, false, false, true)\n    );\n    return value;\n  }\n\n  function variationDetail(key, defaultValue) {\n    return hookRunner.withEvaluation(key, ident.getContext(), defaultValue, () =>\n      variationDetailInternal(key, defaultValue, true, true, false, true)\n    );\n  }\n\n  function variationDetailInternal(key, defaultValue, sendEvent, includeReasonInEvent, isAllFlags, notifyInspection) {\n    let detail;\n    let flag;\n\n    if (flags && utils.objectHasOwnProperty(flags, key) && flags[key] && !flags[key].deleted) {\n      flag = flags[key];\n      detail = getFlagDetail(flag);\n      if (flag.value === null || flag.value === undefined) {\n        detail.value = defaultValue;\n      }\n    } else {\n      detail = { value: defaultValue, variationIndex: null, reason: { kind: 'ERROR', errorKind: 'FLAG_NOT_FOUND' } };\n    }\n\n    if (sendEvent) {\n      // For an all-flags evaluation, with events enabled, each flag will get an event, so we do not\n      // need to duplicate the prerequisites.\n      if (!isAllFlags) {\n        flag?.prerequisites?.forEach(key => {\n          variationDetailInternal(key, undefined, sendEvent, false, false, false);\n        });\n      }\n      sendFlagEvent(key, detail, defaultValue, includeReasonInEvent);\n    }\n\n    // For the all flags case `onFlags` will be called instead.\n    if (!isAllFlags && notifyInspection) {\n      notifyInspectionFlagUsed(key, detail);\n    }\n\n    return detail;\n  }\n\n  function getFlagDetail(flag) {\n    return {\n      value: flag.value,\n      variationIndex: flag.variation === undefined ? null : flag.variation,\n      reason: flag.reason || null,\n    };\n    // Note, the logic above ensures that variationIndex and reason will always be null rather than\n    // undefined if we don't have values for them. That's just to avoid subtle errors that depend on\n    // whether an object was JSON-encoded with null properties omitted or not.\n  }\n\n  function allFlags() {\n    const results = {};\n\n    if (!flags) {\n      return results;\n    }\n\n    for (const key in flags) {\n      if (utils.objectHasOwnProperty(flags, key) && !flags[key].deleted) {\n        results[key] = variationDetailInternal(\n          key,\n          null,\n          !options.sendEventsOnlyForVariation,\n          false,\n          true,\n          false\n        ).value;\n      }\n    }\n\n    return results;\n  }\n\n  function userContextKind(user) {\n    return user.anonymous ? 'anonymousUser' : 'user';\n  }\n\n  function track(key, data, metricValue) {\n    if (typeof key !== 'string') {\n      emitter.maybeReportError(new errors.LDInvalidEventKeyError(messages.unknownCustomEventKey(key)));\n      return;\n    }\n    if (metricValue !== undefined && typeof metricValue !== 'number') {\n      logger.warn(messages.invalidMetricValue(typeof metricValue));\n    }\n\n    // The following logic was used only for the JS browser SDK (js-client-sdk) and\n    // is no longer needed as of version 2.9.13 of that SDK. The other client-side\n    // JS-based SDKs did not define customEventFilter, and now none of them do. We\n    // can remove this in the next major version of the common code, when it's OK to\n    // make breaking changes to our internal API contracts.\n    if (platform.customEventFilter && !platform.customEventFilter(key)) {\n      logger.warn(messages.unknownCustomEventKey(key));\n    }\n\n    const context = ident.getContext();\n    const e = {\n      kind: 'custom',\n      key: key,\n      context,\n      url: platform.getCurrentUrl(),\n      creationDate: new Date().getTime(),\n    };\n    if (context && context.anonymous) {\n      e.contextKind = userContextKind(context);\n    }\n    // Note, check specifically for null/undefined because it is legal to set these fields to a falsey value.\n    if (data !== null && data !== undefined) {\n      e.data = data;\n    }\n    if (metricValue !== null && metricValue !== undefined) {\n      e.metricValue = metricValue;\n    }\n    enqueueEvent(e);\n    hookRunner.afterTrack({ context, key, data, metricValue });\n  }\n\n  function connectStream() {\n    streamActive = true;\n    if (!ident.getContext()) {\n      return;\n    }\n    const tryParseData = jsonData => {\n      try {\n        return JSON.parse(jsonData);\n      } catch (err) {\n        emitter.maybeReportError(new errors.LDInvalidDataError(messages.invalidData()));\n        return undefined;\n      }\n    };\n    stream.connect(ident.getContext(), hash, {\n      ping: function() {\n        logger.debug(messages.debugStreamPing());\n        const contextAtTimeOfPingEvent = ident.getContext();\n        requestor\n          .fetchFlagSettings(contextAtTimeOfPingEvent, hash)\n          .then(requestedFlags => {\n            // Check whether the current context is still the same - we don't want to overwrite the flags if\n            // the application has called identify() while this request was in progress\n            if (utils.deepEquals(contextAtTimeOfPingEvent, ident.getContext())) {\n              replaceAllFlags(requestedFlags || {});\n            }\n          })\n          .catch(err => {\n            emitter.maybeReportError(new errors.LDFlagFetchError(messages.errorFetchingFlags(err)));\n          });\n      },\n      put: function(e) {\n        const data = tryParseData(e.data);\n        if (!data) {\n          return;\n        }\n        logger.debug(messages.debugStreamPut());\n        replaceAllFlags(data);\n        // Don't wait for this Promise to be resolved; note that replaceAllFlags is guaranteed\n        // never to have an unhandled rejection\n      },\n      patch: function(e) {\n        const data = tryParseData(e.data);\n        if (!data) {\n          return;\n        }\n        // If both the flag and the patch have a version property, then the patch version must be\n        // greater than the flag version for us to accept the patch.  If either one has no version\n        // then the patch always succeeds.\n        const oldFlag = flags[data.key];\n        if (!oldFlag || !oldFlag.version || !data.version || oldFlag.version < data.version) {\n          logger.debug(messages.debugStreamPatch(data.key));\n          const mods = {};\n          const newFlag = utils.extend({}, data);\n          delete newFlag['key'];\n          flags[data.key] = newFlag;\n          const newDetail = getFlagDetail(newFlag);\n          if (oldFlag) {\n            mods[data.key] = { previous: oldFlag.value, current: newDetail };\n          } else {\n            mods[data.key] = { current: newDetail };\n          }\n          notifyInspectionFlagChanged(data, newFlag);\n          handleFlagChanges(mods); // don't wait for this Promise to be resolved\n        } else {\n          logger.debug(messages.debugStreamPatchIgnored(data.key));\n        }\n      },\n      delete: function(e) {\n        const data = tryParseData(e.data);\n        if (!data) {\n          return;\n        }\n        if (!flags[data.key] || flags[data.key].version < data.version) {\n          logger.debug(messages.debugStreamDelete(data.key));\n          const mods = {};\n          if (flags[data.key] && !flags[data.key].deleted) {\n            mods[data.key] = { previous: flags[data.key].value };\n          }\n          flags[data.key] = { version: data.version, deleted: true };\n          notifyInspectionFlagChanged(data, flags[data.key]);\n          handleFlagChanges(mods); // don't wait for this Promise to be resolved\n        } else {\n          logger.debug(messages.debugStreamDeleteIgnored(data.key));\n        }\n      },\n    });\n  }\n\n  function disconnectStream() {\n    if (streamActive) {\n      stream.disconnect();\n      streamActive = false;\n    }\n  }\n\n  // Returns a Promise which will be resolved when we have completely updated the internal flags state,\n  // dispatched all change events, and updated local storage if appropriate. This Promise is guaranteed\n  // never to have an unhandled rejection.\n  function replaceAllFlags(newFlags) {\n    const changes = {};\n\n    if (!newFlags) {\n      return Promise.resolve();\n    }\n\n    for (const key in flags) {\n      if (utils.objectHasOwnProperty(flags, key) && flags[key]) {\n        if (newFlags[key] && !utils.deepEquals(newFlags[key].value, flags[key].value)) {\n          changes[key] = { previous: flags[key].value, current: getFlagDetail(newFlags[key]) };\n        } else if (!newFlags[key] || newFlags[key].deleted) {\n          changes[key] = { previous: flags[key].value };\n        }\n      }\n    }\n    for (const key in newFlags) {\n      if (utils.objectHasOwnProperty(newFlags, key) && newFlags[key] && (!flags[key] || flags[key].deleted)) {\n        changes[key] = { current: getFlagDetail(newFlags[key]) };\n      }\n    }\n\n    flags = { ...newFlags };\n\n    notifyInspectionFlagsChanged();\n\n    return handleFlagChanges(changes).catch(() => {}); // swallow any exceptions from this Promise\n  }\n\n  // Returns a Promise which will be resolved when we have dispatched all change events and updated\n  // local storage if appropriate.\n  function handleFlagChanges(changes) {\n    const keys = Object.keys(changes);\n\n    if (keys.length > 0) {\n      const changeEventParams = {};\n      keys.forEach(key => {\n        const current = changes[key].current;\n        const value = current ? current.value : undefined;\n        const previous = changes[key].previous;\n        emitter.emit(changeEvent + ':' + key, value, previous);\n        changeEventParams[key] = current ? { current: value, previous: previous } : { previous: previous };\n      });\n\n      emitter.emit(changeEvent, changeEventParams);\n      emitter.emit(internalChangeEvent, flags);\n\n      // By default, we send feature evaluation events whenever we have received new flag values -\n      // the client has in effect evaluated these flags just by receiving them. This can be suppressed\n      // by setting \"sendEventsOnlyForVariation\". Also, if we have a stateProvider, we don't send these\n      // events because we assume they have already been sent by the other client that gave us the flags\n      // (when it received them in the first place).\n      if (!options.sendEventsOnlyForVariation && !stateProvider) {\n        keys.forEach(key => {\n          sendFlagEvent(key, changes[key].current);\n        });\n      }\n    }\n\n    if (useLocalStorage && persistentFlagStore) {\n      return persistentFlagStore.saveFlags(flags);\n    } else {\n      return Promise.resolve();\n    }\n  }\n\n  function on(event, handler, context) {\n    if (isChangeEventKey(event)) {\n      subscribedToChangeEvents = true;\n      if (inited) {\n        updateStreamingState();\n      }\n      emitter.on(event, handler, context);\n    } else {\n      emitter.on(...arguments);\n    }\n  }\n\n  function off(event) {\n    emitter.off(...arguments);\n    if (isChangeEventKey(event)) {\n      let haveListeners = false;\n      emitter.getEvents().forEach(key => {\n        if (isChangeEventKey(key) && emitter.getEventListenerCount(key) > 0) {\n          haveListeners = true;\n        }\n      });\n      if (!haveListeners) {\n        subscribedToChangeEvents = false;\n        if (streamActive && streamForcedState === undefined) {\n          disconnectStream();\n        }\n      }\n    }\n  }\n\n  function setStreaming(state) {\n    const newState = state === null ? undefined : state;\n    if (newState !== streamForcedState) {\n      streamForcedState = newState;\n      updateStreamingState();\n    }\n  }\n\n  function updateStreamingState() {\n    const shouldBeStreaming = streamForcedState || (subscribedToChangeEvents && streamForcedState === undefined);\n    if (shouldBeStreaming && !streamActive) {\n      connectStream();\n    } else if (!shouldBeStreaming && streamActive) {\n      disconnectStream();\n    }\n    if (diagnosticsManager) {\n      diagnosticsManager.setStreaming(shouldBeStreaming);\n    }\n  }\n\n  function isChangeEventKey(event) {\n    return event === changeEvent || event.substr(0, changeEvent.length + 1) === changeEvent + ':';\n  }\n\n  if (typeof options.bootstrap === 'string' && options.bootstrap.toUpperCase() === 'LOCALSTORAGE') {\n    if (persistentFlagStore) {\n      useLocalStorage = true;\n    } else {\n      logger.warn(messages.localStorageUnavailable());\n    }\n  }\n\n  if (typeof options.bootstrap === 'object') {\n    // Set the flags as soon as possible before we get into any async code, so application code can read\n    // them even if the ready event has not yet fired.\n    flags = readFlagsFromBootstrap(options.bootstrap);\n  }\n\n  if (stateProvider) {\n    // The stateProvider option is used in the Electron SDK, to allow a client instance in the main process\n    // to control another client instance (i.e. this one) in the renderer process. We can't predict which\n    // one will start up first, so the initial state may already be available for us or we may have to wait\n    // to receive it.\n    const state = stateProvider.getInitialState();\n    if (state) {\n      initFromStateProvider(state);\n    } else {\n      stateProvider.on('init', initFromStateProvider);\n    }\n    stateProvider.on('update', updateFromStateProvider);\n  } else {\n    finishInit().catch(signalFailedInit);\n  }\n\n  function finishInit() {\n    if (!env) {\n      return Promise.reject(new errors.LDInvalidEnvironmentIdError(messages.environmentNotSpecified()));\n    }\n    let afterIdentify;\n    return anonymousContextProcessor\n      .processContext(context)\n      .then(verifyContext)\n      .then(context => {\n        afterIdentify = utils.once(hookRunner.identify(context, undefined));\n        return context;\n      })\n      .then(validatedContext => {\n        afterIdentify?.({ status: 'completed' });\n        ident.setContext(validatedContext);\n        if (typeof options.bootstrap === 'object') {\n          // flags have already been set earlier\n          return signalSuccessfulInit();\n        } else if (useLocalStorage) {\n          return finishInitWithLocalStorage();\n        } else {\n          return finishInitWithPolling();\n        }\n      })\n      .catch(err => {\n        afterIdentify?.({ status: 'error' });\n        throw err;\n      });\n  }\n\n  function finishInitWithLocalStorage() {\n    return persistentFlagStore.loadFlags().then(storedFlags => {\n      if (storedFlags === null || storedFlags === undefined) {\n        flags = {};\n        return requestor\n          .fetchFlagSettings(ident.getContext(), hash)\n          .then(requestedFlags => replaceAllFlags(requestedFlags || {}))\n          .then(signalSuccessfulInit)\n          .catch(err => {\n            const initErr = new errors.LDFlagFetchError(messages.errorFetchingFlags(err));\n            signalFailedInit(initErr);\n          });\n      } else {\n        // We're reading the flags from local storage. Signal that we're ready,\n        // then update localStorage for the next page load. We won't signal changes or update\n        // the in-memory flags unless you subscribe for changes\n        flags = storedFlags;\n        utils.onNextTick(signalSuccessfulInit);\n\n        return requestor\n          .fetchFlagSettings(ident.getContext(), hash)\n          .then(requestedFlags => replaceAllFlags(requestedFlags))\n          .catch(err => emitter.maybeReportError(err));\n      }\n    });\n  }\n\n  function finishInitWithPolling() {\n    return requestor\n      .fetchFlagSettings(ident.getContext(), hash)\n      .then(requestedFlags => {\n        flags = requestedFlags || {};\n\n        notifyInspectionFlagsChanged();\n        // Note, we don't need to call updateSettings here because local storage and change events are not relevant\n        signalSuccessfulInit();\n      })\n      .catch(err => {\n        flags = {};\n        signalFailedInit(err);\n      });\n  }\n\n  function initFromStateProvider(state) {\n    environment = state.environment;\n    ident.setContext(state.context);\n    flags = { ...state.flags };\n    utils.onNextTick(signalSuccessfulInit);\n  }\n\n  function updateFromStateProvider(state) {\n    if (state.context) {\n      ident.setContext(state.context);\n    }\n    if (state.flags) {\n      replaceAllFlags(state.flags); // don't wait for this Promise to be resolved\n    }\n  }\n\n  function signalSuccessfulInit() {\n    logger.info(messages.clientInitialized());\n    inited = true;\n    updateStreamingState();\n    initializationStateTracker.signalSuccess();\n  }\n\n  function signalFailedInit(err) {\n    initializationStateTracker.signalFailure(err);\n  }\n\n  function start() {\n    if (sendEvents) {\n      if (diagnosticsManager) {\n        diagnosticsManager.start();\n      }\n      events.start();\n    }\n  }\n\n  function close(onDone) {\n    if (closed) {\n      return utils.wrapPromiseCallback(Promise.resolve(), onDone);\n    }\n    const finishClose = () => {\n      closed = true;\n      flags = {};\n    };\n    const p = Promise.resolve()\n      .then(() => {\n        disconnectStream();\n        if (diagnosticsManager) {\n          diagnosticsManager.stop();\n        }\n        if (sendEvents) {\n          events.stop();\n          return events.flush();\n        }\n      })\n      .then(finishClose)\n      .catch(finishClose);\n    return utils.wrapPromiseCallback(p, onDone);\n  }\n\n  function getFlagsInternal() {\n    // used by Electron integration\n    return flags;\n  }\n\n  function waitForInitializationWithTimeout(timeout) {\n    if (timeout > highTimeoutThreshold) {\n      logger.warn(\n        'The waitForInitialization function was called with a timeout greater than ' +\n          `${highTimeoutThreshold} seconds. We recommend a timeout of ` +\n          `${highTimeoutThreshold} seconds or less.`\n      );\n    }\n\n    const initPromise = initializationStateTracker.getInitializationPromise();\n    const timeoutPromise = timedPromise(timeout, 'waitForInitialization');\n\n    return Promise.race([timeoutPromise, initPromise]).catch(e => {\n      if (e instanceof errors.LDTimeoutError) {\n        logger.error(`waitForInitialization error: ${e}`);\n      }\n      throw e;\n    });\n  }\n\n  function waitForInitialization(timeout = undefined) {\n    if (timeout !== undefined && timeout !== null) {\n      if (typeof timeout === 'number') {\n        return waitForInitializationWithTimeout(timeout);\n      }\n      logger.warn('The waitForInitialization method was provided with a non-numeric timeout.');\n    }\n    logger.warn(\n      'The waitForInitialization function was called without a timeout specified.' +\n        ' In a future version a default timeout will be applied.'\n    );\n    return initializationStateTracker.getInitializationPromise();\n  }\n\n  function addHook(hook) {\n    hookRunner.addHook(hook);\n  }\n\n  const client = {\n    waitForInitialization,\n    waitUntilReady: () => initializationStateTracker.getReadyPromise(),\n    identify: identify,\n    getContext: getContext,\n    variation: variation,\n    variationDetail: variationDetail,\n    track: track,\n    on: on,\n    off: off,\n    setStreaming: setStreaming,\n    flush: flush,\n    allFlags: allFlags,\n    close: close,\n    addHook: addHook,\n  };\n\n  registerPlugins(logger, pluginEnvironment, client, plugins);\n\n  return {\n    client: client, // The client object containing all public methods.\n    options: options, // The validated configuration object, including all defaults.\n    emitter: emitter, // The event emitter which can be used to log errors or trigger events.\n    ident: ident, // The Identity object that manages the current context.\n    logger: logger, // The logging abstraction.\n    requestor: requestor, // The Requestor object.\n    start: start, // Starts the client once the environment is ready.\n    enqueueEvent: enqueueEvent, // Puts an analytics event in the queue, if event sending is enabled.\n    getFlagsInternal: getFlagsInternal, // Returns flag data structure with all details.\n    getEnvironmentId: () => environment, // Gets the environment ID (this may have changed since initialization, if we have a state provider)\n    internalChangeEventName: internalChangeEvent, // This event is triggered whenever we have new flag state.\n  };\n}\n\nmodule.exports = {\n  initialize,\n  commonBasicLogger,\n  errors,\n  messages,\n  utils,\n  getContextKeys,\n};\n", "const { commonBasicLogger } = require('launchdarkly-js-sdk-common');\n\nfunction basicLogger(options) {\n  return commonBasicLogger({ destination: console.log, ...options });\n}\n\nmodule.exports = {\n  basicLogger,\n};\n", "function isSyncXhrSupported() {\n  // This is temporary logic to disable synchronous XHR in Chrome 73 and above. In all other browsers,\n  // we will assume it is supported. See https://github.com/launchdarkly/js-client-sdk/issues/147\n  const userAgent = window.navigator && window.navigator.userAgent;\n  if (userAgent) {\n    const chromeMatch = userAgent.match(/Chrom(e|ium)\\/([0-9]+)\\./);\n    if (chromeMatch) {\n      const version = parseInt(chromeMatch[2], 10);\n      return version < 73;\n    }\n  }\n  return true;\n}\n\nconst emptyResult = { promise: Promise.resolve({ status: 200, header: () => null, body: null }) };\n\nexport default function newHttpRequest(method, url, headers, body, pageIsClosing) {\n  if (pageIsClosing) {\n    // When the page is about to close, we have to use synchronous XHR (until we migrate to sendBeacon).\n    // But not all browsers support this.\n    if (!isSyncXhrSupported()) {\n      return emptyResult;\n      // Note that we return a fake success response, because we don't want the request to be retried in this case.\n    }\n  }\n\n  const xhr = new window.XMLHttpRequest();\n  xhr.open(method, url, !pageIsClosing);\n  for (const key in headers || {}) {\n    if (Object.prototype.hasOwnProperty.call(headers, key)) {\n      xhr.setRequestHeader(key, headers[key]);\n    }\n  }\n  if (pageIsClosing) {\n    try {\n      xhr.send(body); // We specified synchronous mode when we called xhr.open\n    } catch (e) {\n      // do nothing intentionally to suppress noise for now\n    }\n    return emptyResult; // Again, we never want a request to be retried in this case, so we must say it succeeded.\n  } else {\n    let cancelled;\n    const p = new Promise((resolve, reject) => {\n      xhr.addEventListener('load', () => {\n        if (cancelled) {\n          return;\n        }\n        resolve({\n          status: xhr.status,\n          header: (key) => xhr.getResponseHeader(key),\n          body: xhr.responseText,\n        });\n      });\n      xhr.addEventListener('error', () => {\n        if (cancelled) {\n          return;\n        }\n        reject(new Error());\n      });\n      xhr.send(body);\n    });\n    const cancel = () => {\n      cancelled = true;\n      xhr.abort();\n    };\n    return { promise: p, cancel: cancel };\n  }\n}\n", "'use strict';\n\nmodule.exports = string => {\n\tif (typeof string !== 'string') {\n\t\tthrow new TypeError('Expected a string');\n\t}\n\n\t// Escape characters with special meaning either inside or outside character sets.\n\t// Use a simple backslash escape when it’s always valid, and a \\unnnn escape when the simpler form would be disallowed by Unicode patterns’ stricter grammar.\n\treturn string\n\t\t.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&')\n\t\t.replace(/-/g, '\\\\x2d');\n};\n", "import escapeStringRegexp from 'escape-string-regexp';\n\nexport function doesUrlMatch(matcher, href, search, hash) {\n  const keepHash = (matcher.kind === 'substring' || matcher.kind === 'regex') && hash.includes('/');\n  const canonicalUrl = (keepHash ? href : href.replace(hash, '')).replace(search, '');\n\n  let regex;\n  let testUrl;\n\n  switch (matcher.kind) {\n    case 'exact':\n      testUrl = href;\n      regex = new RegExp('^' + escapeStringRegexp(matcher.url) + '/?$');\n      break;\n    case 'canonical':\n      testUrl = canonicalUrl;\n      regex = new RegExp('^' + escapeStringRegexp(matcher.url) + '/?$');\n      break;\n    case 'substring':\n      testUrl = canonicalUrl;\n      regex = new RegExp('.*' + escapeStringRegexp(matcher.substring) + '.*$');\n      break;\n    case 'regex':\n      testUrl = canonicalUrl;\n      regex = new RegExp(matcher.pattern);\n      break;\n    default:\n      return false;\n  }\n  return regex.test(testUrl);\n}\n\nfunction findGoalsForClick(event, clickGoals) {\n  const matches = [];\n\n  for (let i = 0; i < clickGoals.length; i++) {\n    let target = event.target;\n    const goal = clickGoals[i];\n    const selector = goal.selector;\n    const elements = document.querySelectorAll(selector);\n    while (target && elements.length > 0) {\n      for (let j = 0; j < elements.length; j++) {\n        if (target === elements[j]) {\n          matches.push(goal);\n        }\n      }\n      target = target.parentNode;\n    }\n  }\n\n  return matches;\n}\n\nexport default function GoalTracker(goals, onEvent) {\n  const tracker = {};\n  let listenerFn = null;\n\n  const clickGoals = [];\n\n  for (let i = 0; i < goals.length; i++) {\n    const goal = goals[i];\n    const urls = goal.urls || [];\n\n    for (let j = 0; j < urls.length; j++) {\n      if (doesUrlMatch(urls[j], window.location.href, window.location.search, window.location.hash)) {\n        if (goal.kind === 'pageview') {\n          onEvent('pageview', goal);\n        } else {\n          clickGoals.push(goal);\n          onEvent('click_pageview', goal);\n        }\n        break;\n      }\n    }\n  }\n\n  if (clickGoals.length > 0) {\n    listenerFn = function (event) {\n      const goals = findGoalsForClick(event, clickGoals);\n      for (let i = 0; i < goals.length; i++) {\n        onEvent('click', goals[i]);\n      }\n    };\n\n    document.addEventListener('click', listenerFn);\n  }\n\n  tracker.dispose = function () {\n    document.removeEventListener('click', listenerFn);\n  };\n\n  return tracker;\n}\n", "import * as common from 'launchdarkly-js-sdk-common';\nimport GoalTracker from './GoalTracker';\n\nconst locationWatcherInterval = 300;\n\nexport default function GoalManager(clientVars, readyCallback) {\n  let goals;\n  let goalTracker;\n\n  const ret = {};\n\n  function getGoalsPath() {\n    return '/sdk/goals/' + clientVars.getEnvironmentId();\n  }\n\n  function refreshGoalTracker() {\n    if (goalTracker) {\n      goalTracker.dispose();\n    }\n    if (goals && goals.length) {\n      goalTracker = GoalTracker(goals, sendGoalEvent);\n    }\n  }\n\n  function sendGoalEvent(kind, goal) {\n    const context = clientVars.ident.getContext();\n    const event = {\n      kind: kind,\n      key: goal.key,\n      data: null,\n      url: window.location.href,\n      creationDate: new Date().getTime(),\n      context: context,\n    };\n\n    if (kind === 'click') {\n      event.selector = goal.selector;\n    }\n\n    return clientVars.enqueueEvent(event);\n  }\n\n  function watchLocation(interval, callback) {\n    let previousUrl = window.location.href;\n    let currentUrl;\n\n    function checkUrl() {\n      currentUrl = window.location.href;\n\n      if (currentUrl !== previousUrl) {\n        previousUrl = currentUrl;\n        callback();\n      }\n    }\n\n    function poll(fn, interval) {\n      fn();\n      setTimeout(() => {\n        poll(fn, interval);\n      }, interval);\n    }\n\n    poll(checkUrl, interval);\n\n    if (window.history && window.history.pushState) {\n      window.addEventListener('popstate', checkUrl);\n    } else {\n      window.addEventListener('hashchange', checkUrl);\n    }\n  }\n\n  clientVars.requestor\n    .fetchJSON(getGoalsPath())\n    .then((g) => {\n      if (g && g.length > 0) {\n        goals = g;\n        goalTracker = GoalTracker(goals, sendGoalEvent);\n        watchLocation(locationWatcherInterval, refreshGoalTracker);\n      }\n      readyCallback();\n    })\n    .catch((err) => {\n      clientVars.emitter.maybeReportError(\n        new common.errors.LDUnexpectedResponseError('Error fetching goals: ' + (err && err.message) ? err.message : err)\n      );\n      readyCallback();\n    });\n\n  return ret;\n}\n", "import * as common from 'launchdarkly-js-sdk-common';\nimport * as importBasicLogger from './basicLogger';\nimport browserPlatform from './browserPlatform';\nimport GoalManager from './GoalManager';\n\nconst goalsEvent = 'goalsReady';\nconst extraOptionDefs = {\n  fetchGoals: { default: true },\n  hash: { type: 'string' },\n  eventProcessor: { type: 'object' }, // used only in tests\n  eventUrlTransformer: { type: 'function' },\n  disableSyncEventPost: { default: false },\n};\n\n// Pass our platform object to the common code to create the browser version of the client\nexport function initialize(env, user, options = {}) {\n  const platform = browserPlatform(options);\n  const clientVars = common.initialize(env, user, options, platform, extraOptionDefs);\n\n  const client = clientVars.client;\n  const validatedOptions = clientVars.options;\n  const emitter = clientVars.emitter;\n\n  const goalsPromise = new Promise((resolve) => {\n    const onGoals = emitter.on(goalsEvent, () => {\n      emitter.off(goalsEvent, onGoals);\n      resolve();\n    });\n  });\n  client.waitUntilGoalsReady = () => goalsPromise;\n\n  if (validatedOptions.fetchGoals) {\n    GoalManager(clientVars, () => emitter.emit(goalsEvent));\n    // Don't need to save a reference to the GoalManager - its constructor takes care of setting\n    // up the necessary event wiring\n  } else {\n    emitter.emit(goalsEvent);\n  }\n\n  if (document.readyState !== 'complete') {\n    window.addEventListener('load', clientVars.start);\n  } else {\n    clientVars.start();\n  }\n\n  const syncFlush = () => {\n    // Synchronous events are not available in all browsers, but where they\n    // are we should attempt to use them. This increases the chance of the events\n    // being delivered.\n    platform.synchronousFlush = true;\n    client.flush().catch(() => {});\n    platform.synchronousFlush = false;\n  };\n\n  // When the visibility of the page changes to hidden we want to flush any pending events.\n  //\n  // This is handled with visibility, instead of beforeunload/unload\n  // because those events are not compatible with the bfcache and are unlikely\n  // to be called in many situations. For more information see: https://developer.chrome.com/blog/page-lifecycle-api/\n  //\n  // Redundancy is included by using both the visibilitychange handler as well as\n  // pagehide, because different browsers, and versions have different bugs with each.\n  // This also may provide more opportunity for the events to get flushed.\n  //\n  const handleVisibilityChange = () => {\n    if (document.visibilityState === 'hidden') {\n      syncFlush();\n    }\n  };\n\n  document.addEventListener('visibilitychange', handleVisibilityChange);\n  window.addEventListener('pagehide', syncFlush);\n\n  return client;\n}\n\nexport const basicLogger = importBasicLogger.basicLogger;\n\nexport const createConsoleLogger = common.createConsoleLogger;\n\nexport const version = VERSION;\n\nfunction deprecatedInitialize(env, user, options = {}) {\n  console && console.warn && console.warn(common.messages.deprecated('default export', 'named LDClient export')); // eslint-disable-line no-console\n  return initialize(env, user, options);\n}\n\nexport default { initialize: deprecatedInitialize, version };\n", "import newHttpRequest from './httpRequest';\n\nexport default function makeBrowserPlatform(options) {\n  const ret = {\n    userAgentHeaderName: 'X-LaunchDarkly-User-Agent',\n  };\n\n  ret.synchronousFlush = false; // this will be set to true by index.js if the page is hidden\n\n  // XMLHttpRequest may not exist if we're running in a server-side rendering context\n  if (window.XMLHttpRequest) {\n    const disableSyncFlush = options && options.disableSyncEventPost;\n    ret.httpRequest = (method, url, headers, body) => {\n      const syncFlush = ret.synchronousFlush & !disableSyncFlush;\n      ret.synchronousFlush = false;\n      return newHttpRequest(method, url, headers, body, syncFlush);\n    };\n  }\n\n  let hasCors;\n  ret.httpAllowsPost = () => {\n    // We compute this lazily because calling XMLHttpRequest() at initialization time can disrupt tests\n    if (hasCors === undefined) {\n      hasCors = window.XMLHttpRequest ? 'withCredentials' in new window.XMLHttpRequest() : false;\n    }\n    return hasCors;\n  };\n\n  // Image-based mechanism for sending events if POST isn't available\n  ret.httpFallbackPing = (url) => {\n    const img = new window.Image();\n    img.src = url;\n  };\n\n  const eventUrlTransformer = options && options.eventUrlTransformer;\n  ret.getCurrentUrl = () => (eventUrlTransformer ? eventUrlTransformer(window.location.href) : window.location.href);\n\n  ret.isDoNotTrack = () => {\n    let flag;\n    if (window.navigator && window.navigator.doNotTrack !== undefined) {\n      flag = window.navigator.doNotTrack; // FF, Chrome\n    } else if (window.navigator && window.navigator.msDoNotTrack !== undefined) {\n      flag = window.navigator.msDoNotTrack; // IE 9/10\n    } else {\n      flag = window.doNotTrack; // IE 11+, Safari\n    }\n    return flag === 1 || flag === true || flag === '1' || flag === 'yes';\n  };\n\n  try {\n    if (window.localStorage) {\n      ret.localStorage = {\n        get: (key) =>\n          new Promise((resolve) => {\n            resolve(window.localStorage.getItem(key));\n          }),\n        set: (key, value) =>\n          new Promise((resolve) => {\n            window.localStorage.setItem(key, value);\n            resolve();\n          }),\n        clear: (key) =>\n          new Promise((resolve) => {\n            window.localStorage.removeItem(key);\n            resolve();\n          }),\n      };\n    }\n  } catch (e) {\n    // In some browsers (such as Chrome), even looking at window.localStorage at all will cause a\n    // security error if the feature is disabled.\n    ret.localStorage = null;\n  }\n\n  // The browser built-in EventSource implementations do not support setting the method used for\n  // the request. When useReport is true, we ensure sending the user in the body of a REPORT request\n  // rather than in the URL path. If a polyfill for EventSource that supports setting the request\n  // method is provided (currently, launchdarkly-eventsource is the only polyfill that both supports\n  // it and gives us a way to *know* that it supports it), we use the polyfill to connect to a flag\n  // stream that will provide evaluated flags for the specific user. Otherwise, when useReport is\n  // true, we fall back to a generic  'ping' stream that informs the SDK to make a separate REPORT\n  // request for the user's flag evaluations whenever the flag definitions have been updated.\n  let eventSourceConstructor;\n  const useReport = options && options.useReport;\n  if (\n    useReport &&\n    typeof window.EventSourcePolyfill === 'function' &&\n    window.EventSourcePolyfill.supportedOptions &&\n    window.EventSourcePolyfill.supportedOptions.method\n  ) {\n    ret.eventSourceAllowsReport = true;\n    eventSourceConstructor = window.EventSourcePolyfill;\n  } else {\n    ret.eventSourceAllowsReport = false;\n    eventSourceConstructor = window.EventSource;\n  }\n\n  // If EventSource does not exist, the absence of eventSourceFactory will make us not try to open streams\n  if (window.EventSource) {\n    const timeoutMillis = 300000; // this is only used by polyfills - see below\n\n    ret.eventSourceFactory = (url, options) => {\n      // The standard EventSource constructor doesn't take any options, just a URL. However, some\n      // EventSource polyfills allow us to specify a timeout interval, and in some cases they will\n      // default to a too-short timeout if we don't specify one. So, here, we are setting the\n      // timeout properties that are used by several popular polyfills.\n      // Also, the skipDefaultHeaders property (if supported) tells the polyfill not to add the\n      // Cache-Control header that can cause CORS problems in browsers.\n      // See: https://github.com/launchdarkly/js-eventsource\n      const defaultOptions = {\n        heartbeatTimeout: timeoutMillis,\n        silentTimeout: timeoutMillis,\n        skipDefaultHeaders: true,\n      };\n\n      const esOptions = { ...defaultOptions, ...options };\n\n      return new eventSourceConstructor(url, esOptions);\n    };\n\n    ret.eventSourceIsActive = (es) =>\n      es.readyState === window.EventSource.OPEN || es.readyState === window.EventSource.CONNECTING;\n  }\n\n  ret.userAgent = 'JSClient';\n  ret.version = VERSION;\n\n  ret.diagnosticSdkData = {\n    name: 'js-client-sdk',\n    version: VERSION,\n  };\n\n  ret.diagnosticPlatformData = {\n    name: 'JS',\n  };\n\n  ret.diagnosticUseCombinedEvent = true; // the browser SDK uses the \"diagnostic-combined\" event format\n\n  return ret;\n}\n"], "names": ["createCustomError", "name", "CustomError", "message", "code", "Error", "captureStackTrace", "this", "constructor", "prototype", "LDUnexpectedResponseError", "LDInvalidEnvironmentIdError", "LDInvalidUserError", "LDInvalidEventKeyError", "LDInvalidArgumentError", "LDFlagFetchError", "errors", "LDInvalidDataError", "LDTimeoutError", "isHttpErrorRecoverable", "status", "byteLength_1", "b64", "lens", "getLens", "validLen", "placeHoldersLen", "toByteArray_1", "tmp", "i", "arr", "Arr", "_byteLength", "curByte", "len", "revLookup", "charCodeAt", "fromByteArray_1", "uint8", "length", "extraBytes", "parts", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "len2", "push", "encodeChunk", "lookup", "join", "Uint8Array", "Array", "indexOf", "start", "end", "num", "output", "isArray", "keyList", "Object", "keys", "hasProp", "hasOwnProperty", "fastDeepEqual", "equal", "a", "b", "key", "arrA", "arrB", "dateA", "Date", "dateB", "getTime", "regexpA", "RegExp", "regexpB", "toString", "call", "userAttrsToStringify", "btoa", "s", "escaped", "unescape", "encodeURIComponent", "base64", "fromByteArray", "stringToBytes", "objectHasOwnProperty", "object", "getRandomValues", "utils", "appendUrlPath", "baseUrl", "path", "endsWith", "substring", "startsWith", "base64URLEncode", "replace", "clone", "obj", "JSON", "parse", "stringify", "deepEquals", "extend", "objects", "reduce", "acc", "getLDUserAgentString", "platform", "version", "userAgent", "onNextTick", "cb", "setTimeout", "sanitizeContext", "context", "newContext", "kind", "undefined", "for<PERSON>ach", "attr", "value", "String", "transformValuesToVersionedValues", "flags", "ret", "transformVersionedValuesToValues", "flagsState", "wrapPromiseCallback", "promise", "callback", "then", "error", "Promise", "reject", "once", "func", "result", "called", "args", "apply", "rnds8", "rng", "crypto", "bind", "msCrypto", "REGEX", "validate", "uuid", "test", "_nodeId", "_clockseq", "byteToHex", "substr", "offset", "arguments", "toLowerCase", "TypeError", "_lastMSecs", "_lastNSecs", "v", "parseInt", "slice", "v35", "hashfunc", "generateUUID", "namespace", "buf", "str", "bytes", "set", "err", "DNS", "URL", "getOutputLength", "inputLength8", "safeAdd", "x", "y", "lsw", "md5cmn", "q", "t", "cnt", "md5ff", "c", "d", "md5gg", "md5hh", "md5ii", "v3", "msg", "input", "length32", "hexTab", "hex", "char<PERSON>t", "md5ToHexEncodedArray", "olda", "oldb", "oldc", "oldd", "wordsToMd5", "length8", "Uint32Array", "bytesToWords", "v3$1", "f", "z", "ROTL", "n", "v5", "K", "H", "l", "N", "Math", "ceil", "M", "_i", "j", "pow", "floor", "_i2", "W", "_t", "e", "_t2", "T", "v5$1", "options", "node", "clockseq", "seedBytes", "random", "msecs", "now", "nsecs", "dt", "tl", "tmh", "rnds", "logLevels", "loggers", "commonBasicLogger", "formatFn", "destination", "toConsole", "methodName", "line", "console", "destinations", "prependLevelToMessage", "prefix", "minLevel", "level", "write", "levelIndex", "levelName", "fullPrefix", "tempArgs", "log", "logger", "validate<PERSON><PERSON>ger", "errorString", "docLink", "messages", "bootstrapInvalid", "bootstrapOldFormat", "clientInitialized", "clientNotReady", "debugEnqueueingEvent", "debugPostingDiagnosticEvent", "event", "debugPostingEvents", "count", "debugStreamDelete", "debugStreamDeleteIgnored", "debugStreamPatch", "debugStreamPatchIgnored", "debugStreamPing", "debugPolling", "url", "debugStreamPut", "deprecated", "old<PERSON>ame", "newName", "environmentNotFound", "environmentNotSpecified", "errorFetchingFlags", "eventCapacityExceeded", "eventWithoutContext", "httpErrorMessage", "retryMessage", "httpUnavailable", "identifyDisabled", "inspector<PERSON><PERSON>od<PERSON><PERSON>r", "type", "invalidContentType", "contentType", "invalidData", "invalidInspector", "<PERSON><PERSON><PERSON>", "invalidMetricV<PERSON>ue", "badType", "invalidContext", "invalidTagV<PERSON>ue", "localStorageUnavailable", "networkError", "optionBelowMinimum", "minimum", "streamClosing", "streamConnecting", "streamError", "streamReconnectDelay", "tagValueTooLong", "unknownCustomEventKey", "unknownOption", "contextNotSpecified", "unrecoverableStreamError", "wrongOptionType", "expectedType", "actualType", "wrongOptionTypeBoolean", "require$$0", "baseOptionDefs", "default", "streamUrl", "eventsUrl", "sendEvents", "streaming", "sendLDHeaders", "requestHeaderTransform", "sendEventsOnlyForVariation", "useReport", "evaluationReasons", "eventCapacity", "flushInterval", "samplingInterval", "allAttributesPrivate", "privateAttributes", "bootstrap", "diagnosticRecordingInterval", "diagnosticOptOut", "wrapperName", "wrapperVersion", "stateProvider", "application", "validator", "validated", "id", "validateTagValue", "inspectors", "hooks", "plugins", "allowedTagCharacters", "canonicalizeUrl", "tagValue", "match", "warn", "configuration", "emitter", "extraOptionDefs", "optionDefs", "deprecatedOptions", "reportArgumentError", "maybeReportError", "config", "opts", "checkDeprecatedOptions", "applyDefaults", "typeDescForValue", "optionDef", "allowedTypes", "split", "validateTypesAndNames", "getTags", "tags", "headers", "getLDHeaders", "h", "userAgentHeaderName", "tagKeys", "sort", "map", "flattened", "item", "concat", "transformHeaders", "v1", "uuidv1", "require$$1", "EventSender_1", "environmentId", "baseHeaders", "sender", "events", "isDiagnostic", "httpRequest", "resolve", "jsonBody", "payloadId", "doPostRequest", "canRetry", "dateStr", "header", "time", "serverTime", "getResponseInfo", "catch", "canonicalize_1", "canonicalize", "visited", "includes", "filter", "validKind", "encodeKey", "checkContext", "allowLegacyKey", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "kinds", "every", "<PERSON><PERSON>ey", "getContextKeys", "user", "entries", "getContextKinds", "getCanonicalKey", "EventSummarizer_1", "es", "startDate", "endDate", "counters", "contextKinds", "summarizeEvent", "counterKey", "variation", "counterVal", "Set", "contextKeys", "get<PERSON><PERSON>s", "add", "creationDate", "getSummary", "flagsOut", "empty", "values", "flag", "counterOut", "unknown", "features", "clearSummary", "MultiEventSummarizer_1", "contextFilter", "summarizers", "contexts", "summarizer", "EventSummarizer", "getSummaries", "summarizersToFlush", "contextsForSummaries", "summary", "processEscapeCharacters", "getComponents", "reference", "component", "isLiteral", "compare", "aIsLiteral", "bIsLiteral", "bComponents", "aComponents", "literalToReference", "literal", "attributeReference", "cloneExcluding", "target", "references", "stack", "cloned", "excluded", "ptr", "source", "parent", "pop", "some", "ContextFilter_1", "protectedAttributes", "legacyTopLevelCopyAttributes", "filterSingleKind", "redactAnonymous", "AttributeReference", "anonymous", "_meta", "protectedAttr", "getAttributesToFilter", "redactedAttributes", "filtered", "custom", "privateAttributeNames", "legacyToSingleKind", "filteredContext", "filterMultiKind", "EventProcessor_1", "diagnosticsAccumulator", "processor", "eventSender", "EventSender", "mainEventsUrl", "ContextFilter", "MultiEventSummarizer", "flushTimer", "queue", "lastKnownPastTime", "disabled", "exceededCapacity", "shouldSampleEvent", "makeOutputEvent", "addToOutbox", "incrementDroppedEvents", "enqueue", "addFullEvent", "addDebugEvent", "trackEvents", "debugEventsUntilDate", "debugEvent", "flush", "async", "eventsToSend", "setEventsInLastBatch", "debug", "responseInfo", "flushTick", "stop", "clearTimeout", "EventEmitter_1", "on", "handler", "off", "emit", "copiedHandlers", "getEvents", "getEventListenerCount", "readyEvent", "successEvent", "failureEvent", "InitializationState", "eventEmitter", "succeeded", "failed", "failureValue", "initializationPromise", "readyPromise", "onReady", "getInitializationPromise", "onSuccess", "onFailure", "getReadyPromise", "signalSuccess", "signalFailure", "PersistentFlagStore_1", "storage", "environment", "hash", "ident", "store", "getFlagsKey", "getContext", "loadFlags", "get", "dataStr", "data", "schema", "$schema", "ex", "clearFlags", "saveFlags", "clear", "PersistentStorage_1", "localStorageProvider", "loggedError", "logError", "isEnabled", "require$$2", "Stream_1", "stream", "evalUrlPrefix", "withReasons", "baseReconnectDelay", "connectionAttemptStartTime", "firstConnectionErrorLogged", "reconnectTimeoutReference", "handlers", "retryCount", "getNextRetryDelay", "delay", "computed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backoff", "trunc", "handleError", "closeConnection", "logConnectionResult", "tryConnect", "openConnection", "query", "readTimeoutMillis", "eventSourceFactory", "eventSourceAllowsReport", "method", "body", "info", "addEventListener", "onerror", "onopen", "close", "success", "recordStreamInit", "connect", "newHash", "newHandlers", "disconnect", "isConnected", "eventSourceIsActive", "promiseCoalescer_1", "finallyFn", "currentPromise", "currentCancelFn", "finalResolve", "finalReject", "coalescer", "p", "cancelFn", "resultPromise", "jsonContentType", "Requestor_1", "requestor", "activeRequests", "fetchJSON", "endpoint", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "req", "statusText", "getResponseError", "addPromise", "cancel", "fetchFlagSettings", "Identity_1", "initialContext", "onChange", "setContext", "AnonymousContextProcessor_1", "persistentStorage", "getContextKeyIdString", "processSingleKindContext", "getCachedContextKey", "cachedId", "setCachedContextKey", "processContext", "processedContext", "all", "diagnosticEvents", "DiagnosticId", "sdkKey", "diagnosticId", "sdkKeySuffix", "DiagnosticsAccumulator", "startTime", "dataSinceDate", "droppedEvents", "eventsInLastBatch", "streamInits", "reset", "getProps", "setProps", "props", "timestamp", "<PERSON><PERSON><PERSON><PERSON>", "DiagnosticsManager", "accumulator", "combinedMode", "diagnosticUseCombinedEvent", "localStorageKey", "diagnosticEventsUrl", "periodicInterval", "eventSentTime", "periodicTimer", "streamingEnabled", "manager", "makeInitProperties", "sdk", "makeSdkData", "makeConfigData", "diagnosticPlatformData", "sendDiagnosticEvent", "sendPeriodicEvent", "currentTime", "createPeriodicEventAndReset", "saveProperties", "sdkData", "diagnosticSdkData", "customBaseURI", "customStreamURI", "customEventsURI", "eventsCapacity", "eventsFlushIntervalMillis", "reconnectTimeMillis", "streamingDisabled", "diagnosticRecordingIntervalMillis", "usingSecureMode", "bootstrapMode", "fetchGoalsDisabled", "fetchGoals", "loadProperties", "localStorageAvailable", "nextEventTime", "timeNow", "setStreaming", "enabled", "SafeInspector_1", "inspector", "errorLogged", "wrapper", "synchronous", "InspectorTypes", "flagUsed", "flagDetailsChanged", "flagDetail<PERSON><PERSON>ed", "clientIdentityChanged", "freeze", "InspectorManager_1", "Inspector<PERSON><PERSON><PERSON>", "inspectorsByType", "synchronousInspectorsByType", "safeInspectors", "SafeInspector", "safeInspector", "hasListeners", "onFlagUsed", "<PERSON><PERSON><PERSON>", "detail", "onFlags", "onFlagChanged", "onIdentityChanged", "timedPromise_1", "taskName", "_res", "UNKNOWN_HOOK_NAME", "tryExecuteStage", "<PERSON><PERSON><PERSON>", "stage", "def", "getHookName", "hook", "getMetadata", "<PERSON><PERSON><PERSON><PERSON>", "initialHooks", "hooksInternal", "withEvaluation", "defaultValue", "hookContext", "hookData", "beforeEvaluation", "executeBeforeEvaluation", "updatedData", "hookIndex", "afterEvaluation", "executeAfterEvaluation", "identify", "timeout", "beforeIdentify", "executeBeforeIdentify", "afterIdentify", "executeAfterIdentify", "addHook", "afterTrack", "executeAfterTrack", "UNKNOWN_PLUGIN_NAME", "getPluginName", "plugin", "getPluginHooks", "environmentMetadata", "pluginHooks", "getHooks", "registerPlugins", "client", "register", "createPluginEnvironment", "env", "pluginSdkMetadata", "pluginApplicationMetadata", "pluginEnvironment", "clientSideId", "require$$3", "changeEvent", "internalChangeEvent", "src", "initialize", "specifiedOptions", "createLogger", "EventEmitter", "initializationStateTracker", "InitializationStateTracker", "inspector<PERSON><PERSON><PERSON>", "hook<PERSON>unner", "create<PERSON>ook<PERSON><PERSON>ner", "PersistentStorage", "localStorage", "diagnosticsEnabled", "diagnostics", "diagnosticsManager", "Stream", "eventProcessor", "EventProcessor", "Requestor", "useLocalStorage", "streamActive", "subscribedToChangeEvents", "streamForcedState", "inited", "closed", "firstEvent", "Identity", "enqueueEvent", "sendIdentifyEvent", "anonymousContextProcessor", "AnonymousContextProcessor", "persistentFlagStore", "PersistentFlagStore", "isDoNotTrack", "notifyInspectionFlagChanged", "newFlag", "getFlagDetail", "notifyInspectionFlagsChanged", "cur", "sendFlagEvent", "includeReason", "variationIndex", "flagVersion", "trackReason", "reason", "verifyContext", "variationDetailInternal", "sendEvent", "includeReasonInEvent", "isAllFlags", "notifyInspection", "deleted", "errorKind", "prerequisites", "notifyInspectionFlagUsed", "connectStream", "tryParseData", "jsonData", "ping", "contextAtTimeOfPingEvent", "requestedFlags", "replaceAllFlags", "put", "patch", "oldFlag", "mods", "newDetail", "previous", "current", "handleFlagChanges", "delete", "disconnectStream", "newFlags", "changes", "changeEventParams", "updateStreamingState", "shouldBeStreaming", "isChangeEventKey", "toUpperCase", "metadataKey", "<PERSON><PERSON><PERSON>", "metadata", "readFlagsFromBootstrap", "state", "getInitialState", "initFromStateProvider", "validatedContext", "signalSuccessfulInit", "storedFlags", "signalFailedInit", "finishInit", "waitForInitialization", "initPromise", "timeoutPromise", "timedPromise", "race", "waitForInitializationWithTimeout", "waitUntilReady", "onDone", "<PERSON><PERSON><PERSON><PERSON>", "flagValueMap", "variationDetail", "track", "metricValue", "customEventFilter", "getCurrentUrl", "contextKind", "haveListeners", "newState", "allFlags", "results", "finishClose", "getFlagsInternal", "getEnvironmentId", "internalChangeEventName", "_objectSpread", "emptyResult", "newHttpRequest", "pageIsClosing", "window", "navigator", "chromeMatch", "isSyncXhrSupported", "xhr", "XMLHttpRequest", "open", "setRequestHeader", "send", "cancelled", "getResponseHeader", "responseText", "abort", "escapeStringRegexp", "string", "doesUrlMatch", "matcher", "href", "search", "regex", "testUrl", "canonicalUrl", "pattern", "GoalTracker", "goals", "onEvent", "tracker", "listenerFn", "clickGoals", "goal", "urls", "location", "matches", "selector", "elements", "document", "querySelectorAll", "parentNode", "findGoalsForClick", "dispose", "removeEventListener", "GoalManager", "clientVars", "readyCallback", "goalTracker", "refreshGoalTracker", "sendGoalEvent", "g", "interval", "currentUrl", "previousUrl", "checkUrl", "poll", "fn", "history", "pushState", "watchLocation", "common", "goalsEvent", "eventUrlTransformer", "disableSyncEventPost", "hasCors", "disableSyncFlush", "syncFlush", "synchronousFlush", "httpAllowsPost", "httpFallbackPing", "Image", "eventSourceConstructor", "doNotTrack", "msDoNotTrack", "getItem", "setItem", "removeItem", "EventSourcePolyfill", "supportedOptions", "EventSource", "timeout<PERSON><PERSON><PERSON>", "esOptions", "defaultOptions", "heartbeatTimeout", "silentTimeout", "skip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "readyState", "OPEN", "CONNECTING", "browserPlatform", "validatedOptions", "goalsPromise", "onGoals", "waitUntilGoalsReady", "visibilityState", "basicLogger", "importBasicLogger", "index"], "mappings": "gPAAA,SAASA,EAAkBC,GACzB,SAASC,EAAYC,EAASC,GAC5BC,MAAMC,mBAAqBD,MAAMC,kBAAkBC,KAAMA,KAAKC,aAC9DD,KAAKJ,QAAUA,EACfI,KAAKH,KAAOA,CACb,CAMD,OAJAF,EAAYO,UAAY,IAAIJ,MAC5BH,EAAYO,UAAUR,KAAOA,EAC7BC,EAAYO,UAAUD,YAAcN,EAE7BA,CACT,CAEA,MAAMQ,EAA4BV,EAAkB,uCAC9CW,EAA8BX,EAAkB,yCAChDY,EAAqBZ,EAAkB,gCACvCa,EAAyBb,EAAkB,oCAC3Cc,EAAyBd,EAAkB,oCAC3Ce,EAAmBf,EAAkB,8BCR3C,IDmBA,IAAAgB,EAAiB,CACfN,4BACAC,8BACAC,qBACAC,yBACAC,yBACAG,mBAhByBjB,EAAkB,gCAiB3Ce,mBACFG,eAjBuBlB,EAAkB,4BAkBzCmB,uBAhBA,SAAgCC,GAC9B,QAAIA,GAAU,KAAOA,EAAS,OACV,MAAXA,GAA6B,MAAXA,GAA6B,MAAXA,EAG/C,GC1BAC,EAuCA,SAAqBC,GACnB,IAAIC,EAAOC,EAAQF,GACfG,EAAWF,EAAK,GAChBG,EAAkBH,EAAK,GAC3B,OAAuC,GAA9BE,EAAWC,GAAuB,EAAKA,CAClD,EA3CAC,EAiDA,SAAsBL,GACpB,IAAIM,EAcAC,EAbAN,EAAOC,EAAQF,GACfG,EAAWF,EAAK,GAChBG,EAAkBH,EAAK,GAEvBO,EAAM,IAAIC,EAVhB,SAAsBT,EAAKG,EAAUC,GACnC,OAAuC,GAA9BD,EAAWC,GAAuB,EAAKA,CAClD,CAQoBM,CAAYV,EAAKG,EAAUC,IAEzCO,EAAU,EAGVC,EAAMR,EAAkB,EACxBD,EAAW,EACXA,EAGJ,IAAKI,EAAI,EAAGA,EAAIK,EAAKL,GAAK,EACxBD,EACGO,EAAUb,EAAIc,WAAWP,KAAO,GAChCM,EAAUb,EAAIc,WAAWP,EAAI,KAAO,GACpCM,EAAUb,EAAIc,WAAWP,EAAI,KAAO,EACrCM,EAAUb,EAAIc,WAAWP,EAAI,IAC/BC,EAAIG,KAAcL,GAAO,GAAM,IAC/BE,EAAIG,KAAcL,GAAO,EAAK,IAC9BE,EAAIG,KAAmB,IAANL,EAGK,IAApBF,IACFE,EACGO,EAAUb,EAAIc,WAAWP,KAAO,EAChCM,EAAUb,EAAIc,WAAWP,EAAI,KAAO,EACvCC,EAAIG,KAAmB,IAANL,GAGK,IAApBF,IACFE,EACGO,EAAUb,EAAIc,WAAWP,KAAO,GAChCM,EAAUb,EAAIc,WAAWP,EAAI,KAAO,EACpCM,EAAUb,EAAIc,WAAWP,EAAI,KAAO,EACvCC,EAAIG,KAAcL,GAAO,EAAK,IAC9BE,EAAIG,KAAmB,IAANL,GAGnB,OAAOE,CACT,EA5FAO,EAkHA,SAAwBC,GAQtB,IAPA,IAAIV,EACAM,EAAMI,EAAMC,OACZC,EAAaN,EAAM,EACnBO,EAAQ,GACRC,EAAiB,MAGZb,EAAI,EAAGc,EAAOT,EAAMM,EAAYX,EAAIc,EAAMd,GAAKa,EACtDD,EAAMG,KAAKC,EAAYP,EAAOT,EAAIA,EAAIa,EAAkBC,EAAOA,EAAQd,EAAIa,IAI1D,IAAfF,GACFZ,EAAMU,EAAMJ,EAAM,GAClBO,EAAMG,KACJE,EAAOlB,GAAO,GACdkB,EAAQlB,GAAO,EAAK,IACpB,OAEsB,IAAfY,IACTZ,GAAOU,EAAMJ,EAAM,IAAM,GAAKI,EAAMJ,EAAM,GAC1CO,EAAMG,KACJE,EAAOlB,GAAO,IACdkB,EAAQlB,GAAO,EAAK,IACpBkB,EAAQlB,GAAO,EAAK,IACpB,MAIJ,OAAOa,EAAMM,KAAK,GACpB,EA/IID,EAAS,GACTX,EAAY,GACZJ,EAA4B,oBAAfiB,WAA6BA,WAAaC,MAEvD7C,EAAO,mEACFyB,EAAI,EAAsBA,EAAbzB,KAAwByB,EAC5CiB,EAAOjB,GAAKzB,EAAKyB,GACjBM,EAAU/B,EAAKgC,WAAWP,IAAMA,EAQlC,SAASL,EAASF,GAChB,IAAIY,EAAMZ,EAAIiB,OAEd,GAAIL,EAAM,EAAI,EACZ,MAAM,IAAI7B,MAAM,kDAKlB,IAAIoB,EAAWH,EAAI4B,QAAQ,KAO3B,OANkB,IAAdzB,IAAiBA,EAAWS,GAMzB,CAACT,EAJcA,IAAaS,EAC/B,EACA,EAAKT,EAAW,EAGtB,CAmEA,SAASoB,EAAaP,EAAOa,EAAOC,GAGlC,IAFA,IAAIxB,EARoByB,EASpBC,EAAS,GACJzB,EAAIsB,EAAOtB,EAAIuB,EAAKvB,GAAK,EAChCD,GACIU,EAAMT,IAAM,GAAM,WAClBS,EAAMT,EAAI,IAAM,EAAK,QACP,IAAfS,EAAMT,EAAI,IACbyB,EAAOV,KAdFE,GADiBO,EAeMzB,IAdT,GAAK,IACxBkB,EAAOO,GAAO,GAAK,IACnBP,EAAOO,GAAO,EAAI,IAClBP,EAAa,GAANO,IAaT,OAAOC,EAAOP,KAAK,GACrB,CAlGAZ,EAAU,IAAIC,WAAW,IAAM,GAC/BD,EAAU,IAAIC,WAAW,IAAM,sDCjB3BmB,EAAUN,MAAMM,QAChBC,EAAUC,OAAOC,KACjBC,EAAUF,OAAOhD,UAAUmD,eAE/BC,EAAiB,SAASC,EAAMC,EAAGC,GACjC,GAAID,IAAMC,EAAG,OAAO,EAEpB,GAAID,GAAKC,GAAiB,iBAALD,GAA6B,iBAALC,EAAe,CAC1D,IAEInC,EACAU,EACA0B,EAJAC,EAAOX,EAAQQ,GACfI,EAAOZ,EAAQS,GAKnB,GAAIE,GAAQC,EAAM,CAEhB,IADA5B,EAASwB,EAAExB,SACGyB,EAAEzB,OAAQ,OAAO,EAC/B,IAAKV,EAAIU,EAAgB,IAARV,KACf,IAAKiC,EAAMC,EAAElC,GAAImC,EAAEnC,IAAK,OAAO,EACjC,OAAO,CACR,CAED,GAAIqC,GAAQC,EAAM,OAAO,EAEzB,IAAIC,EAAQL,aAAaM,KACrBC,EAAQN,aAAaK,KACzB,GAAID,GAASE,EAAO,OAAO,EAC3B,GAAIF,GAASE,EAAO,OAAOP,EAAEQ,WAAaP,EAAEO,UAE5C,IAAIC,EAAUT,aAAaU,OACvBC,EAAUV,aAAaS,OAC3B,GAAID,GAAWE,EAAS,OAAO,EAC/B,GAAIF,GAAWE,EAAS,OAAOX,EAAEY,YAAcX,EAAEW,WAEjD,IAAIjB,EAAOF,EAAQO,GAGnB,IAFAxB,EAASmB,EAAKnB,UAECiB,EAAQQ,GAAGzB,OACxB,OAAO,EAET,IAAKV,EAAIU,EAAgB,IAARV,KACf,IAAK8B,EAAQiB,KAAKZ,EAAGN,EAAK7B,IAAK,OAAO,EAExC,IAAKA,EAAIU,EAAgB,IAARV,KAEf,IAAKiC,EAAMC,EADXE,EAAMP,EAAK7B,IACQmC,EAAEC,IAAO,OAAO,EAGrC,OAAO,CACR,CAED,OAAOF,GAAIA,GAAKC,GAAIA,CACtB,ECnDA,MAAMa,EAAuB,CAAC,MAAO,KAAM,UAAW,QAAS,YAAa,WAAY,SAAU,QAUlG,SAASC,EAAKC,GACZ,MAAMC,EAAUC,SAASC,mBAAmBH,IAC5C,OAAOI,EAAOC,cAGhB,SAAuBL,GACrB,MAAMf,EAAI,GACV,IAAK,IAAInC,EAAI,EAAGA,EAAIkD,EAAExC,OAAQV,IAC5BmC,EAAEpB,KAAKmC,EAAE3C,WAAWP,IAEtB,OAAOmC,CACT,CAT8BqB,CAAcL,GAC5C,CA2GA,SAASM,EAAqBC,EAAQtF,GACpC,OAAOwD,OAAOhD,UAAUmD,eAAegB,KAAKW,EAAQtF,EACtD,CAyCA,ICnKIuF,EDmKJC,EAAiB,CACjBC,cAlKA,SAAuBC,EAASC,GAI9B,OADoBD,EAAQE,SAAS,KAAOF,EAAQG,UAAU,EAAGH,EAAQpD,OAAS,GAAKoD,IACjEC,EAAKG,WAAW,KAAO,GAAK,KAAOH,CAC3D,EA8JAI,gBA9IA,SAAyBjB,GACvB,OACED,EAAKC,GAEFkB,QAAQ,KAAM,IACdA,QAAQ,MAAO,KACfA,QAAQ,MAAO,IAEtB,EAuIEnB,OACAoB,MAtIF,SAAeC,GACb,OAAOC,KAAKC,MAAMD,KAAKE,UAAUH,GACnC,EAqIEI,WAnIF,SAAoBxC,EAAGC,GACrB,OAAOH,EAAcE,EAAGC,EAC1B,EAkIEwC,OArDF,YAAmBC,GACjB,OAAOA,EAAQC,QAAO,CAACC,EAAKR,KAAG,IAAWQ,KAAQR,KAAQ,CAAE,EAC9D,EAoDAS,qBA3DA,SAA8BC,GAC5B,MAAMC,EAAUD,EAASC,SAAW,IACpC,OAAOD,EAASE,UAAY,IAAMD,CACpC,EAyDAxB,qBAAEA,EACF0B,WAjIA,SAAoBC,GAClBC,WAAWD,EAAI,EACjB,EAgIEE,gBAjDF,SAAyBC,GACvB,IAAKA,EACH,OAAOA,EAET,IAAIC,EAYJ,OAVqB,OAAjBD,EAAQE,WAAkCC,IAAjBH,EAAQE,MACnCzC,EAAqB2C,SAAQC,IAC3B,MAAMC,EAAQN,EAAQK,QACRF,IAAVG,GAAwC,iBAAVA,IAChCL,EAAaA,GAAc,IAAKD,GAChCC,EAAWI,GAAQE,OAAOD,OAKzBL,GAAcD,CACvB,EAiCEQ,iCAtFF,SAA0CC,GACxC,MAAMC,EAAM,CAAA,EACZ,IAAK,MAAM7D,KAAO4D,EACZvC,EAAqBuC,EAAO5D,KAC9B6D,EAAI7D,GAAO,CAAEyD,MAAOG,EAAM5D,GAAM6C,QAAS,IAG7C,OAAOgB,CACT,EA+EEC,iCA1EF,SAA0CC,GACxC,MAAMF,EAAM,CAAA,EACZ,IAAK,MAAM7D,KAAO+D,EACZ1C,EAAqB0C,EAAY/D,KACnC6D,EAAI7D,GAAO+D,EAAW/D,GAAKyD,OAG/B,OAAOI,CACT,EAmEEG,oBApHF,SAA6BC,EAASC,GACpC,MAAML,EAAMI,EAAQE,MAClBV,IACMS,GACFjB,YAAW,KACTiB,EAAS,KAAMT,KACd,GAEEA,KAETW,IACE,IAAIF,EAKF,OAAOG,QAAQC,OAAOF,GAJtBnB,YAAW,KACTiB,EAASE,EAAO,QACf,MAOT,OAAQF,OAAiBZ,EAANO,CACrB,EA+FEU,KA1BF,SAAcC,GACZ,IACIC,EADAC,GAAS,EAEb,OAAO,YAAYC,GAKjB,OAJKD,IACHA,GAAS,EACTD,EAASD,EAAKI,MAAMtI,KAAMqI,IAErBF,CACX,CACA,GChKII,EAAQ,IAAI9F,WAAW,IACZ,SAAS+F,IAEtB,IAAKvD,KAGHA,EAAoC,oBAAXwD,QAA0BA,OAAOxD,iBAAmBwD,OAAOxD,gBAAgByD,KAAKD,SAA+B,oBAAbE,UAAgE,mBAA7BA,SAAS1D,iBAAkC0D,SAAS1D,gBAAgByD,KAAKC,WAGrO,MAAM,IAAI7I,MAAM,4GAIpB,OAAOmF,EAAgBsD,EACzB,CClBA,IAAAK,EAAe,sHCEf,SAASC,EAASC,GAChB,MAAuB,iBAATA,GAAqBF,EAAMG,KAAKD,EAChD,CCIA,IAFA,ICAIE,EAEAC,EDFAC,EAAY,GAEP5H,EAAI,EAAGA,EAAI,MAAOA,EACzB4H,EAAU7G,MAAMf,EAAI,KAAO8C,SAAS,IAAI+E,OAAO,IAGjD,SAASpD,EAAUxE,GACjB,IAAI6H,EAASC,UAAUrH,OAAS,QAAsBgF,IAAjBqC,UAAU,GAAmBA,UAAU,GAAK,EAG7EP,GAAQI,EAAU3H,EAAI6H,EAAS,IAAMF,EAAU3H,EAAI6H,EAAS,IAAMF,EAAU3H,EAAI6H,EAAS,IAAMF,EAAU3H,EAAI6H,EAAS,IAAM,IAAMF,EAAU3H,EAAI6H,EAAS,IAAMF,EAAU3H,EAAI6H,EAAS,IAAM,IAAMF,EAAU3H,EAAI6H,EAAS,IAAMF,EAAU3H,EAAI6H,EAAS,IAAM,IAAMF,EAAU3H,EAAI6H,EAAS,IAAMF,EAAU3H,EAAI6H,EAAS,IAAM,IAAMF,EAAU3H,EAAI6H,EAAS,KAAOF,EAAU3H,EAAI6H,EAAS,KAAOF,EAAU3H,EAAI6H,EAAS,KAAOF,EAAU3H,EAAI6H,EAAS,KAAOF,EAAU3H,EAAI6H,EAAS,KAAOF,EAAU3H,EAAI6H,EAAS,MAAME,cAMzf,IAAKT,EAASC,GACZ,MAAMS,UAAU,+BAGlB,OAAOT,CACT,CChBA,IAAIU,EAAa,EACbC,EAAa,ECVjB,SAAS3D,EAAMgD,GACb,IAAKD,EAASC,GACZ,MAAMS,UAAU,gBAGlB,IAAIG,EACAnI,EAAM,IAAIkB,WAAW,IAuBzB,OArBAlB,EAAI,IAAMmI,EAAIC,SAASb,EAAKc,MAAM,EAAG,GAAI,OAAS,GAClDrI,EAAI,GAAKmI,IAAM,GAAK,IACpBnI,EAAI,GAAKmI,IAAM,EAAI,IACnBnI,EAAI,GAAS,IAAJmI,EAETnI,EAAI,IAAMmI,EAAIC,SAASb,EAAKc,MAAM,EAAG,IAAK,OAAS,EACnDrI,EAAI,GAAS,IAAJmI,EAETnI,EAAI,IAAMmI,EAAIC,SAASb,EAAKc,MAAM,GAAI,IAAK,OAAS,EACpDrI,EAAI,GAAS,IAAJmI,EAETnI,EAAI,IAAMmI,EAAIC,SAASb,EAAKc,MAAM,GAAI,IAAK,OAAS,EACpDrI,EAAI,GAAS,IAAJmI,EAGTnI,EAAI,KAAOmI,EAAIC,SAASb,EAAKc,MAAM,GAAI,IAAK,KAAO,cAAgB,IACnErI,EAAI,IAAMmI,EAAI,WAAc,IAC5BnI,EAAI,IAAMmI,IAAM,GAAK,IACrBnI,EAAI,IAAMmI,IAAM,GAAK,IACrBnI,EAAI,IAAMmI,IAAM,EAAI,IACpBnI,EAAI,IAAU,IAAJmI,EACHnI,CACT,CCfe,SAAAsI,EAAUnK,EAAM6G,EAASuD,GACtC,SAASC,EAAa5C,EAAO6C,EAAWC,EAAKb,GAS3C,GARqB,iBAAVjC,IACTA,EAjBN,SAAuB+C,GACrBA,EAAMxF,SAASC,mBAAmBuF,IAIlC,IAFA,IAAIC,EAAQ,GAEH7I,EAAI,EAAGA,EAAI4I,EAAIlI,SAAUV,EAChC6I,EAAM9H,KAAK6H,EAAIrI,WAAWP,IAG5B,OAAO6I,CACT,CAOcrF,CAAcqC,IAGC,iBAAd6C,IACTA,EAAYlE,EAAMkE,IAGK,KAArBA,EAAUhI,OACZ,MAAMuH,UAAU,oEAMlB,IAAIY,EAAQ,IAAI1H,WAAW,GAAK0E,EAAMnF,QAOtC,GANAmI,EAAMC,IAAIJ,GACVG,EAAMC,IAAIjD,EAAO6C,EAAUhI,SAC3BmI,EAAQL,EAASK,IACX,GAAgB,GAAXA,EAAM,GAAY5D,EAC7B4D,EAAM,GAAgB,GAAXA,EAAM,GAAY,IAEzBF,EAAK,CACPb,EAASA,GAAU,EAEnB,IAAK,IAAI9H,EAAI,EAAGA,EAAI,KAAMA,EACxB2I,EAAIb,EAAS9H,GAAK6I,EAAM7I,GAG1B,OAAO2I,CACR,CAED,OAAOlE,EAAUoE,EAClB,CAGD,IACEJ,EAAarK,KAAOA,CACxB,CAAI,MAAO2K,GAAO,CAKhB,OAFAN,EAAaO,IA7CE,uCA8CfP,EAAaQ,IA7CE,uCA8CRR,CACT,CCPA,SAASS,EAAgBC,GACvB,OAAwC,IAAhCA,EAAe,KAAO,GAAK,GAAU,CAC/C,CAsHA,SAASC,EAAQC,EAAGC,GAClB,IAAIC,GAAW,MAAJF,IAAmB,MAAJC,GAE1B,OADWD,GAAK,KAAOC,GAAK,KAAOC,GAAO,KAC5B,GAAW,MAANA,CACrB,CAcA,SAASC,EAAOC,EAAGvH,EAAGC,EAAGkH,EAAGnG,EAAGwG,GAC7B,OAAON,GATc5H,EASQ4H,EAAQA,EAAQlH,EAAGuH,GAAIL,EAAQC,EAAGK,OATrCC,EAS0CzG,GARhD1B,IAAQ,GAAKmI,EAQuCxH,GAT1E,IAAuBX,EAAKmI,CAU5B,CAEA,SAASC,EAAM1H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAGnG,EAAGwG,GAC/B,OAAOF,EAAOrH,EAAI0H,GAAK1H,EAAI2H,EAAG5H,EAAGC,EAAGkH,EAAGnG,EAAGwG,EAC5C,CAEA,SAASK,EAAM7H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAGnG,EAAGwG,GAC/B,OAAOF,EAAOrH,EAAI2H,EAAID,GAAKC,EAAG5H,EAAGC,EAAGkH,EAAGnG,EAAGwG,EAC5C,CAEA,SAASM,EAAM9H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAGnG,EAAGwG,GAC/B,OAAOF,EAAOrH,EAAI0H,EAAIC,EAAG5H,EAAGC,EAAGkH,EAAGnG,EAAGwG,EACvC,CAEA,SAASO,EAAM/H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAGnG,EAAGwG,GAC/B,OAAOF,EAAOK,GAAK1H,GAAK2H,GAAI5H,EAAGC,EAAGkH,EAAGnG,EAAGwG,EAC1C,CClNA,IAAIQ,EAAK3B,EAAI,KAAM,IDkBnB,SAAaM,GACX,GAAqB,iBAAVA,EAAoB,CAC7B,IAAIsB,EAAM/G,SAASC,mBAAmBwF,IAEtCA,EAAQ,IAAI1H,WAAWgJ,EAAIzJ,QAE3B,IAAK,IAAIV,EAAI,EAAGA,EAAImK,EAAIzJ,SAAUV,EAChC6I,EAAM7I,GAAKmK,EAAI5J,WAAWP,EAE7B,CAED,OAOF,SAA8BoK,GAK5B,IAJA,IAAI3I,EAAS,GACT4I,EAA0B,GAAfD,EAAM1J,OACjB4J,EAAS,mBAEJtK,EAAI,EAAGA,EAAIqK,EAAUrK,GAAK,EAAG,CACpC,IAAIqJ,EAAIe,EAAMpK,GAAK,KAAOA,EAAI,GAAK,IAC/BuK,EAAMlC,SAASiC,EAAOE,OAAOnB,IAAM,EAAI,IAAQiB,EAAOE,OAAW,GAAJnB,GAAW,IAC5E5H,EAAOV,KAAKwJ,EACb,CAED,OAAO9I,CACT,CAnBSgJ,CAiCT,SAAoBpB,EAAGhJ,GAErBgJ,EAAEhJ,GAAO,IAAM,KAAQA,EAAM,GAC7BgJ,EAAEH,EAAgB7I,GAAO,GAAKA,EAM9B,IALA,IAAI6B,EAAI,WACJC,GAAK,UACL0H,GAAK,WACLC,EAAI,UAEC9J,EAAI,EAAGA,EAAIqJ,EAAE3I,OAAQV,GAAK,GAAI,CACrC,IAAI0K,EAAOxI,EACPyI,EAAOxI,EACPyI,EAAOf,EACPgB,EAAOf,EACX5H,EAAI0H,EAAM1H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,GAAI,GAAI,WAChC8J,EAAIF,EAAME,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,GAAI,IAAK,WACrC6J,EAAID,EAAMC,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,GAAI,GAAI,WACpCmC,EAAIyH,EAAMzH,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,GAAI,IAAK,YACrCkC,EAAI0H,EAAM1H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,GAAI,GAAI,WACpC8J,EAAIF,EAAME,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,GAAI,GAAI,YACpC6J,EAAID,EAAMC,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,GAAI,IAAK,YACrCmC,EAAIyH,EAAMzH,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,GAAI,IAAK,UACrCkC,EAAI0H,EAAM1H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,GAAI,EAAG,YACnC8J,EAAIF,EAAME,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,GAAI,IAAK,YACrC6J,EAAID,EAAMC,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,IAAK,IAAK,OACtCmC,EAAIyH,EAAMzH,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,IAAK,IAAK,YACtCkC,EAAI0H,EAAM1H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,IAAK,EAAG,YACpC8J,EAAIF,EAAME,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,IAAK,IAAK,UACtC6J,EAAID,EAAMC,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,IAAK,IAAK,YAEtCkC,EAAI6H,EAAM7H,EADVC,EAAIyH,EAAMzH,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,IAAK,GAAI,YACrB6J,EAAGC,EAAGT,EAAErJ,EAAI,GAAI,GAAI,WACpC8J,EAAIC,EAAMD,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,GAAI,GAAI,YACpC6J,EAAIE,EAAMF,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,IAAK,GAAI,WACrCmC,EAAI4H,EAAM5H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,GAAI,IAAK,WACjCkC,EAAI6H,EAAM7H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,GAAI,GAAI,WACpC8J,EAAIC,EAAMD,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,IAAK,EAAG,UACpC6J,EAAIE,EAAMF,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,IAAK,IAAK,WACtCmC,EAAI4H,EAAM5H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,GAAI,IAAK,WACrCkC,EAAI6H,EAAM7H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,GAAI,EAAG,WACnC8J,EAAIC,EAAMD,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,IAAK,GAAI,YACrC6J,EAAIE,EAAMF,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,GAAI,IAAK,WACrCmC,EAAI4H,EAAM5H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,GAAI,GAAI,YACpCkC,EAAI6H,EAAM7H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,IAAK,GAAI,YACrC8J,EAAIC,EAAMD,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,GAAI,GAAI,UACpC6J,EAAIE,EAAMF,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,GAAI,GAAI,YAEpCkC,EAAI8H,EAAM9H,EADVC,EAAI4H,EAAM5H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,IAAK,IAAK,YACtB6J,EAAGC,EAAGT,EAAErJ,EAAI,GAAI,GAAI,QACpC8J,EAAIE,EAAMF,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,GAAI,IAAK,YACrC6J,EAAIG,EAAMH,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,IAAK,GAAI,YACrCmC,EAAI6H,EAAM7H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,IAAK,IAAK,UACtCkC,EAAI8H,EAAM9H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,GAAI,GAAI,YACpC8J,EAAIE,EAAMF,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,GAAI,GAAI,YACpC6J,EAAIG,EAAMH,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,GAAI,IAAK,WACrCmC,EAAI6H,EAAM7H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,IAAK,IAAK,YACtCkC,EAAI8H,EAAM9H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,IAAK,EAAG,WACpC8J,EAAIE,EAAMF,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,GAAI,IAAK,WACjC6J,EAAIG,EAAMH,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,GAAI,IAAK,WACrCmC,EAAI6H,EAAM7H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,GAAI,GAAI,UACpCkC,EAAI8H,EAAM9H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,GAAI,GAAI,WACpC8J,EAAIE,EAAMF,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,IAAK,IAAK,WACtC6J,EAAIG,EAAMH,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,IAAK,GAAI,WAErCkC,EAAI+H,EAAM/H,EADVC,EAAI6H,EAAM7H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,GAAI,IAAK,WACrB6J,EAAGC,EAAGT,EAAErJ,GAAI,GAAI,WAChC8J,EAAIG,EAAMH,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,GAAI,GAAI,YACpC6J,EAAII,EAAMJ,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,IAAK,IAAK,YACtCmC,EAAI8H,EAAM9H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,GAAI,IAAK,UACrCkC,EAAI+H,EAAM/H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,IAAK,EAAG,YACpC8J,EAAIG,EAAMH,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,GAAI,IAAK,YACrC6J,EAAII,EAAMJ,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,IAAK,IAAK,SACtCmC,EAAI8H,EAAM9H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,GAAI,IAAK,YACrCkC,EAAI+H,EAAM/H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,GAAI,EAAG,YACnC8J,EAAIG,EAAMH,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,IAAK,IAAK,UACtC6J,EAAII,EAAMJ,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,GAAI,IAAK,YACrCmC,EAAI8H,EAAM9H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,IAAK,GAAI,YACrCkC,EAAI+H,EAAM/H,EAAGC,EAAG0H,EAAGC,EAAGT,EAAErJ,EAAI,GAAI,GAAI,WACpC8J,EAAIG,EAAMH,EAAG5H,EAAGC,EAAG0H,EAAGR,EAAErJ,EAAI,IAAK,IAAK,YACtC6J,EAAII,EAAMJ,EAAGC,EAAG5H,EAAGC,EAAGkH,EAAErJ,EAAI,GAAI,GAAI,WACpCmC,EAAI8H,EAAM9H,EAAG0H,EAAGC,EAAG5H,EAAGmH,EAAErJ,EAAI,GAAI,IAAK,WACrCkC,EAAIkH,EAAQlH,EAAGwI,GACfvI,EAAIiH,EAAQjH,EAAGwI,GACfd,EAAIT,EAAQS,EAAGe,GACfd,EAAIV,EAAQU,EAAGe,EAChB,CAED,MAAO,CAAC3I,EAAGC,EAAG0H,EAAGC,EACnB,CAtH8BgB,CA6H9B,SAAsBV,GACpB,GAAqB,IAAjBA,EAAM1J,OACR,MAAO,GAMT,IAHA,IAAIqK,EAAyB,EAAfX,EAAM1J,OAChBe,EAAS,IAAIuJ,YAAY9B,EAAgB6B,IAEpC/K,EAAI,EAAGA,EAAI+K,EAAS/K,GAAK,EAChCyB,EAAOzB,GAAK,KAAsB,IAAfoK,EAAMpK,EAAI,KAAcA,EAAI,GAGjD,OAAOyB,CACT,CA1IyCwJ,CAAapC,GAAuB,EAAfA,EAAMnI,QACpE,IC7BAwK,EAAehB,ECDf,SAASiB,EAAEjI,EAAGmG,EAAGC,EAAG8B,GAClB,OAAQlI,GACN,KAAK,EACH,OAAOmG,EAAIC,GAAKD,EAAI+B,EAEtB,KAAK,EAML,KAAK,EACH,OAAO/B,EAAIC,EAAI8B,EAJjB,KAAK,EACH,OAAO/B,EAAIC,EAAID,EAAI+B,EAAI9B,EAAI8B,EAKjC,CAEA,SAASC,EAAKhC,EAAGiC,GACf,OAAOjC,GAAKiC,EAAIjC,IAAM,GAAKiC,CAC7B,CClBA,IAAIC,EAAKhD,EAAI,KAAM,IDoBnB,SAAcM,GACZ,IAAI2C,EAAI,CAAC,WAAY,WAAY,WAAY,YACzCC,EAAI,CAAC,WAAY,WAAY,WAAY,UAAY,YAEzD,GAAqB,iBAAV5C,EAAoB,CAC7B,IAAIsB,EAAM/G,SAASC,mBAAmBwF,IAEtCA,EAAQ,GAER,IAAK,IAAI7I,EAAI,EAAGA,EAAImK,EAAIzJ,SAAUV,EAChC6I,EAAM9H,KAAKoJ,EAAI5J,WAAWP,GAE7B,MAAWoB,MAAMM,QAAQmH,KAExBA,EAAQzH,MAAMxC,UAAU0J,MAAMvF,KAAK8F,IAGrCA,EAAM9H,KAAK,KAKX,IAJA,IAAI2K,EAAI7C,EAAMnI,OAAS,EAAI,EACvBiL,EAAIC,KAAKC,KAAKH,EAAI,IAClBI,EAAI,IAAI1K,MAAMuK,GAETI,EAAK,EAAGA,EAAKJ,IAAKI,EAAI,CAG7B,IAFA,IAAI9L,EAAM,IAAI+K,YAAY,IAEjBgB,EAAI,EAAGA,EAAI,KAAMA,EACxB/L,EAAI+L,GAAKnD,EAAW,GAALkD,EAAc,EAAJC,IAAU,GAAKnD,EAAW,GAALkD,EAAc,EAAJC,EAAQ,IAAM,GAAKnD,EAAW,GAALkD,EAAc,EAAJC,EAAQ,IAAM,EAAInD,EAAW,GAALkD,EAAc,EAAJC,EAAQ,GAGvIF,EAAEC,GAAM9L,CACT,CAED6L,EAAEH,EAAI,GAAG,IAA2B,GAApB9C,EAAMnI,OAAS,GAASkL,KAAKK,IAAI,EAAG,IACpDH,EAAEH,EAAI,GAAG,IAAMC,KAAKM,MAAMJ,EAAEH,EAAI,GAAG,KACnCG,EAAEH,EAAI,GAAG,IAA2B,GAApB9C,EAAMnI,OAAS,GAAS,WAExC,IAAK,IAAIyL,EAAM,EAAGA,EAAMR,IAAKQ,EAAK,CAGhC,IAFA,IAAIC,EAAI,IAAIpB,YAAY,IAEftB,EAAI,EAAGA,EAAI,KAAMA,EACxB0C,EAAE1C,GAAKoC,EAAEK,GAAKzC,GAGhB,IAAK,IAAI2C,EAAK,GAAIA,EAAK,KAAMA,EAC3BD,EAAEC,GAAMhB,EAAKe,EAAEC,EAAK,GAAKD,EAAEC,EAAK,GAAKD,EAAEC,EAAK,IAAMD,EAAEC,EAAK,IAAK,GAShE,IANA,IAAInK,EAAIuJ,EAAE,GACNtJ,EAAIsJ,EAAE,GACN5B,EAAI4B,EAAE,GACN3B,EAAI2B,EAAE,GACNa,EAAIb,EAAE,GAEDc,EAAM,EAAGA,EAAM,KAAMA,EAAK,CACjC,IAAIrJ,EAAI0I,KAAKM,MAAMK,EAAM,IACrBC,EAAInB,EAAKnJ,EAAG,GAAKiJ,EAAEjI,EAAGf,EAAG0H,EAAGC,GAAKwC,EAAId,EAAEtI,GAAKkJ,EAAEG,KAAS,EAC3DD,EAAIxC,EACJA,EAAID,EACJA,EAAIwB,EAAKlJ,EAAG,MAAQ,EACpBA,EAAID,EACJA,EAAIsK,CACL,CAEDf,EAAE,GAAKA,EAAE,GAAKvJ,IAAM,EACpBuJ,EAAE,GAAKA,EAAE,GAAKtJ,IAAM,EACpBsJ,EAAE,GAAKA,EAAE,GAAK5B,IAAM,EACpB4B,EAAE,GAAKA,EAAE,GAAK3B,IAAM,EACpB2B,EAAE,GAAKA,EAAE,GAAKa,IAAM,CACrB,CAED,MAAO,CAACb,EAAE,IAAM,GAAK,IAAMA,EAAE,IAAM,GAAK,IAAMA,EAAE,IAAM,EAAI,IAAa,IAAPA,EAAE,GAAWA,EAAE,IAAM,GAAK,IAAMA,EAAE,IAAM,GAAK,IAAMA,EAAE,IAAM,EAAI,IAAa,IAAPA,EAAE,GAAWA,EAAE,IAAM,GAAK,IAAMA,EAAE,IAAM,GAAK,IAAMA,EAAE,IAAM,EAAI,IAAa,IAAPA,EAAE,GAAWA,EAAE,IAAM,GAAK,IAAMA,EAAE,IAAM,GAAK,IAAMA,EAAE,IAAM,EAAI,IAAa,IAAPA,EAAE,GAAWA,EAAE,IAAM,GAAK,IAAMA,EAAE,IAAM,GAAK,IAAMA,EAAE,IAAM,EAAI,IAAa,IAAPA,EAAE,GACxV,IC1FAgB,GAAelB,0CNWf,SAAYmB,EAAS/D,EAAKb,GACxB,IAAI9H,EAAI2I,GAAOb,GAAU,EACrB3F,EAAIwG,GAAO,IAAIvH,MAAM,IAErBuL,GADJD,EAAUA,GAAW,IACFC,MAAQjF,EACvBkF,OAAgClH,IAArBgH,EAAQE,SAAyBF,EAAQE,SAAWjF,EAInE,GAAY,MAARgF,GAA4B,MAAZC,EAAkB,CACpC,IAAIC,EAAYH,EAAQI,SAAWJ,EAAQxF,KAAOA,KAEtC,MAARyF,IAEFA,EAAOjF,EAAU,CAAgB,EAAfmF,EAAU,GAAWA,EAAU,GAAIA,EAAU,GAAIA,EAAU,GAAIA,EAAU,GAAIA,EAAU,KAG3F,MAAZD,IAEFA,EAAWjF,EAAiD,OAApCkF,EAAU,IAAM,EAAIA,EAAU,IAEzD,CAMD,IAAIE,OAA0BrH,IAAlBgH,EAAQK,MAAsBL,EAAQK,MAAQvK,KAAKwK,MAG3DC,OAA0BvH,IAAlBgH,EAAQO,MAAsBP,EAAQO,MAAQ9E,EAAa,EAEnE+E,EAAKH,EAAQ7E,GAAc+E,EAAQ9E,GAAc,IAarD,GAXI+E,EAAK,QAA0BxH,IAArBgH,EAAQE,WACpBA,EAAWA,EAAW,EAAI,QAKvBM,EAAK,GAAKH,EAAQ7E,SAAiCxC,IAAlBgH,EAAQO,QAC5CA,EAAQ,GAINA,GAAS,IACX,MAAM,IAAIzO,MAAM,mDAGlB0J,EAAa6E,EACb5E,EAAa8E,EACbtF,EAAYiF,EAIZ,IAAIO,GAA4B,KAAb,WAFnBJ,GAAS,cAE+BE,GAAS,WACjD9K,EAAEnC,KAAOmN,IAAO,GAAK,IACrBhL,EAAEnC,KAAOmN,IAAO,GAAK,IACrBhL,EAAEnC,KAAOmN,IAAO,EAAI,IACpBhL,EAAEnC,KAAY,IAALmN,EAET,IAAIC,EAAML,EAAQ,WAAc,IAAQ,UACxC5K,EAAEnC,KAAOoN,IAAQ,EAAI,IACrBjL,EAAEnC,KAAa,IAANoN,EAETjL,EAAEnC,KAAOoN,IAAQ,GAAK,GAAM,GAE5BjL,EAAEnC,KAAOoN,IAAQ,GAAK,IAEtBjL,EAAEnC,KAAO4M,IAAa,EAAI,IAE1BzK,EAAEnC,KAAkB,IAAX4M,EAET,IAAK,IAAItB,EAAI,EAAGA,EAAI,IAAKA,EACvBnJ,EAAEnC,EAAIsL,GAAKqB,EAAKrB,GAGlB,OAAO3C,GAAOlE,EAAUtC,EAC1B,UOzFA,SAAYuK,EAAS/D,EAAKb,GAExB,IAAIuF,GADJX,EAAUA,GAAW,IACFI,SAAWJ,EAAQxF,KAAOA,KAK7C,GAHAmG,EAAK,GAAe,GAAVA,EAAK,GAAY,GAC3BA,EAAK,GAAe,GAAVA,EAAK,GAAY,IAEvB1E,EAAK,CACPb,EAASA,GAAU,EAEnB,IAAK,IAAI9H,EAAI,EAAGA,EAAI,KAAMA,EACxB2I,EAAIb,EAAS9H,GAAKqN,EAAKrN,GAGzB,OAAO2I,CACR,CAED,OAAOlE,EAAU4I,EACnB,YCrBe,+CCEf,SAAiB7F,GACf,IAAKD,EAASC,GACZ,MAAMS,UAAU,gBAGlB,OAAOI,SAASb,EAAKK,OAAO,GAAI,GAAI,GACtC,mCCRA,MAAMyF,GAAY,CAAC,QAAS,OAAQ,OAAQ,QAAS,QAyFrD,IAAAC,GAAiB,CACjBC,kBArFA,SAA2Bd,EAASe,GAClC,GAAIf,GAAWA,EAAQgB,aAA8C,mBAAxBhB,EAAQgB,YACnD,MAAM,IAAIlP,MAAM,yDAGlB,SAASmP,EAAUC,GAGjB,OAAO,SAASC,GACVC,SAAWA,QAAQF,IACrBE,QAAQF,GAAY7K,KAAK+K,QAASD,EAE1C,CACG,CACD,MAAME,EACJrB,GAAWA,EAAQgB,YACf,CAAChB,EAAQgB,YAAahB,EAAQgB,YAAahB,EAAQgB,YAAahB,EAAQgB,aACxE,CAACC,EAAU,OAAQA,EAAU,QAASA,EAAU,QAASA,EAAU,UACnEK,KAA2BtB,IAAWA,EAAQgB,aAC9CO,EACHvB,QAA8BhH,IAAnBgH,EAAQuB,QAA2C,OAAnBvB,EAAQuB,OAAsCvB,EAAQuB,OAA5B,kBAExE,IAAIC,EAAW,EACf,GAAIxB,GAAWA,EAAQyB,MACrB,IAAK,IAAInO,EAAI,EAAGA,EAAIsN,GAAU5M,OAAQV,IAChCsN,GAAUtN,KAAO0M,EAAQyB,QAC3BD,EAAWlO,GAKjB,SAASoO,EAAMC,EAAYC,EAAWvH,GACpC,GAAIA,EAAKrG,OAAS,EAChB,OAEF,IAAImN,EACJ,MAAMU,EAAaP,EAAwBM,EAAY,KAAOL,EAASA,EACvE,GAAoB,IAAhBlH,EAAKrG,QAAiB+M,EAEnB,CACL,MAAMe,EAAW,IAAIzH,GACrByH,EAAS,GAAKD,EAAaC,EAAS,GACpCX,EAAOJ,KAAYe,EACpB,MALCX,EAAOU,EAAaxH,EAAK,GAM3B,IACEgH,EAAaM,GAAYR,EAC1B,CAAC,MAAO9E,GACP+E,SACEA,QAAQW,KACRX,QAAQW,IAAI,sCAAwCH,EAAY,+BAAiCvF,EACpG,CACF,CAED,MAAM2F,EAAS,CAAA,EACf,IAAK,IAAI1O,EAAI,EAAGA,EAAIsN,GAAU5M,OAAQV,IAAK,CACzC,MAAMsO,EAAYhB,GAAUtN,GAC5B,GAAkB,SAAdsO,EACF,GAAItO,EAAIkO,EACNQ,EAAOJ,GAAa,WACf,CACL,MAAMD,EAAarO,EACnB0O,EAAOJ,GAAa,WAElBF,EAAMC,EAAYC,EAAWvG,UACvC,CACO,CAEJ,CAED,OAAO2G,CACT,EAgBAC,eAdA,SAAwBD,GACtBpB,GAAU3H,SAAQwI,IAChB,GAAc,SAAVA,KAAsBO,EAAOP,IAAmC,mBAAlBO,EAAOP,IACvD,MAAM,IAAI3P,MAAM,gDAAkD2P,EAAQ,kBAOhF,GCrFA,SAASS,GAAY7F,GACnB,OAAIA,GAAOA,EAAIzK,QACNyK,EAAIzK,QAEM,iBAARyK,GAAoBA,aAAejD,OACrCiD,EAEFxE,KAAKE,UAAUsE,EACxB,CAEA,MAIM8F,GACJ,qIAkLF,IAAAC,GAAiB,CACfC,iBAlHuB,WACvB,MAAO,6FACT,EAiHEC,mBA3HyB,WACzB,MACE,gIAEAH,EAEJ,EAsHEI,kBA1LwB,WACxB,MAAO,iCACT,EAyLEC,eApLqB,WACrB,MAAO,kCACT,EAmLEC,qBA5B2B,SAAS1J,GACpC,MAAO,eAAiBA,EAAO,SACjC,EA2BE2J,4BArBkC,SAASC,GAC3C,MAAO,6BAA+BA,EAAM5J,KAAO,GACrD,EAoBE6J,mBA1ByB,SAASC,GAClC,MAAO,WAAaA,EAAQ,SAC9B,EAyBEC,kBAvCwB,SAASpN,GACjC,MAAO,yCAA2CA,EAAM,GAC1D,EAsCEqN,yBApC+B,SAASrN,GACxC,MAAO,yCAA2CA,EAAM,oCAC1D,EAmCEsN,iBAjDuB,SAAStN,GAChC,MAAO,uCAAyCA,EAAM,GACxD,EAgDEuN,wBA9C8B,SAASvN,GACvC,MAAO,uCAAyCA,EAAM,oCACxD,EA6CEwN,gBA3DsB,WACtB,MAAO,mCACT,EA0DEC,aAhEmB,SAASC,GAC5B,MAAO,gCAAkCA,CAC3C,EA+DEC,eAzDqB,WACrB,MAAO,yCACT,EAwDEC,WA5HiB,SAASC,EAASC,GACnC,OAAIA,EACK,IAAMD,EAAU,gCAAkCC,EAAU,IAE9D,IAAMD,EAAU,iBACzB,EAwHEE,oBAjK0B,WAC1B,MAAO,6FAA+FtB,EACxG,EAgKEuB,wBA9J8B,WAC9B,MAAO,+CAAiDvB,EAC1D,EA6JEwB,mBA3JyB,SAAStH,GAClC,MAAO,iCAAmC6F,GAAY7F,EACxD,EA0JEuH,sBA/L4B,WAC5B,MAAO,4EACT,EA8LEC,oBA5L0B,WAC1B,MAAO,uHACT,EA2LEC,iBA3HuB,SAASjR,EAAQgG,EAASkL,GACjD,MACE,kBACAlR,GACY,MAAXA,EAAiB,qBAAuB,IACzC,QACAgG,EACA,OACCpG,EAAOG,uBAAuBC,GAAUkR,EAAe,wBAE5D,EAkHEC,gBAhHsB,WACtB,MAAO,iDAAmD7B,EAC5D,EA+GE8B,iBA7GuB,WACvB,MAAO,8EACT,EA4GEC,qBAjC2B,CAACC,EAAMzS,IAAS,kBAAkBA,gBAAmByS,4BAkChFC,mBA7LyB,SAASC,GAClC,MAAO,mDAAqDA,EAAc,GAC5E,EA4LEC,YAvJkB,WAClB,MAAO,+EACT,EAsJEC,iBAtCuB,CAACJ,EAAMzS,IAAS,kBAAkBA,0BAA6ByS,oBAuCtFK,WA5LiB,WACjB,MAAO,4BACT,EA2LEC,mBAhCyBC,GACzB,mEAAmEA,gDAgCnEC,eA/JqB,WACrB,MAAO,6BAA+BxC,EACxC,EA8JEyC,gBAtCsBlT,GAAQ,kBAAkBA,oDAuChDmT,wBA5L8B,SAASxI,GACvC,MAAO,iCAAmC6F,GAAY7F,EACxD,EA2LEyI,aAzLmBlF,GAAK,iBAAmBA,EAAI,KAAOA,EAAI,IAAM,IA0LhEmF,mBAxFyB,CAACrT,EAAMyH,EAAO6L,IACvC,kBAAoBtT,EAAO,gBAAkByH,EAAQ,kCAAoC6L,EAwFzFC,cArHoB,WACpB,MAAO,2BACT,EAoHEC,iBAlHuB,SAAS9B,GAChC,MAAO,gCAAkCA,CAC3C,EAiHE+B,YA/GkB,SAAS9I,EAAK+I,GAChC,MACE,+BACAlD,GAAY7F,GACZ,kCACA+I,EACA,gBAEJ,EAwGEC,gBA3CsB3T,GAAQ,aAAaA,sDA4C3C4T,sBA5L4B,SAAS5P,GACrC,MAAO,iBAAmBA,EAAM,kBAClC,EA2LE6P,cAxGoB7T,GAAQ,mCAAqCA,EAAO,IAyGxE8T,oBA9K0B,WAC1B,MAAO,wBAA0BrD,EACnC,EA6KEsD,yBAxG+BpJ,GAAO,8BAA8B6F,GAAY7F,4BAyGhFqJ,gBAvGsB,CAAChU,EAAMiU,EAAcC,IAC3C,kBAAoBlU,EAAO,uBAAyBiU,EAAe,SAAWC,EAAa,wBAuG3FC,uBArG6B,CAACnU,EAAMkU,IACpC,kBAAoBlU,EAAO,8BAAgCkU,EAAa,2BC1I1E,MAAM3D,eAAEA,IAAmB6D,GAarBC,GAAiB,CACrB3O,QAAS,CAAE4O,QAAS,gCACpBC,UAAW,CAAED,QAAS,yCACtBE,UAAW,CAAEF,QAAS,mCACtBG,WAAY,CAAEH,SAAS,GACvBI,UAAW,CAAEjC,KAAM,WACnBkC,cAAe,CAAEL,SAAS,GAC1BM,uBAAwB,CAAEnC,KAAM,YAChCoC,2BAA4B,CAAEP,SAAS,GACvCQ,UAAW,CAAER,SAAS,GACtBS,kBAAmB,CAAET,SAAS,GAC9BU,cAAe,CAAEV,QAAS,IAAKhB,QAAS,GACxC2B,cAAe,CAAEX,QAAS,IAAMhB,QAAS,KACzC4B,iBAAkB,CAAEZ,QAAS,EAAGhB,QAAS,GACzCI,qBAAsB,CAAEY,QAAS,IAAMhB,QAAS,GAChD6B,qBAAsB,CAAEb,SAAS,GACjCc,kBAAmB,CAAEd,QAAS,IAC9Be,UAAW,CAAE5C,KAAM,iBACnB6C,4BAA6B,CAAEhB,QAAS,IAAQhB,QAAS,KACzDiC,iBAAkB,CAAEjB,SAAS,GAC7BkB,YAAa,CAAE/C,KAAM,UACrBgD,eAAgB,CAAEhD,KAAM,UACxBiD,cAAe,CAAEjD,KAAM,UACvBkD,YAAa,CAAEC,UAgCjB,SAAoC5V,EAAMyH,EAAO6I,GAC/C,MAAMuF,EAAY,CAAA,EACdpO,EAAMqO,KACRD,EAAUC,GAAKC,GAAiB,GAAG/V,OAAWyH,EAAMqO,GAAIxF,IAEtD7I,EAAMZ,UACRgP,EAAUhP,QAAUkP,GAAiB,GAAG/V,YAAgByH,EAAMZ,QAASyJ,IAEzE,OAAOuF,CACT,GAxCEG,WAAY,CAAE1B,QAAS,IACvB2B,MAAO,CAAE3B,QAAS,IAClB4B,QAAS,CAAE5B,QAAS,KAMhB6B,GAAuB,eAE7B,SAASC,GAAgB1E,GACvB,OAAOA,GAAOA,EAAI1L,QAAQ,OAAQ,GACpC,CAOA,SAAS+P,GAAiB/V,EAAMqW,EAAU/F,GACxC,GAAwB,iBAAb+F,GAA0BA,EAASC,MAAMH,IAApD,CAIA,KAAIE,EAAS/T,OAAS,IAItB,OAAO+T,EAHL/F,EAAOiG,KAAK7F,GAASiD,gBAAgB3T,GAFtC,MAFCsQ,EAAOiG,KAAK7F,GAASwC,gBAAgBlT,GAQzC,CAyJA,IAAAwW,GAAiB,CACjBnC,eAAEA,GACAlL,SA9IF,SAAkBmF,EAASmI,EAASC,EAAiBpG,GACnD,MAAMqG,EAAanR,EAAMe,OAAO,CAAE+J,OAAQ,CAAEgE,QAAShE,IAAY+D,GAAgBqC,GAE3EE,EAAoB,CAI5B,EA8FE,SAASC,EAAoB3W,GAC3BsF,EAAMuB,YAAW,KACf0P,GAAWA,EAAQK,iBAAiB,IAAI/V,EAAOF,uBAAuBX,MAEzE,CAED,IAAI6W,EAASvR,EAAMe,OAAO,CAAA,EAAI+H,GAAW,CAAA,GAQzC,OA1GA,SAAgCyI,GAC9B,MAAMC,EAAOD,EACbvT,OAAOC,KAAKmT,GAAmBrP,SAAQsK,IACrC,QAAsBvK,IAAlB0P,EAAKnF,GAAwB,CAC/B,MAAMC,EAAU8E,EAAkB/E,GAClCvB,GAAUA,EAAOiG,KAAK7F,GAASkB,WAAWC,EAASC,IAC/CA,SACoBxK,IAAlB0P,EAAKlF,KACPkF,EAAKlF,GAAWkF,EAAKnF,WAEhBmF,EAAKnF,GAEf,IAEJ,CAsFDoF,CAAuBF,GAEvBA,EAtFA,SAAuBA,GAIrB,MAAMlP,EAAMrC,EAAMe,OAAO,CAAE,EAAEwQ,GAM7B,OALAvT,OAAOC,KAAKkT,GAAYpP,SAAQvH,SACZsH,IAAdO,EAAI7H,IAAqC,OAAd6H,EAAI7H,KACjC6H,EAAI7H,GAAQ2W,EAAW3W,IAAS2W,EAAW3W,GAAMsU,YAG9CzM,CACR,CA2EQqP,CAAcH,GACvBA,EA1EA,SAA+BA,GAC7B,MAAMlP,EAAMrC,EAAMe,OAAO,CAAE,EAAEwQ,GACvBI,EAAmB1P,IACvB,GAAc,OAAVA,EACF,MAAO,MAET,QAAcH,IAAVG,EACF,OAEF,GAAIzE,MAAMM,QAAQmE,GAChB,MAAO,QAET,MAAM6D,SAAW7D,EACjB,MAAU,YAAN6D,GAAyB,WAANA,GAAwB,WAANA,GAAwB,aAANA,EAClDA,EAEF,UA4CT,OA1CA9H,OAAOC,KAAKsT,GAAQxP,SAAQvH,IAC1B,MAAMyH,EAAQsP,EAAO/W,GACrB,GAAIyH,QAAuC,CACzC,MAAM2P,EAAYT,EAAW3W,GAC7B,QAAkBsH,IAAd8P,EACFP,EAAoBnG,GAASmD,cAAc7T,QACtC,CACL,MAAMiU,EAAemD,EAAU3E,MAAQ0E,EAAiBC,EAAU9C,SAC5DsB,EAAYwB,EAAUxB,UAC5B,GAAIA,EAAW,CACb,MAAMC,EAAYD,EAAU5V,EAAM+W,EAAO/W,GAAOsQ,QAC9BhJ,IAAduO,EACFhO,EAAI7H,GAAQ6V,SAELhO,EAAI7H,EAEzB,MAAiB,GAAqB,QAAjBiU,EAAwB,CACjC,MAAMoD,EAAepD,EAAaqD,MAAM,KAClCpD,EAAaiD,EAAiB1P,GAChC4P,EAAapU,QAAQiR,GAAc,EAChB,YAAjBD,GACFpM,EAAI7H,KAAUyH,EACdoP,EAAoBnG,GAASyD,uBAAuBnU,EAAMkU,MAE1D2C,EAAoBnG,GAASsD,gBAAgBhU,EAAMiU,EAAcC,IACjErM,EAAI7H,GAAQoX,EAAU9C,SAGL,WAAfJ,QAAiD5M,IAAtB8P,EAAU9D,SAAyB7L,EAAQ2P,EAAU9D,UAClFuD,EAAoBnG,GAAS2C,mBAAmBrT,EAAMyH,EAAO2P,EAAU9D,UACvEzL,EAAI7H,GAAQoX,EAAU9D,QAG3B,CACF,CACF,KAGHzL,EAAInC,QAAU0Q,GAAgBvO,EAAInC,SAClCmC,EAAI0M,UAAY6B,GAAgBvO,EAAI0M,WACpC1M,EAAI2M,UAAY4B,GAAgBvO,EAAI2M,WAE7B3M,CACR,CAaQ0P,CAAsBR,GAC/BxG,GAAewG,EAAOzG,QAEfyG,CACT,EA2BES,QAjBF,SAAiBT,GACf,MAAMU,EAAO,CAAA,EAUb,OATIV,IACEA,EAAOpB,kBAAyCrO,IAA1ByP,EAAOpB,YAAYG,IAA8C,OAA1BiB,EAAOpB,YAAYG,KAClF2B,EAAK,kBAAoB,CAACV,EAAOpB,YAAYG,KAE3CiB,EAAOpB,kBAA8CrO,IAA/ByP,EAAOpB,YAAY9O,SAAmD,OAA1BkQ,EAAOpB,YAAYG,KACvF2B,EAAK,uBAAyB,CAACV,EAAOpB,YAAY9O,WAI/C4Q,CACT,GC1NA,MAAM9Q,qBAAEA,IAAyByN,EAmCjC,IAAAsD,GAAiB,CACjBC,aAjCA,SAAsB/Q,EAAU0H,GAC9B,GAAIA,IAAYA,EAAQqG,cACtB,MAAO,GAET,MAAMiD,EAAI,CAAA,EACVA,EAAEhR,EAASiR,qBAAuB,cAAgBlR,GAAqBC,GACnE0H,GAAWA,EAAQkH,cACrBoC,EAAE,0BAA4BtJ,EAAQmH,eAClCnH,EAAQkH,YAAc,IAAMlH,EAAQmH,eACpCnH,EAAQkH,aAEd,MAAMiC,EAAOjB,GAAcgB,QAAQlJ,GAC7BwJ,EAAUtU,OAAOC,KAAKgU,GAU5B,OATIK,EAAQxV,SACVsV,EAAE,uBAAyBE,EACxBC,OACAC,KAAIhU,GACHhB,MAAMM,QAAQmU,EAAKzT,IAAQyT,EAAKzT,GAAK+T,OAAOC,KAAIvQ,GAAS,GAAGzD,KAAOyD,MAAW,CAAC,GAAGzD,KAAOyT,EAAKzT,QAE/FyC,QAAO,CAACwR,EAAWC,IAASD,EAAUE,OAAOD,IAAO,IACpDpV,KAAK,MAEH8U,CACT,EAWAQ,iBATA,SAA0BV,EAASpJ,GACjC,OAAKA,GAAYA,EAAQsG,uBAGlBtG,EAAQsG,uBAAuB,IAAK8C,IAFlCA,CAGX,GC/BA,MAAQW,GAAIC,IAAWlE,iBACfuD,GAAYS,iBAAEA,IAAqBG,GA4D3C,IAAAC,GA1DA,SAAqB5R,EAAU6R,EAAenK,GAC5C,MAAMoK,EAAclT,EAAMe,OAAO,CAAE,eAAgB,oBAAsBoR,GAAa/Q,EAAU0H,IAC1FqK,EAAS,CAAA,EAqDf,OAvCAA,EAAOlE,WAAa,CAACmE,EAAQlH,EAAKmH,KAChC,IAAKjS,EAASkS,YACZ,OAAOzQ,QAAQ0Q,UAGjB,MAAMC,EAAW7S,KAAKE,UAAUuS,GAC1BK,EAAYJ,EAAe,KAAOP,KA8BxC,OA5BA,SAASY,EAAcC,GACrB,MAAMzB,EAAUmB,EACZH,EACAlT,EAAMe,OAAO,CAAE,EAAEmS,EAAa,CAC5B,8BAA+B,IAC/B,4BAA6BO,IAEnC,OAAOrS,EACJkS,YAAY,OAAQpH,EAAK0G,GAAiBV,EAASpJ,GAAU0K,GAC7D/Q,QAAQE,MAAKM,IACZ,GAAKA,EAIL,OAAIA,EAAOtH,QAAU,KAAOJ,EAAOG,uBAAuBuH,EAAOtH,SAAWgY,EACnED,GAAc,GAnC/B,SAAyBzQ,GACvB,MAAMZ,EAAM,CAAE1G,OAAQsH,EAAOtH,QACvBiY,EAAU3Q,EAAO4Q,OAAO,QAC9B,GAAID,EAAS,CACX,MAAME,EAAOlV,KAAKgC,MAAMgT,GACpBE,IACFzR,EAAI0R,WAAaD,EAEpB,CACD,OAAOzR,CACR,CA2BgB2R,CAAgB/Q,MAG1BgR,OAAM,IACDN,EACKD,GAAc,GAEhB7Q,QAAQC,UAEpB,CAEM4Q,EAAc,GAAMO,OAAM,UAG5Bd,CACT,ECrBA,IAAAe,GA9BA,SAASC,EAAarU,EAAQsU,EAAU,IAEtC,GAAe,OAAXtU,GAAqC,iBAAXA,EAC5B,OAAOa,KAAKE,UAAUf,GAGxB,GAAIsU,EAAQC,SAASvU,GACnB,MAAM,IAAIlF,MAAM,kBAGlB,GAAI4C,MAAMM,QAAQgC,GAAS,CAIzB,MAAO,IAHQA,EACZ0S,KAAIE,GAAQyB,EAAazB,EAAM,IAAI0B,EAAStU,MAC5C0S,KAAIE,QAAkB5Q,IAAT4Q,EAAqB,OAASA,IAC5BpV,KAAK,OACxB,CAYD,MAAO,IAVQU,OAAOC,KAAK6B,GACxByS,OACAC,KAAIhU,IACH,MAAMyD,EAAQkS,EAAarU,EAAOtB,GAAM,IAAI4V,EAAStU,IACrD,QAAcgC,IAAVG,EACF,MAAO,GAAGtB,KAAKE,UAAUrC,MAAQyD,OAIpCqS,QAAO5B,QAAiB5Q,IAAT4Q,IACApV,KAAK,OACzB,ECtCA,MAAQsM,kBAAAA,IAAsBgF,GAO9B,SAAS2F,GAAU1S,GACjB,MAAuB,iBAATA,GAA8B,SAATA,GAAmBA,EAAKiP,MAAM,eACnE,CA6DA,SAAS0D,GAAUhW,GACjB,OAAIA,EAAI6V,SAAS,MAAQ7V,EAAI6V,SAAS,KAC7B7V,EAAIgC,QAAQ,KAAM,OAAOA,QAAQ,KAAM,OAEzChC,CACT,CAqDA,IAAAmD,GAAiB,CACjB8S,aA/GA,SAAsB9S,EAAS+S,GAC7B,GAAI/S,EAAS,CACX,GAAI+S,SAAoC5S,IAAjBH,EAAQE,MAAuC,OAAjBF,EAAQE,MAC3D,YAAuBC,IAAhBH,EAAQnD,KAAqC,OAAhBmD,EAAQnD,IAE9C,MAAMA,EAAMmD,EAAQnD,IACdqD,OAAwBC,IAAjBH,EAAQE,KAAqB,OAASF,EAAQE,KACrD8S,EAAYJ,GAAU1S,GACtB+S,EAAoB,UAAT/S,GAAqBrD,SAA6C,KAARA,EAC3E,GAAa,UAATqD,EAAkB,CACpB,MAAMgT,EAAQ7W,OAAOC,KAAK0D,GAAS2S,QAAO9V,GAAe,SAARA,IACjD,OACEoW,GACAC,EAAMC,OAAMtW,GAAO+V,GAAU/V,MAC7BqW,EAAMC,OAAMtW,IACV,MAAMuW,EAAapT,EAAQnD,GAAKA,IAChC,OAAOuW,SAAkE,KAAfA,IAG/D,CACD,OAAOH,GAAYD,CACpB,CACD,OAAO,CACT,EAyFAK,eArCA,SAAwBrT,EAASmJ,EAASlB,MACxC,IAAKjI,EACH,OAGF,MAAM1D,EAAO,CAAA,GACP4D,KAAEA,EAAIrD,IAAEA,GAAQmD,EAEtB,OAAQE,GACN,UAAKC,EACH7D,EAAKgX,KAAO,GAAGzW,IACf,MACF,IAAK,QACHR,OAAOkX,QAAQvT,GACZ2S,QAAO,EAAE9V,KAAiB,SAARA,IAClBuD,SAAQ,EAAEvD,EAAKyD,MACVA,GAASA,EAAMzD,MACjBP,EAAKO,GAAOyD,EAAMzD,QAGxB,MACF,KAAK,KACHsM,EAAOiG,KAAK,qCAAqCpP,KACjD,MACF,IAAK,GACHmJ,EAAOiG,KAAK,mCAAmCpP,KAC/C,MACF,QACE1D,EAAK4D,GAAQ,GAAGrD,IAIpB,OAAOP,CACT,EAKAkX,gBAnFA,SAAyBxT,GACvB,OAAIA,EACmB,OAAjBA,EAAQE,WAAkCC,IAAjBH,EAAQE,KAC5B,CAAC,QAEW,UAAjBF,EAAQE,KACH,CAACF,EAAQE,MAEX7D,OAAOC,KAAK0D,GAAS2S,QAAOzS,GAAiB,SAATA,IAEtC,EACT,EAyEEuT,gBAvDF,SAAyBzT,GACvB,GAAIA,EAAS,CACX,SAAsBG,IAAjBH,EAAQE,MAAuC,OAAjBF,EAAQE,MAAkC,SAAjBF,EAAQE,OAAoBF,EAAQnD,IAC9F,OAAOmD,EAAQnD,IACV,GAAqB,UAAjBmD,EAAQE,MAAoBF,EAAQnD,IAC7C,MAAO,GAAGmD,EAAQE,QAAQ2S,GAAU7S,EAAQnD,OACvC,GAAqB,UAAjBmD,EAAQE,KACjB,OAAO7D,OAAOC,KAAK0D,GAChB4Q,OACA+B,QAAO9V,GAAe,SAARA,IACdgU,KAAIhU,GAAO,GAAGA,KAAOgW,GAAU7S,EAAQnD,GAAKA,SAC5ClB,KAAK,IAEX,CACH,GC3FA,MAAQ6X,gBAAAA,IAAoBvG,GAyG5B,IAAAyG,GA7FA,WACE,MAAMC,EAAK,CAAA,EAEX,IAAIC,EAAY,EACdC,EAAU,EACVC,EAAW,CAAE,EACbC,EAAe,CAAA,EAoFjB,OAlFAJ,EAAGK,eAAiBlK,IAClB,GAAmB,YAAfA,EAAM5J,KAAoB,CAC5B,MAAM+T,EACJnK,EAAMjN,IACN,KACqB,OAApBiN,EAAMoK,gBAA0C/T,IAApB2J,EAAMoK,UAA0BpK,EAAMoK,UAAY,IAC/E,KACmB,OAAlBpK,EAAMpK,cAAsCS,IAAlB2J,EAAMpK,QAAwBoK,EAAMpK,QAAU,IACrEyU,EAAaL,EAASG,GAC5B,IAAIf,EAAQa,EAAajK,EAAMjN,KAC1BqW,IACHA,EAAQ,IAAIkB,IACZL,EAAajK,EAAMjN,KAAOqW,GA9BlC,SAAkBpJ,GAChB,OAAIA,EAAM9J,QACDwT,GAAgB1J,EAAM9J,SAE3B8J,EAAMuK,YACDhY,OAAOC,KAAKwN,EAAMuK,aAEpB,EACT,CAwBMC,CAASxK,GAAO1J,SAAQF,GAAQgT,EAAMqB,IAAIrU,KAEtCiU,EACFA,EAAWnK,MAAQmK,EAAWnK,MAAQ,EAEtC8J,EAASG,GAAc,CACrBjK,MAAO,EACPnN,IAAKiN,EAAMjN,IACX6C,QAASoK,EAAMpK,QACfwU,UAAWpK,EAAMoK,UACjB5T,MAAOwJ,EAAMxJ,MACb6M,QAASrD,EAAMqD,UAGD,IAAdyG,GAAmB9J,EAAM0K,aAAeZ,KAC1CA,EAAY9J,EAAM0K,cAEhB1K,EAAM0K,aAAeX,IACvBA,EAAU/J,EAAM0K,aAEnB,GAGHb,EAAGc,WAAa,KACd,MAAMC,EAAW,CAAA,EACjB,IAAIC,GAAQ,EACZ,IAAK,MAAMrQ,KAAKjI,OAAOuY,OAAOd,GAAW,CACvC,IAAIe,EAAOH,EAASpQ,EAAEzH,KACjBgY,IACHA,EAAO,CACL1H,QAAS7I,EAAE6I,QACX2G,SAAU,GACVC,aAAc,IAAIA,EAAazP,EAAEzH,OAEnC6X,EAASpQ,EAAEzH,KAAOgY,GAEpB,MAAMC,EAAa,CACjBxU,MAAOgE,EAAEhE,MACT0J,MAAO1F,EAAE0F,YAES7J,IAAhBmE,EAAE4P,WAA2C,OAAhB5P,EAAE4P,YACjCY,EAAWZ,UAAY5P,EAAE4P,gBAET/T,IAAdmE,EAAE5E,SAAuC,OAAd4E,EAAE5E,QAC/BoV,EAAWpV,QAAU4E,EAAE5E,QAEvBoV,EAAWC,SAAU,EAEvBF,EAAKf,SAAStY,KAAKsZ,GACnBH,GAAQ,CACT,CACD,OAAOA,EACH,KACA,CACEf,YACAC,UACAmB,SAAUN,EACVxU,KAAM,YAIdyT,EAAGsB,aAAe,KAChBrB,EAAY,EACZC,EAAU,EACVC,EAAW,CAAA,EACXC,EAAe,CAAA,GAGVJ,CACT,EC5CA,IAAAuB,GApDA,SAA8BC,GAC5B,IAAIC,EAAc,CAAA,EACdC,EAAW,CAAA,EA4Cf,MAAO,CACLrB,eApCF,SAAwBlK,GACtB,GAAmB,YAAfA,EAAM5J,KAAoB,CAC5B,MAAMrD,EAAM2V,GAAa1I,EAAM9J,SAC/B,IAAKnD,EACH,OAGF,IAAIyY,EAAaF,EAAYvY,GACxByY,IACHF,EAAYvY,GAAO0Y,KACnBD,EAAaF,EAAYvY,GACzBwY,EAASxY,GAAOiN,EAAM9J,SAGxBsV,EAAWtB,eAAelK,EAC3B,CACF,EAqBC0L,aAfF,WACE,MAAMC,EAAqBL,EACrBM,EAAuBL,EAI7B,OAFAD,EAAc,CAAA,EACdC,EAAW,CAAA,EACJhZ,OAAOkX,QAAQkC,GAAoB5E,KAAI,EAAEhU,EAAKyY,MACnD,MAAMK,EAAUL,EAAWb,aAE3B,OADAkB,EAAQ3V,QAAUmV,EAAcxC,OAAO+C,EAAqB7Y,IACrD8Y,IAEV,EAMH,ECpDA,SAASC,GAAwB/Y,GAC/B,OAAOA,EAAIgC,QAAQ,KAAM,MAAMA,QAAQ,MAAO,KAChD,CAMA,SAASgX,GAAcC,GAErB,OAD+BA,EAAUnX,WAAW,KAAOmX,EAAUpX,UAAU,GAAKoX,GAEjF3F,MAAM,KACNU,KAAIkF,GAAcA,EAAUja,QAAQ,MAAQ,EAAIia,EAAUlX,QAAQ,MAAO,KAAKA,QAAQ,MAAO,KAAOkX,GACzG,CAMA,SAASC,GAAUF,GACjB,OAAQA,EAAUnX,WAAW,IAC/B,CAOA,SAASsX,GAAQtZ,EAAGC,GAClB,MAAMsZ,EAAaF,GAAUrZ,GACvBwZ,EAAaH,GAAUpZ,GAC7B,GAAIsZ,GAAcC,EAChB,OAAOxZ,IAAMC,EAEf,GAAIsZ,EAAY,CACd,MAAME,EAAcP,GAAcjZ,GAClC,OAA2B,IAAvBwZ,EAAYjb,QAGTwB,IAAMyZ,EAAY,EAC1B,CACD,GAAID,EAAY,CACd,MAAME,EAAcR,GAAclZ,GAClC,OAA2B,IAAvB0Z,EAAYlb,QAGTyB,IAAMyZ,EAAY,EAC1B,CACD,OAAO1Z,IAAMC,CACf,CAkBA,SAAS0Z,GAAmBC,GAC1B,MAAO,IAAIX,GAAwBW,IACrC,CAgEA,IAAAC,GAAiB,CACfC,eAzDF,SAAwBC,EAAQC,GAC9B,MAAMC,EAAQ,GACRC,EAAS,CAAA,EACTC,EAAW,GAYjB,IAVAF,EAAMpb,QACDa,OAAOC,KAAKoa,GAAQ7F,KAAIhU,IAAQ,CACjCA,MACAka,IAAKT,GAAmBzZ,GACxBma,OAAQN,EACRO,OAAQJ,EACRpE,QAAS,CAACiE,QAIPE,EAAMzb,QAAQ,CACnB,MAAM4V,EAAO6F,EAAMM,MACnB,GAAKP,EAAWQ,MAAKJ,GAAOd,GAAQc,EAAKhG,EAAKgG,OAiC5CD,EAAStb,KAAKuV,EAAKgG,SAjCgC,CACnD,MAAMzW,EAAQyQ,EAAKiG,OAAOjG,EAAKlU,KAG/B,GAAc,OAAVyD,EACFyQ,EAAKkG,OAAOlG,EAAKlU,KAAOyD,OACnB,GAAIzE,MAAMM,QAAQmE,GACvByQ,EAAKkG,OAAOlG,EAAKlU,KAAO,IAAIyD,QACvB,GAAqB,iBAAVA,EAAoB,CAMpC,GAAIyQ,EAAK0B,QAAQC,SAASpS,GACxB,SAGFyQ,EAAKkG,OAAOlG,EAAKlU,KAAO,CAAA,EAExB+Z,EAAMpb,QACDa,OAAOC,KAAKgE,GAAOuQ,KAAIhU,IAAQ,OAChCA,MACAka,KA7DEpa,EA6DQoU,EAAKgG,IA7DVna,EA6DegZ,GAAwB/Y,GA5D/C,GAAGF,KAAKC,KA6DLoa,OAAQ1W,EACR2W,OAAQlG,EAAKkG,OAAOlG,EAAKlU,KACzB4V,QAAS,IAAI1B,EAAK0B,QAASnS,IAhEvC,IAAc3D,EAAGC,KAmEjB,MACQmU,EAAKkG,OAAOlG,EAAKlU,KAAOyD,CAEhC,CAGG,CACD,MAAO,CAAEuW,SAAQC,SAAUA,EAASlG,OACtC,EAIEqF,WACAK,uBCAF,IAAAc,GA3IA,SAAuBxH,GACrB,MAAM+C,EAAS,CAAA,EAET3E,EAAuB4B,EAAO5B,qBAC9BC,EAAoB2B,EAAO3B,mBAAqB,GAGhDoJ,EAAsB,CAAC,MAAO,OAAQ,QAAS,aAE/CC,EAA+B,CAAC,OAAQ,KAAM,YAAa,WAAY,QAAS,SAAU,WAmB1FC,EAAmB,CAACvX,EAASwX,KACjC,GAAuB,iBAAZxX,GAAoC,OAAZA,GAAoBnE,MAAMM,QAAQ6D,GACnE,OAGF,MAAM6W,OAAEA,EAAMC,SAAEA,GAAaW,GAAmBhB,eAC9CzW,EAlB0B,EAACA,EAASwX,KACrCxJ,GAAyBwJ,GAAmBxX,EAAQ0X,UACjDrb,OAAOC,KAAK0D,GACZ,IAAIiO,KAAwBjO,EAAQ2X,OAAS3X,EAAQ2X,MAAM1J,mBAAsB,KACnF0E,QAAOtS,IAASgX,EAAoBF,MAAKS,GAAiBH,GAAmBxB,QAAQ5V,EAAMuX,OAe3FC,CAAsB7X,EAASwX,IAqBjC,OAnBAX,EAAOha,IAAM0D,OAAOsW,EAAOha,KACvBia,EAAS3b,SACN0b,EAAOc,QACVd,EAAOc,MAAQ,IAEjBd,EAAOc,MAAMG,mBAAqBhB,GAEhCD,EAAOc,eACFd,EAAOc,MAAyB,kBACE,IAArCtb,OAAOC,KAAKua,EAAOc,OAAOxc,eACrB0b,EAAOc,YAKOxX,IAArB0W,EAAOa,YACTb,EAAOa,YAAcb,EAAOa,WAGvBb,GAgFT,OAVAlE,EAAOA,OAAS,CAAC3S,EAASwX,GAAkB,SACrBrX,IAAjBH,EAAQE,MAAuC,OAAjBF,EAAQE,KACjCqX,EAzCgBjE,KACzB,MAAMyE,EAAW,IAKXzE,EAAK0E,QAAU,GAGnB9X,KAAM,OAENrD,IAAKyW,EAAKzW,UAGWsD,IAAnBmT,EAAKoE,YACPK,EAASL,YAAcpE,EAAKoE,WAK9B,IAAK,MAAM7a,KAAOya,SACTS,EAASlb,QACEsD,IAAdmT,EAAKzW,IAAoC,OAAdyW,EAAKzW,KAClCkb,EAASlb,GAAO0D,OAAO+S,EAAKzW,KAahC,YATmCsD,IAA/BmT,EAAK2E,uBAAsE,OAA/B3E,EAAK2E,wBACnDF,EAASJ,MAAQI,EAASJ,OAAS,CAAA,EAGnCI,EAASJ,MAAM1J,kBAAoBqF,EAAK2E,sBAAsBpH,KAAI0F,GAChEA,EAAQ5X,WAAW,KAAO8Y,GAAmBnB,mBAAmBC,GAAWA,KAIxEwB,GAKmBG,CAAmBlY,GAAUwX,GAC3B,UAAjBxX,EAAQE,KAhEG,EAACF,EAASwX,KAChC,MAAMO,EAAW,CACf7X,KAAMF,EAAQE,MAEVmU,EAAchY,OAAOC,KAAK0D,GAEhC,IAAK,MAAMoT,KAAciB,EACvB,GAAmB,SAAfjB,EAAuB,CACzB,MAAM+E,EAAkBZ,EAAiBvX,EAAQoT,GAAaoE,GAC1DW,IACFJ,EAAS3E,GAAc+E,EAE1B,CAEH,OAAOJ,GAmDEK,CAAgBpY,EAASwX,GAEzBD,EAAiBvX,EAASwX,GAI9B7E,CACT,ECrIA,MAAQU,eAAAA,IAAmBpG,GA6K3B,IAAAoL,GA3KA,SACE5Y,EACA0H,EACAmK,EACAgH,EAAyB,KACzBhJ,EAAU,KACVkC,EAAS,MAET,MAAM+G,EAAY,CAAA,EACZC,EAAchH,GAAUiH,GAAYhZ,EAAU6R,EAAenK,GAC7DuR,EAAgBra,EAAMC,cAAc6I,EAAQkG,UAAW,gBAAkBiE,GACzE6D,EAAgBwD,GAAcxR,GAC9BmO,EAAasD,GAAqBzD,GAClCpH,EAAmB5G,EAAQ4G,iBAC3BF,EAAgB1G,EAAQ0G,cACxBC,EAAgB3G,EAAQ2G,cACxB3E,EAAShC,EAAQgC,OACvB,IAII0P,EAJAC,EAAQ,GACRC,EAAoB,EACpBC,GAAW,EACXC,GAAmB,EAGvB,SAASC,IACP,OAA4B,IAArBnL,GAA2E,IAAjD1H,KAAKM,MAAMN,KAAKkB,SAAWwG,EAC7D,CAcD,SAASoL,EAAgBpS,GACvB,MAAMrG,EAAMrC,EAAMe,OAAO,CAAE,EAAE2H,GAe7B,MAXe,aAAXA,EAAE7G,MAAkC,YAAX6G,EAAE7G,MAAiC,WAAX6G,EAAE7G,KACrDQ,EAAIV,QAAUmV,EAAcxC,OAAO5L,EAAE/G,UAErCU,EAAI2T,YAYChB,GAZqCtM,EAYhB/G,QAASmJ,UAX5BzI,EAAa,SAGP,YAAXqG,EAAE7G,cACGQ,EAAiB,mBACjBA,EAA0B,sBAE5BA,CACR,CAMD,SAAS0Y,EAAYtP,GACfgP,EAAM3d,OAAS0S,GACjBiL,EAAMtd,KAAKsO,GACXmP,GAAmB,IAEdA,IACHA,GAAmB,EACnB9P,EAAOiG,KAAK7F,GAASwB,0BAEnBuN,GAEFA,EAAuBe,yBAG5B,CA4FD,OA1FAd,EAAUe,QAAU,SAASxP,GAC3B,GAAIkP,EACF,OAEF,IAAIO,GAAe,EACfC,GAAgB,EAxDtB,IAA0BzS,EA2ExB,GAhBAuO,EAAWtB,eAAelK,GAIP,YAAfA,EAAM5J,KACJgZ,MACFK,IAAiBzP,EAAM2P,YACvBD,KAlEoBzS,EAkEa+C,GAjE/B4P,sBAKG3S,EAAE2S,qBAAuBX,GAAqBhS,EAAE2S,sBAAuB,IAAIzc,MAAOE,WA+DzFoc,EAAeL,IAGbK,GACFH,EAAYD,EAAgBrP,IAE1B0P,EAAe,CACjB,MAAMG,EAAatb,EAAMe,OAAO,CAAA,EAAI0K,EAAO,CAAE5J,KAAM,UACnDyZ,EAAW3Z,QAAUmV,EAAcxC,OAAOgH,EAAW3Z,gBAC9C2Z,EAAwB,mBACxBA,EAAiC,qBACxCP,EAAYO,EACb,CACL,EAEEpB,EAAUqB,MAAQC,iBAChB,GAAIb,EACF,OAAO9X,QAAQ0Q,UAEjB,MAAMkI,EAAehB,EAerB,OAdkBxD,EAAWE,eAEnBpV,SAAQuV,IACZtZ,OAAOC,KAAKqZ,EAAQX,UAAU7Z,QAChC2e,EAAate,KAAKma,MAIlB2C,GAIFA,EAAuByB,qBAAqBD,EAAa3e,QAE/B,IAAxB2e,EAAa3e,OACR+F,QAAQ0Q,WAEjBkH,EAAQ,GACR3P,EAAO6Q,MAAMzQ,GAASQ,mBAAmB+P,EAAa3e,SAC/Cqd,EAAYlL,WAAWwM,EAAcpB,GAAe1X,MAAKiZ,IAC1DA,IACEA,EAAa7H,aACf2G,EAAoBkB,EAAa7H,YAE9BxY,EAAOG,uBAAuBkgB,EAAajgB,UAC9Cgf,GAAW,GAETiB,EAAajgB,QAAU,KACzBqE,EAAMuB,YAAW,KACf0P,EAAQK,iBACN,IAAI/V,EAAON,0BACTiQ,GAAS0B,iBAAiBgP,EAAajgB,OAAQ,gBAAiB,qCAOhF,EAEEue,EAAUxc,MAAQ,WAChB,MAAMme,EAAY,KAChB3B,EAAUqB,QACVf,EAAa/Y,WAAWoa,EAAWpM,IAErC+K,EAAa/Y,WAAWoa,EAAWpM,EACvC,EAEEyK,EAAU4B,KAAO,WACfC,aAAavB,EACjB,EAESN,CACT,ECtHA,IAAA8B,GA3DA,SAAsBlR,GACpB,MAAMmG,EAAU,CAAA,EACVmC,EAAS,CAAA,EAsDf,OAlDAnC,EAAQgL,GAAK,SAASxQ,EAAOyQ,EAASva,GACpCyR,EAAO3H,GAAS2H,EAAO3H,IAAU,GACjC2H,EAAO3H,GAAS2H,EAAO3H,GAAOkH,OAAO,CACnCuJ,QAASA,EACTva,QAASA,GAEf,EAEEsP,EAAQkL,IAAM,SAAS1Q,EAAOyQ,EAASva,GACrC,GAAKyR,EAAO3H,GAGZ,IAAK,IAAIrP,EAAI,EAAGA,EAAIgX,EAAO3H,GAAO3O,OAAQV,IACpCgX,EAAO3H,GAAOrP,GAAG8f,UAAYA,GAAW9I,EAAO3H,GAAOrP,GAAGuF,UAAYA,IACvEyR,EAAO3H,GAAS2H,EAAO3H,GAAO/G,MAAM,EAAGtI,GAAGuW,OAAOS,EAAO3H,GAAO/G,MAAMtI,EAAI,IAGjF,EAEE6U,EAAQmL,KAAO,SAAS3Q,GACtB,IAAK2H,EAAO3H,GACV,OAKF,MAAM4Q,EAAiBjJ,EAAO3H,GAAO/G,MAAM,GAC3C,IAAK,IAAItI,EAAI,EAAGA,EAAIigB,EAAevf,OAAQV,IACzCigB,EAAejgB,GAAG8f,QAAQ9Y,MAAMiZ,EAAejgB,GAAGuF,QAASnE,MAAMxC,UAAU0J,MAAMvF,KAAKgF,UAAW,GAEvG,EAEE8M,EAAQqL,UAAY,WAClB,OAAOte,OAAOC,KAAKmV,EACvB,EAEEnC,EAAQsL,sBAAwB,SAAS9Q,GACvC,OAAO2H,EAAO3H,GAAS2H,EAAO3H,GAAO3O,OAAS,CAClD,EAEEmU,EAAQK,iBAAmB,SAAS1O,GAC7BA,IA3CwBwQ,EA8Cb,SACdtY,KAAKshB,KAAK,QAASxZ,IAElBkI,GAAUZ,SAAStH,MAAMA,EAAMlI,SAEtC,EACSuW,CACT,ECzCA,MAAMuL,GAAa,QACjBC,GAAe,cACfC,GAAe,SAgEjB,IAAAC,GA9DA,SAAoCC,GAClC,IAAIC,GAAY,EACdC,GAAS,EACTC,EAAe,KACfC,EAAwB,KAE1B,MAAMC,EAAe,IAAIpa,SAAQ0Q,IAC/B,MAAM2J,EAAU,KACdN,EAAaT,IAAIK,GAAYU,GAC7B3J,KAEFqJ,EAAaX,GAAGO,GAAYU,MAC3BjJ,OAAM,SAET,MAAO,CACLkJ,yBAA0B,IACpBH,IAGAH,EACKha,QAAQ0Q,UAEbuJ,EACKja,QAAQC,OAAOia,IAExBC,EAAwB,IAAIna,SAAQ,CAAC0Q,EAASzQ,KAC5C,MAAMsa,EAAY,KAChBR,EAAaT,IAAIM,GAAcW,GAC/B7J,KAEI8J,EAAYlY,IAChByX,EAAaT,IAAIO,GAAcW,GAC/Bva,EAAOqC,IAETyX,EAAaX,GAAGQ,GAAcW,GAC9BR,EAAaX,GAAGS,GAAcW,MAEzBL,IAGTM,gBAAiB,IAAML,EAEvBM,cAAe,KACRV,GAAcC,IACjBD,GAAY,EACZD,EAAaR,KAAKK,IAClBG,EAAaR,KAAKI,MAItBgB,cAAerY,IACR0X,GAAcC,IACjBA,GAAS,EACTC,EAAe5X,EACfyX,EAAaR,KAAKM,GAAcvX,GAChCyX,EAAaR,KAAKI,KAEpBI,EAAatL,iBAAiBnM,IAGpC,EC/BA,IAAAsY,GA/CA,SAA6BC,EAASC,EAAaC,EAAMC,GACvD,MAAMC,EAAQ,CAAA,EAEd,SAASC,IACP,IAAIvf,EAAM,GACV,MAAMmD,EAAUkc,EAAMG,aAItB,OAHIrc,IACFnD,EAAMof,GAAQ5d,EAAMX,KAAKsB,KAAKE,UAAUc,KAEnC,MAAQgc,EAAc,IAAMnf,CACpC,CAkCD,OA9BAsf,EAAMG,UAAY,IAChBP,EAAQQ,IAAIH,KAAepb,MAAKwb,IAC9B,GAAIA,QACF,OAAO,KAET,IACE,IAAIC,EAAOzd,KAAKC,MAAMud,GACtB,GAAIC,EAAM,CACR,MAAMC,EAASD,EAAKE,aACLxc,IAAXuc,GAAwBA,EAAS,EACnCD,EAAOpe,EAAMmC,iCAAiCic,UAEvCA,EAAc,OAExB,CACD,OAAOA,CACR,CAAC,MAAOG,GACP,OAAOT,EAAMU,aAAa7b,MAAK,IAAM,MACtC,KAILmb,EAAMW,UAAYrc,IAChB,MAAMgc,EAAOpe,EAAMe,OAAO,CAAA,EAAIqB,EAAO,CAAEkc,QAAS,IAChD,OAAOZ,EAAQxY,IAAI6Y,IAAepd,KAAKE,UAAUud,KAInDN,EAAMU,WAAa,IAAMd,EAAQgB,MAAMX,KAEhCD,CACT,ECiCA,IAAAa,GAhEA,SAA2BC,EAAsB9T,GAC/C,MAAM4S,EAAU,CAAA,EAChB,IAAImB,GAAc,EAElB,MAAMC,EAAW3Z,IACV0Z,IACHA,GAAc,EACd/T,EAAOiG,KAAK7F,GAASyC,wBAAwBxI,MAsDjD,OAlDAuY,EAAQqB,UAAY,MAAQH,EAG5BlB,EAAQQ,IAAM1f,GACZ,IAAIqE,SAAQ0Q,IACLqL,EAILA,EACGV,IAAI1f,GACJmE,KAAK4Q,GACLU,OAAM9O,IACL2Z,EAAS3Z,GACToO,OAAQzR,MARVyR,OAAQzR,MAad4b,EAAQxY,IAAM,CAAC1G,EAAKyD,IAClB,IAAIY,SAAQ0Q,IACLqL,EAILA,EACG1Z,IAAI1G,EAAKyD,GACTU,MAAK,IAAM4Q,GAAQ,KACnBU,OAAM9O,IACL2Z,EAAS3Z,GACToO,GAAQ,MARVA,GAAQ,MAadmK,EAAQgB,MAAQlgB,GACd,IAAIqE,SAAQ0Q,IACLqL,EAILA,EACGF,MAAMlgB,GACNmE,MAAK,IAAM4Q,GAAQ,KACnBU,OAAM9O,IACL2Z,EAAS3Z,GACToO,GAAQ,MARVA,GAAQ,MAYPmK,CACT,EC7EA,MAAMzd,cAAEA,GAAaM,gBAAEA,GAAeV,qBAAEA,IAAyB+O,gBACzDuD,GAAYS,iBAAEA,IAAqBG,IACrCrX,uBAAEA,IAA2BsjB,EAiMnC,IAAAC,GA9KA,SAAgB7d,EAAUmQ,EAAQoM,EAAa1D,GAC7C,MAAM/Z,EAAUqR,EAAOxC,UACjBjE,EAASyG,EAAOzG,OAChBoU,EAAS,CAAA,EACTC,EAAgBlf,GAAcC,EAAS,SAAWyd,GAClDrO,EAAYiC,EAAOjC,UACnB8P,EAAc7N,EAAOhC,kBACrB8P,EAAqB9N,EAAOrD,qBAC5BgE,EAAUC,GAAa/Q,EAAUmQ,GACvC,IAGI+N,EAHAC,GAA6B,EAC7BjK,EAAK,KACLkK,EAA4B,KAE5B7d,EAAU,KACVic,EAAO,KACP6B,EAAW,KACXC,EAAa,EAWjB,SAASC,IACP,MAAMC,GALQC,EALhB,WACE,MAAMD,EAAQP,EAAqBrX,KAAKK,IAAI,EAAGqX,GAC/C,OAAOE,EAvBW,QAuB6BA,CAChD,CAOsBE,GAJdD,EAAsB7X,KAAK+X,MA1BlB,GA0BwB/X,KAAKkB,SAAyB2W,IADxE,IAAgBA,EAOd,OADAH,GAAc,EACPE,CACR,CA8BD,SAASI,EAAY7a,GAInB,GAAIA,EAAIxJ,QAAgC,iBAAfwJ,EAAIxJ,SAAwBD,GAAuByJ,EAAIxJ,QAU9E,OAPAskB,IACAnV,EAAOlI,MAAMsI,GAASqD,yBAAyBpJ,SAE3Cqa,IACFzD,aAAayD,GACbA,EAA4B,OAKhC,MAAMI,EAAQD,IAETJ,IACHzU,EAAOiG,KAAK7F,GAAS+C,YAAY9I,EAAKya,IACtCL,GAA6B,GAE/BW,GAAoB,GACpBD,IACAE,EAAWP,EACZ,CAED,SAASO,EAAWP,GACbJ,IACCI,EACFJ,EAA4B/d,WAAW2e,EAAgBR,GAEvDQ,IAGL,CAED,SAASA,IAEP,IAAIlU,EADJsT,EAA4B,KAE5B,IAAIa,EAAQ,GACZ,MAAMvX,EAAU,CAAEoJ,UAASoO,kBA3GC,KA4G5B,GAAIlf,EAASmf,mBAAoB,CAC3B3C,UACFyC,EAAQ,KAAOzC,GAEbtO,EACElO,EAASof,yBACXtU,EAAMiT,EACNrW,EAAQ2X,OAAS,SACjB3X,EAAQoJ,QAAQ,gBAAkB,mBAClCpJ,EAAQ4X,KAAO/f,KAAKE,UAAUc,KAG9BuK,EAAMjM,GAAcC,EAAS,SAAWyd,GACxC0C,EAAQ,IAGVnU,EAAMiT,EAAgB,IAAM5e,GAAgBI,KAAKE,UAAUc,IAE7DmH,EAAQoJ,QAAUU,GAAiB9J,EAAQoJ,QAASX,GAChD6N,IACFiB,EAAQA,GAASA,EAAQ,IAAM,IAAM,oBAEvCnU,EAAMA,GAAOmU,EAAQ,IAAM,IAAMA,EAEjCJ,IACAnV,EAAO6V,KAAKzV,GAAS8C,iBAAiB9B,IA4BxCoT,GAA6B,IAAI1gB,MAAOE,UAzBtCwW,EAAKlU,EAASmf,mBAAmBrU,EAAKpD,GACtC,IAAK,MAAMtK,KAAOihB,EACZ5f,GAAqB4f,EAAUjhB,IACjC8W,EAAGsL,iBAAiBpiB,EAAKihB,EAASjhB,IAItC8W,EAAGuL,QAAUb,EAEb1K,EAAGwL,OAAS,KAEVpB,EAAa,EAEhB,CACF,CAED,SAASO,IACH3K,IACFxK,EAAO6V,KAAKzV,GAAS6C,iBACrBuH,EAAGyL,QACHzL,EAAK,KAER,CAMD,SAAS4K,EAAoBc,GACvB1B,GAA8BrF,GAChCA,EAAuBgH,iBACrB3B,GACC0B,GACD,IAAIpiB,MAAOE,UAAYwgB,GAG3BA,EAA6B,IAC9B,CAED,OA1IAJ,EAAOgC,QAAU,SAAStf,EAAYuf,EAASC,GAC7Czf,EAAUC,EACVgc,EAAOuD,EACP1B,EAAW,CAAA,EACX,IAAK,MAAMjhB,KAAO4iB,GAAe,GAC/B3B,EAASjhB,GAAO,SAASkK,GAKvB6W,GAA6B,EAC7BW,GAAoB,GACpBkB,EAAY5iB,IAAQ4iB,EAAY5iB,GAAKkK,EAC7C,EAEIyX,GACJ,EAEEjB,EAAOmC,WAAa,WAClBtF,aAAayD,GACbA,EAA4B,KAC5BS,GACJ,EAEEf,EAAOoC,YAAc,WACnB,SAAUhM,GAAMlU,EAASmgB,qBAAuBngB,EAASmgB,oBAAoBjM,GACjF,EAgHS4J,CACT,EC/IA,IAAAsC,GArCA,SAA0BC,GACxB,IAAIC,EACAC,EACAC,EACAC,EAEJ,MAAMC,EAAY,CAElBA,WAAuB,CAACC,EAAGC,KACzBN,EAAiBK,EACjBJ,GAAmBA,IACnBA,EAAkBK,EAElBD,EAAEpf,MACAM,IACMye,IAAmBK,IACrBH,EAAa3e,GACbwe,GAAaA,QAGjB7e,IACM8e,IAAmBK,IACrBF,EAAYjf,GACZ6e,GAAaA,UAWrB,OALAK,EAAUG,cAAgB,IAAIpf,SAAQ,CAAC0Q,EAASzQ,KAC9C8e,EAAerO,EACfsO,EAAc/e,KAGTgf,CACT,EC7CA,MAAMlP,iBAAEA,GAAgBT,aAAEA,IAAiBvD,GAErCsT,GAAkB,mBAyGxB,IAAAC,GA/FA,SAAmB/gB,EAAU0H,EAAS6U,GACpC,MAAMzd,EAAU4I,EAAQ5I,QAClBoP,EAAYxG,EAAQwG,UACpB8P,EAActW,EAAQyG,kBACtBzE,EAAShC,EAAQgC,OAEjBsX,EAAY,CAAA,EAEZC,EAAiB,CAAA,EAEvB,SAASC,EAAUC,EAAU7B,GAC3B,IAAKtf,EAASkS,YACZ,OAAO,IAAIzQ,SAAQ,CAAC0Q,EAASzQ,KAC3BA,EAAO,IAAIvH,EAAOD,iBAAiB4P,GAAS4B,uBAIhD,MAAM2T,EAASC,EAAO,SAAW,MAC3BxO,EAAUC,GAAa/Q,EAAU0H,GACnC4X,IACFxO,EAAQ,gBAAkBgQ,IAG5B,IAAIJ,EAAYO,EAAeE,GAC1BT,IACHA,EAAYU,IAAiB,YAEpBH,EAAeE,MAExBF,EAAeE,GAAYT,GAG7B,MAAMW,EAAMrhB,EAASkS,YAAYmN,EAAQ8B,EAAU3P,GAAiBV,EAASpJ,GAAU4X,GACjFqB,EAAIU,EAAIhgB,QAAQE,MACpBM,IACE,GAAsB,MAAlBA,EAAOtH,OAAgB,CAEzB,GACEsH,EAAO4Q,OAAO,iBACd5Q,EAAO4Q,OAAO,gBAAgBxT,UAAU,EAAG6hB,MAA4BA,GAEvE,OAAOvhB,KAAKC,MAAMqC,EAAOyd,MACpB,CACL,MAAMhmB,EAAUwQ,GAASgC,mBAAmBjK,EAAO4Q,OAAO,iBAAmB,IAC7E,OAAOhR,QAAQC,OAAO,IAAIvH,EAAOD,iBAAiBZ,GACnD,CACX,CACU,OAAOmI,QAAQC,OAvDzB,SAA0BG,GACxB,OAAsB,MAAlBA,EAAOtH,OACF,IAAIJ,EAAOL,4BAA4BgQ,GAASqB,uBAEhD,IAAIhR,EAAOD,iBAAiB4P,GAASuB,mBAAmBxJ,EAAOyf,YAAcxgB,OAAOe,EAAOtH,SAEtG,CAiDgCgnB,CAAiB1f,OAG3CyF,GAAK7F,QAAQC,OAAO,IAAIvH,EAAOD,iBAAiB4P,GAAS0C,aAAalF,OAMxE,OAJAoZ,EAAUc,WAAWb,GAAG,KAEtBU,EAAII,QAAUJ,EAAII,YAEbf,EAAUG,aAClB,CAmCD,OA/BAG,EAAUE,UAAY,SAASniB,GAC7B,OAAOmiB,EAAUtiB,EAAMC,cAAcC,EAASC,GAAO,KACzD,EAIEiiB,EAAUU,kBAAoB,SAASnhB,EAASic,GAC9C,IAAIQ,EACAmE,EAEA7B,EADAL,EAAQ,GAmBZ,OAhBI/Q,GACFiT,EAAW,CAACriB,EAAS,cAAeyd,EAAa,YAAYrgB,KAAK,IAClEojB,EAAO/f,KAAKE,UAAUc,KAEtByc,EAAOpe,EAAMO,gBAAgBI,KAAKE,UAAUc,IAC5C4gB,EAAW,CAACriB,EAAS,cAAeyd,EAAa,aAAcS,GAAM9gB,KAAK,KAExEsgB,IACFyC,EAAQ,KAAOzC,GAEbwB,IACFiB,EAAQA,GAASA,EAAQ,IAAM,IAAM,oBAEvCkC,EAAWA,GAAYlC,EAAQ,IAAM,IAAMA,EAC3CvV,EAAO6Q,MAAMzQ,GAASe,aAAasW,IAE5BD,EAAUC,EAAU7B,EAC/B,EAES0B,CACT,ECrFA,IAAAW,GAtBA,SAAkBC,EAAgBC,GAChC,MAAMpF,EAAQ,CAAA,EACd,IAAIlc,EAiBJ,OAfAkc,EAAMqF,WAAa,SAASjd,GAC1BtE,EAAU3B,EAAM0B,gBAAgBuE,GAC5BtE,GAAWshB,GACbA,EAASjjB,EAAMS,MAAMkB,GAE3B,EAEEkc,EAAMG,WAAa,WACjB,OAAOrc,EAAU3B,EAAMS,MAAMkB,GAAW,IAC5C,EAEMqhB,GACFnF,EAAMqF,WAAWF,GAGZnF,CACT,ECtBA,MAAQhL,GAAIC,IAAWlE,IACjBuG,gBAAEA,IAAoBpC,GA6F5B,IAAAoQ,GA7EA,SAAmCC,GACjC,SAASC,EAAsBxhB,GAC7B,OAAIA,SAAgD,SAATA,EAZ3B,iBAeT,kBAAkBA,GAC1B,CAkBD,SAASyhB,EAAyBzhB,EAAMF,GAKtC,OAAoB,OAAhBA,EAAQnD,UAAgCsD,IAAhBH,EAAQnD,KAClCmD,EAAQnD,IAAMmD,EAAQnD,IAAIU,WACnB2D,QAAQ0Q,QAAQ5R,IAGrBA,EAAQ0X,UA1Bd,SAA6BxX,GAC3B,OAAOuhB,EAAkBlF,IAAImF,EAAsBxhB,GACpD,CA2BU0hB,CAAoB1hB,GAAMc,MAAK6gB,IACpC,GAAIA,EAEF,OADA7hB,EAAQnD,IAAMglB,EACP7hB,EACF,CACL,MAAM2O,EAAKwC,KAEX,OADAnR,EAAQnD,IAAM8R,EA/BtB,SAA6BA,EAAIzO,GAC/B,OAAOuhB,EAAkBle,IAAIme,EAAsBxhB,GAAOyO,EAC3D,CA8BcmT,CAAoBnT,EAAIzO,GAAMc,MAAK,IAAMhB,GACjD,KAGIkB,QAAQC,OAAO,IAAIvH,EAAOJ,mBAAmB+P,GAASuC,kBAGhE,CAQD3S,KAAK4oB,eAAiB/hB,IACpB,IAAKA,EACH,OAAOkB,QAAQC,OAAO,IAAIvH,EAAOJ,mBAAmB+P,GAASoD,wBAG/D,MAAMqV,EAAmB3jB,EAAMS,MAAMkB,GAErC,GAAqB,UAAjBA,EAAQE,KAAkB,CAC5B,MAAMgT,EAAQM,GAAgBwO,GAE9B,OAAO9gB,QAAQ+gB,IAAI/O,EAAMrC,KAAI3Q,GAAQyhB,EAAyBzhB,EAAM8hB,EAAiB9hB,OAASc,MAC5F,IAAMghB,GAET,CACD,OAAOL,EAAyB3hB,EAAQE,KAAM8hB,GAElD,EC5FA,MAAQ9Q,GAAIC,IAAWlE,IAKjBC,eAAEA,IAAmBkE,IAErB9S,cAAEA,IAAkB+e,EA+P1B,IAAA6E,GAAiB,CACfC,aA9PF,SAAsBC,GACpB,MAAM1hB,EAAM,CACV2hB,aAAclR,MAKhB,OAHIiR,IACF1hB,EAAI4hB,aAAeF,EAAOjnB,OAAS,EAAIinB,EAAO1jB,UAAU0jB,EAAOjnB,OAAS,GAAKinB,GAExE1hB,CACT,EAuPE6hB,uBAnPF,SAAgCC,GAC9B,IAAIC,EAAeC,EAAeC,EAAmBC,EAErD,SAASC,EAAM1Q,GACbsQ,EAAgBtQ,EAChBuQ,EAAgB,EAChBC,EAAoB,EACpBC,EAAc,EACf,CAID,OAFAC,EAAML,GAEC,CACLM,SAAU,KAAO,CACfL,gBACAC,gBACAC,oBACAC,gBAGFG,SAAUC,IACRP,EAAgBO,EAAMP,cACtBC,EAAgBM,EAAMN,eAAiB,EACvCC,EAAoBK,EAAML,mBAAqB,EAC/CC,EAAcI,EAAMJ,aAAe,IAErCvJ,uBAAwB,KACtBqJ,KAEF3I,qBAAsBhU,IACpB4c,EAAoB5c,GAEtBuZ,iBAAkB,CAAC2D,EAAW9H,EAAQ+H,KACpC,MAAMlE,EAAO,CAAEiE,YAAW9H,SAAQ+H,kBAClCN,EAAYpnB,KAAKwjB,IAEnB6D,QAEJ,EA8MEM,mBAjMF,SACE1jB,EACAgiB,EACA2B,EACA5K,EACAlH,EACA1B,EACAyS,GAEA,MAAMgB,IAAiB5jB,EAAS6jB,2BAC1BC,EAAkB,MAAQjS,EAAgB,gBAC1CkS,EAAsBllB,GAAcsR,EAAOvC,UAAW,sBAAwBiE,GAC9EmS,EAAmB7T,EAAOzB,4BAC1B5O,EAAM6jB,EAEZ,IACIM,EACAC,EAFAC,IAAqBhU,EAAOrC,UAGhC,MAAMsW,EAAU,CAAA,EAEhB,SAASC,IACP,MAAO,CACLC,IAAKC,IACL3U,cAAe4U,IACfxkB,SAAUA,EAASykB,uBAEtB,CAGD,SAASC,EAAoBra,GAC3B8F,EAAOzG,QAAUyG,EAAOzG,OAAO6Q,MAAMzQ,GAASM,4BAA4BC,IAC1E0O,EACGlL,WAAWxD,EAAO0Z,GAAqB,GACvCxiB,MAAK,SACLsR,OAAM,QACV,CA4DD,SAAS8R,IACPD,EAhBF,WACE,MAAME,GAAc,IAAIpnB,MAAOE,UAC/B,IAAIuD,EAAM,CACRR,KAAMmjB,EAAe,sBAAwB,aAC7C1U,GAAI0T,EACJ7N,aAAc6P,KACX9kB,EAAIujB,YAMT,OAJIO,IACF3iB,EAAM,IAAKA,KAAQojB,MAErBvkB,EAAIsjB,MAAMwB,GACH3jB,CACR,CAGqB4jB,IACpBX,EAAgB7jB,WAAWskB,EAAmBX,GAC9CC,GAAgB,IAAIzmB,MAAOE,UACvBkmB,GAvCN,WACE,GAAI5B,EAAkBrE,YAAa,CACjC,MAAM4F,EAAQ,IAAKzjB,EAAIujB,YACvBrB,EAAkBle,IAAIggB,EAAiBvkB,KAAKE,UAAU8jB,GACvD,CACF,CAmCGuB,EAEH,CAED,SAASP,IACP,MAAMQ,EAAU,IAAK/kB,EAASglB,mBAO9B,OANI7U,EAAOvB,cACTmW,EAAQnW,YAAcuB,EAAOvB,aAE3BuB,EAAOtB,iBACTkW,EAAQlW,eAAiBsB,EAAOtB,gBAE3BkW,CACR,CAED,SAASP,IA6BP,MA5BmB,CACjBS,cAAe9U,EAAOrR,UAAY2O,GAAe3O,QAAQ4O,QACzDwX,gBAAiB/U,EAAOxC,YAAcF,GAAeE,UAAUD,QAC/DyX,gBAAiBhV,EAAOvC,YAAcH,GAAeG,UAAUF,QAC/D0X,eAAgBjV,EAAO/B,cACvBiX,0BAA2BlV,EAAO9B,cAClCiX,oBAAqBnV,EAAOrD,qBAC5ByY,mBAAoBpB,EACpB5V,uBAAwB4B,EAAO5B,qBAC/BiX,kCAAmCrV,EAAOzB,4BAE1C+W,kBAAmBtV,EAAOqM,KAC1BkJ,gBAAiBvV,EAAO1B,UACxBkX,oBAAqBxV,EAAOyV,WAC5B3X,6BAA8BkC,EAAOlC,2BAexC,CA0CD,OArCAmW,EAAQ9nB,MAAQ,KACVsnB,EAlHN,SAAwBtiB,GACtB,IAAK0gB,EAAkBrE,YACrB,OAAOrc,GAAS,GAElB0gB,EACGlF,IAAIgH,GACJviB,MAAKyb,IACJ,GAAIA,EACF,IACE,MAAMuG,EAAQhkB,KAAKC,MAAMwd,GACzBld,EAAIwjB,SAASC,GACbU,EAAgBV,EAAMP,aACvB,CAAC,MAAO1b,GAER,CAEHhG,GAAS,MAEVuR,OAAM,KACLvR,GAAS,KAEd,CA8FGukB,EAAeC,IACb,GAAIA,EAAuB,CACzB,MAAMC,GAAiB9B,GAAiB,GAAKD,EACvCgC,GAAU,IAAIxoB,MAAOE,UACvBsoB,GAAWD,EACbpB,IAEAT,EAAgB7jB,WAAWskB,EAAmBoB,EAAgBC,EAE1E,MAI2E,IAA7Dpf,KAAKM,MAvJoB,EAuJdN,KAAKkB,UAClB6c,IAEAT,EAAgB7jB,WAAWskB,EAAmBX,OAKpDU,EAvGK,CACLjkB,KAAM,kBACNyO,GAAI0T,EACJ7N,aAAcjV,EAAIujB,WAAWL,iBAC1BqB,MAoGHH,EAAgB7jB,WAAWskB,EAAmBX,KAIlDI,EAAQ1J,KAAO,KACbwJ,GAAiBvJ,aAAauJ,IAIhCE,EAAQ6B,aAAeC,IACrB/B,EAAmB+B,GAGd9B,CACT,GClOA,IAAA+B,GA5BA,SAAuBC,EAAW1c,GAChC,IAAI2c,GAAc,EAClB,MAAMC,EAAU,CACdza,KAAMua,EAAUva,KAChBzS,KAAMgtB,EAAUhtB,KAChBmtB,YAAaH,EAAUG,YAGzBD,OAAiB,IAAIvkB,KACnB,IACEqkB,EAAU/G,UAAUtd,EAC1B,CAAM,MAMKskB,IACHA,GAAc,EACd3c,EAAOiG,KAAK7F,GAAS8B,qBAAqB0a,EAAQza,KAAMya,EAAQltB,OAGnE,IAGH,OAAOktB,CACT,EC9BA,MAAMnmB,WAAEA,IAAeqN,EAKjBgZ,GAAiB,CACrBC,SAAU,YACVC,mBAAoB,uBACpBC,kBAAmB,sBACnBC,sBAAuB,2BAGzBhqB,OAAOiqB,OAAOL,IA8Id,IAAAM,GAAiB,CAAAN,eAAEA,GAAcO,iBAzIjC,SAA0B3X,EAAY1F,GACpC,MAAM0a,EAAU,CAAA,EASV4C,EAAmB,CACvB,CAACR,GAAeC,UAAW,GAC3B,CAACD,GAAeE,oBAAqB,GACrC,CAACF,GAAeG,mBAAoB,GACpC,CAACH,GAAeI,uBAAwB,IAOpCK,EAA8B,CAClC,CAACT,GAAeC,UAAW,GAC3B,CAACD,GAAeE,oBAAqB,GACrC,CAACF,GAAeG,mBAAoB,GACpC,CAACH,GAAeI,uBAAwB,IAGpCM,EAAiB9X,GAAcA,EAAWgC,KAAIgV,GAAae,GAAcf,EAAW1c,KA0G1F,OAxGAwd,GACEA,EAAevmB,SAAQymB,IAEjBxqB,OAAOhD,UAAUmD,eAAegB,KAAKipB,EAAkBI,EAAcvb,QAAUub,EAAcb,YAC/FS,EAAiBI,EAAcvb,MAAM9P,KAAKqrB,GAE1CxqB,OAAOhD,UAAUmD,eAAegB,KAAKkpB,EAA6BG,EAAcvb,OAChFub,EAAcb,YAEdU,EAA4BG,EAAcvb,MAAM9P,KAAKqrB,GAErD1d,EAAOiG,KAAK7F,GAASmC,iBAAiBmb,EAAcvb,KAAMub,EAAchuB,UAU9EgrB,EAAQiD,aAAexb,GACpBmb,EAAiBnb,IAASmb,EAAiBnb,GAAMnQ,QACjDurB,EAA4Bpb,IAASob,EAA4Bpb,GAAMnQ,OAW1E0oB,EAAQkD,WAAa,CAACC,EAASC,EAAQjnB,KACrC,MAAMsL,EAAO2a,GAAeC,SACxBQ,EAA4Bpb,GAAMnQ,QACpCurB,EAA4Bpb,GAAMlL,SAAQylB,GAAaA,EAAU/G,OAAOkI,EAASC,EAAQjnB,KAEvFymB,EAAiBnb,GAAMnQ,QACzByE,IAAW,KACT6mB,EAAiBnb,GAAMlL,SAAQylB,GAAaA,EAAU/G,OAAOkI,EAASC,EAAQjnB,SAYpF6jB,EAAQqD,QAAUzmB,IAChB,MAAM6K,EAAO2a,GAAeE,mBACxBO,EAA4Bpb,GAAMnQ,QACpCurB,EAA4Bpb,GAAMlL,SAAQylB,GAAaA,EAAU/G,OAAOre,KAEtEgmB,EAAiBnb,GAAMnQ,QACzByE,IAAW,KACT6mB,EAAiBnb,GAAMlL,SAAQylB,GAAaA,EAAU/G,OAAOre,SAanEojB,EAAQsD,cAAgB,CAACH,EAASnS,KAChC,MAAMvJ,EAAO2a,GAAeG,kBACxBM,EAA4Bpb,GAAMnQ,QACpCurB,EAA4Bpb,GAAMlL,SAAQylB,GAAaA,EAAU/G,OAAOkI,EAASnS,KAE/E4R,EAAiBnb,GAAMnQ,QACzByE,IAAW,KACT6mB,EAAiBnb,GAAMlL,SAAQylB,GAAaA,EAAU/G,OAAOkI,EAASnS,SAY5EgP,EAAQuD,kBAAoBpnB,IAC1B,MAAMsL,EAAO2a,GAAeI,sBACxBK,EAA4Bpb,GAAMnQ,QACpCurB,EAA4Bpb,GAAMlL,SAAQylB,GAAaA,EAAU/G,OAAO9e,KAEtEymB,EAAiBnb,GAAMnQ,QACzByE,IAAW,KACT6mB,EAAiBnb,GAAMlL,SAAQylB,GAAaA,EAAU/G,OAAO9e,SAK5D6jB,CACT,GC1JA,MAAM/pB,eAAEA,IAAmBmT,EAgB3B,IAAAoa,GARA,SAAsBljB,EAAGmjB,GACvB,OAAO,IAAIpmB,SAAQ,CAACqmB,EAAMpmB,KACxBrB,YAAW,KAETqB,EAAO,IAAIrH,GADD,GAAGwtB,qBAA4BnjB,iBAEpC,IAAJA,KAEP,ECfA,MAAMqjB,GAAoB,eAgB1B,SAASC,GAAgBte,EAAQ2V,EAAQ4I,EAAUC,EAAOC,GACxD,IACE,OAAOD,GACR,CAAC,MAAOnkB,GAEP,OADA2F,GAAQlI,MAAM,gCAAgC6d,cAAmB4I,YAAmBlkB,KAC7EokB,CACR,CACH,CAQA,SAASC,GAAY1e,EAAQ2e,GAC3B,IACE,OAAOA,EAAKC,cAAclvB,MAAQ2uB,EACtC,CAAI,MAEA,OADAre,EAAOlI,MAAM,wEACNumB,EACR,CACH,CAmNA,IAAAQ,GAxFA,SAA0B7e,EAAQ8e,GAEhC,MAAMC,EAAgBD,EAAe,IAAIA,GAAgB,GA8EzD,MAAO,CACLE,eArEF,SAAwBtrB,EAAKmD,EAASooB,EAActJ,GAClD,GAA6B,IAAzBoJ,EAAc/sB,OAChB,OAAO2jB,IAET,MAAMhQ,EAAQ,IAAIoZ,GAEZG,EAAc,CAClBrB,QAASnqB,EACTmD,UACAooB,gBAIIE,EA3IV,SAAiCnf,EAAQ2F,EAAOuZ,GAC9C,OAAOvZ,EAAM+B,KAAIiX,GACfL,GACEte,EAjD+B,mBAmD/B0e,GAAY1e,EAAQ2e,IACpB,IAAMA,GAAMS,mBAAmBF,EAAa,CAAA,IAAO,CAAE,GACrD,CAAE,IAGR,CAiIqBG,CAAwBrf,EAAQ2F,EAAOuZ,GAClD/mB,EAASwd,IAEf,OAzHJ,SAAgC3V,EAAQ2F,EAAOuZ,EAAaI,EAAannB,GAGvE,IAAK,IAAIonB,EAAY5Z,EAAM3T,OAAS,EAAGutB,GAAa,EAAGA,GAAa,EAAG,CACrE,MAAMZ,EAAOhZ,EAAM4Z,GACbjM,EAAOgM,EAAYC,GACzBjB,GACEte,EAzE8B,kBA2E9B0e,GAAY1e,EAAQ2e,IACpB,IAAMA,GAAMa,kBAAkBN,EAAa5L,EAAMnb,IAAW,CAAE,GAC9D,CAAE,EAEL,CACH,CA0GIsnB,CAAuBzf,EAAQ2F,EAAOuZ,EAAaC,EAAUhnB,GACtDA,CACR,EAqDCunB,SA3CF,SAAkB7oB,EAAS8oB,GACzB,MAAMha,EAAQ,IAAIoZ,GAEZG,EAAc,CAClBroB,UACA8oB,WAGIR,EArHV,SAA+Bnf,EAAQ2F,EAAOuZ,GAC5C,OAAOvZ,EAAM+B,KAAIiX,GACfL,GACEte,EA3F6B,iBA6F7B0e,GAAY1e,EAAQ2e,IACpB,IAAMA,GAAMiB,iBAAiBV,EAAa,CAAA,IAAO,CAAE,GACnD,CAAE,IAGR,CA2GqBW,CAAsB7f,EAAQ2F,EAAOuZ,GAKtD,OAAO/mB,KArGX,SAA8B6H,EAAQ2F,EAAOuZ,EAAaI,EAAannB,GAGrE,IAAK,IAAIonB,EAAY5Z,EAAM3T,OAAS,EAAGutB,GAAa,EAAGA,GAAa,EAAG,CACrE,MAAMZ,EAAOhZ,EAAM4Z,GACbjM,EAAOgM,EAAYC,GACzBjB,GACEte,EAnH4B,gBAqH5B0e,GAAY1e,EAAQ2e,IACpB,IAAMA,GAAMmB,gBAAgBZ,EAAa5L,EAAMnb,IAAW,CAAE,GAC5D,CAAE,EAEL,CACH,CAwFM4nB,CAAqB/f,EAAQ2F,EAAOuZ,EAAaC,EAAUhnB,GAE9D,EA4BC6nB,QArBF,SAAiBrB,GAEfI,EAAc1sB,KAAKssB,EACpB,EAmBCsB,WAZF,SAAoBf,GAClB,GAA6B,IAAzBH,EAAc/sB,OAChB,OAEF,MAAM2T,EAAQ,IAAIoZ,IAtGtB,SAA2B/e,EAAQ2F,EAAOuZ,GAGxC,IAAK,IAAIK,EAAY5Z,EAAM3T,OAAS,EAAGutB,GAAa,EAAGA,GAAa,EAAG,CACrE,MAAMZ,EAAOhZ,EAAM4Z,GACnBjB,GACEte,EAxIyB,aA0IzB0e,GAAY1e,EAAQ2e,IACpB,IAAMA,GAAMsB,aAAaf,SACzBloB,EAEH,CACH,CA0FIkpB,CAAkBlgB,EAAQ2F,EAAOuZ,EAClC,EAQH,ECvPA,MAAMiB,GAAsB,iBAQ5B,SAASC,GAAcpgB,EAAQqgB,GAC7B,IACE,OAAOA,EAAOzB,cAAclvB,MAAQywB,EACrC,CAAC,MAAOroB,GAEP,OADAkI,EAAOlI,MAAM,4EACNqoB,EACR,CACH,CAyFA,IAAAva,GAAiB,CACjB0a,eAjFA,SAAwBtgB,EAAQugB,EAAqB3a,GACnD,MAAMD,EAAQ,GAad,OAZAC,EAAQ3O,SAAQopB,IACd,IACE,MAAMG,EAAcH,EAAOI,WAAWF,QAClBvpB,IAAhBwpB,EACFxgB,EAAOlI,MAAM,UAAUsoB,GAAcpgB,EAAQqgB,wCACpCG,GAAeA,EAAYxuB,OAAS,GAC7C2T,EAAMtT,QAAQmuB,EAEjB,CAAC,MAAO1oB,GACPkI,EAAOlI,MAAM,6CAA6CsoB,GAAcpgB,EAAQqgB,2BACjF,KAEI1a,CACT,EAmEA+a,gBA1DA,SAAyB1gB,EAAQugB,EAAqBI,EAAQ/a,GAC5DA,EAAQ3O,SAAQopB,IACd,IACEA,EAAOO,SAASD,EAAQJ,EACzB,CAAC,MAAOzoB,GACPkI,EAAOlI,MAAM,uCAAuCsoB,GAAcpgB,EAAQqgB,MAC3E,IAEL,EAmDAQ,wBA1CA,SAAiCvqB,EAAUwqB,EAAK9iB,GAC9C,MAAM+iB,EAAoB,CAAA,EAEtBzqB,EAASE,YACXuqB,EAAkBrxB,KAAO4G,EAASE,WAEhCF,EAASC,UACXwqB,EAAkBxqB,QAAUD,EAASC,SAEnCyH,EAAQkH,cACV6b,EAAkB7b,YAAclH,EAAQkH,aAEtClH,EAAQmH,iBACV4b,EAAkB5b,eAAiBnH,EAAQmH,gBAG7C,MAAM6b,EAA4B,CAAA,EAE9BhjB,EAAQqH,cACNrH,EAAQqH,YAAY3V,OACtBsxB,EAA0BtxB,KAAOsO,EAAQqH,YAAY3V,MAEnDsO,EAAQqH,YAAY9O,UACtByqB,EAA0BzqB,QAAUyH,EAAQqH,YAAY9O,UAI5D,MAAM0qB,EAAoB,CACxBrG,IAAKmG,EACLG,aAAcJ,GAOhB,OAJI5tB,OAAOC,KAAK6tB,GAA2BhvB,OAAS,IAClDivB,EAAkB5b,YAAc2b,GAG3BC,CACT,GC1FA,MAAQniB,kBAAAA,IAAsBgF,IAIxB6F,aAAEA,GAAYO,eAAEA,IAAmBjC,IACnC6U,eAAEA,GAAcO,iBAAEA,IAAqBnJ,IAGvCoM,eAAEA,GAAcI,gBAAEA,GAAeG,wBAAEA,IAA4BM,GAC/DC,GAAc,SACdC,GAAsB,kBA22B5B,IAAAC,GAAiB,CACjBC,WAh2BA,SAAoBT,EAAKjqB,EAAS2qB,EAAkBlrB,EAAU8P,GAC5D,MAAMpG,EAsEN,WACE,GAAIwhB,GAAoBA,EAAiBxhB,OACvC,OAAOwhB,EAAiBxhB,OAE1B,OAAQoG,GAAmBA,EAAgBpG,QAAUoG,EAAgBpG,OAAOgE,SAAYlF,GAAkB,OAC3G,CA3Ec2iB,GACTtb,EAAUub,GAAa1hB,GACvB2hB,EAA6BC,GAA2Bzb,GACxDnI,EAAUkI,GAAcrN,SAAS2oB,EAAkBrb,EAASC,EAAiBpG,GAC7E6hB,EAAmBxE,GAAiBrf,EAAQ0H,WAAY1F,GACxDmE,EAAanG,EAAQmG,WAC3B,IAAI0O,EAAciO,EACdhO,EAAO9U,EAAQ8U,KACnB,MAAMlN,EAAU,IAAI5H,EAAQ4H,SAEtBqb,EAAoBJ,GAAwBvqB,EAAUwqB,EAAK9iB,GAE3DwiB,EAAcF,GAAetgB,EAAQihB,EAAmBrb,GAExDkc,EAAaC,GAAiB/hB,EAAQ,IAAIhC,EAAQ2H,SAAU6a,IAE5DlI,EAAoB0J,GAAkB1rB,EAAS2rB,aAAcjiB,GAE7DqP,EAAcC,GAAYhZ,EAAUuc,EAAa7U,GAEjDkkB,EAAqBlkB,EAAQmG,aAAenG,EAAQiH,iBACpDiU,EAAegJ,EAAqBC,GAAYnJ,aAAanG,GAAe,KAC5E1D,EAAyB+S,EAAqBC,GAAY/I,wBAAuB,IAAItlB,MAAOE,WAAa,KACzGouB,EAAqBF,EACvBC,GAAYnI,mBACV1jB,EACAgiB,EACAnJ,EACAE,EACAwD,EACA7U,EACAkb,GAEF,KAEE9E,EAASiO,GAAO/rB,EAAU0H,EAAS6U,EAAa1D,GAEhD7G,EACJtK,EAAQskB,gBACRC,GAAejsB,EAAU0H,EAAS6U,EAAa1D,EAAwBhJ,EAASkJ,GAE5EiI,EAAYkL,GAAUlsB,EAAU0H,EAAS6U,GAE/C,IACI4P,EACAC,EAEAC,EAJArrB,EAAQ,CAAA,EAGRsrB,EAAoB5kB,EAAQoG,UAE5Bye,GAAS,EACTC,GAAS,EACTC,GAAa,EAYjB,MAAM3d,EAAgBpH,EAAQoH,cAExB2N,EAAQiQ,GAAS,MAsGvB,SAA0BnsB,IAK1B,SAA2BA,GACzB,GAAIuO,EAEF,OAEEvO,GACFosB,EAAa,CACXlsB,KAAM,WACNF,UACAwU,cAAc,IAAIvX,MAAOE,WAG9B,EAhBCkvB,CAAkBrsB,GA1BdgrB,EAAiBlE,aAAab,GAAeI,wBAC/C2E,EAAiB5D,kBAAkBlL,EAAMG,aA2B5C,IAxGKiQ,EAA4B,IAAIC,GAA0B9K,GAC1D+K,EAAsB/K,EAAkBrE,YAC1CqP,GAAoBhL,EAAmBzF,EAAaC,EAAMC,GAC1D,KA0CJ,SAASkQ,EAAatiB,GACfkS,IAIDzN,GAAiBA,EAAc6d,cAAgB7d,EAAc6d,aAAatiB,KAIzEA,EAAM9J,SAOXksB,GAAa,GAnBN5e,GAAe2e,GAAWxsB,EAASitB,iBAsBxCvjB,EAAO6Q,MAAMzQ,GAASK,qBAAqBE,EAAM5J,OACjDuR,EAAO6H,QAAQxP,KAVXoiB,IACF/iB,EAAOiG,KAAK7F,GAASyB,uBACrBkhB,GAAa,IAUlB,CAcD,SAASS,EAA4BlQ,EAAMmQ,GACrC5B,EAAiBlE,aAAab,GAAeG,oBAC/C4E,EAAiB7D,cAAc1K,EAAK5f,IAAKgwB,EAAcD,GAE1D,CAED,SAASE,IACH9B,EAAiBlE,aAAab,GAAeE,qBAC/C6E,EAAiB9D,QACf7qB,OAAOkX,QAAQ9S,GACZoQ,KAAI,EAAEhU,EAAKyD,MAAY,CAAEzD,MAAKoqB,OAAQ4F,EAAcvsB,OACpDhB,QAAO,CAACC,EAAKwtB,KAEZxtB,EAAIwtB,EAAIlwB,KAAOkwB,EAAI9F,OACZ1nB,IACN,IAGV,CAqBD,SAASytB,EAAcnwB,EAAKoqB,EAAQmB,EAAc6E,GAChD,MAAMjtB,EAAUkc,EAAMG,aAChB5U,EAAM,IAAIxK,KAGV6M,EAAQ,CACZ5J,KAAM,UACNrD,IAAKA,EACLmD,UACAM,MANY2mB,EAASA,EAAO3mB,MAAQ,KAOpC4T,UAAW+S,EAASA,EAAOiG,eAAiB,KAC5C/f,QAASib,EACT5T,aAAc/M,EAAItK,WAEd0X,EAAOpU,EAAM5D,GACfgY,IACF/K,EAAMpK,QAAUmV,EAAKsY,YAActY,EAAKsY,YAActY,EAAKnV,QAC3DoK,EAAM2P,YAAc5E,EAAK4E,YACzB3P,EAAM4P,qBAAuB7E,EAAK6E,uBAE/BuT,GAAkBpY,GAAQA,EAAKuY,cAAiBnG,IACnDnd,EAAMujB,OAASpG,EAAOoG,QAGxBjB,EAAatiB,EACd,CAED,SAASwjB,EAActtB,GAGrB,OAAI8S,GAAa9S,GAAS,GACjBkB,QAAQ0Q,QAAQ5R,GAEhBkB,QAAQC,OAAO,IAAIvH,EAAOJ,mBAAmB+P,GAASuC,kBAEhE,CAyED,SAASyhB,EAAwB1wB,EAAKurB,EAAcoF,EAAWC,EAAsBC,EAAYC,GAC/F,IAAI1G,EACApS,EA4BJ,OA1BIpU,GAASpC,EAAMH,qBAAqBuC,EAAO5D,IAAQ4D,EAAM5D,KAAS4D,EAAM5D,GAAK+wB,SAC/E/Y,EAAOpU,EAAM5D,GACboqB,EAAS4F,EAAchY,GACJ,OAAfA,EAAKvU,YAAiCH,IAAf0U,EAAKvU,QAC9B2mB,EAAO3mB,MAAQ8nB,IAGjBnB,EAAS,CAAE3mB,MAAO8nB,EAAc8E,eAAgB,KAAMG,OAAQ,CAAEntB,KAAM,QAAS2tB,UAAW,mBAGxFL,IAGGE,GACH7Y,GAAMiZ,eAAe1tB,SAAQvD,IAC3B0wB,EAAwB1wB,OAAKsD,EAAWqtB,GAAW,GAAO,GAAO,MAGrER,EAAcnwB,EAAKoqB,EAAQmB,EAAcqF,KAItCC,GAAcC,GAzLrB,SAAkC9wB,EAAKoqB,GACjC+D,EAAiBlE,aAAab,GAAeC,WAC/C8E,EAAiBjE,WAAWlqB,EAAKoqB,EAAQ/K,EAAMG,aAElD,CAsLG0R,CAAyBlxB,EAAKoqB,GAGzBA,CACR,CAED,SAAS4F,EAAchY,GACrB,MAAO,CACLvU,MAAOuU,EAAKvU,MACZ4sB,oBAAmC/sB,IAAnB0U,EAAKX,UAA0B,KAAOW,EAAKX,UAC3DmZ,OAAQxY,EAAKwY,QAAU,KAK1B,CAqED,SAASW,IAEP,GADAnC,GAAe,GACV3P,EAAMG,aACT,OAEF,MAAM4R,EAAeC,IACnB,IACE,OAAOlvB,KAAKC,MAAMivB,EACnB,CAAC,MAAO1qB,GAEP,YADA8L,EAAQK,iBAAiB,IAAI/V,EAAOC,mBAAmB0P,GAASkC,eAEjE,GAEH8R,EAAOgC,QAAQrD,EAAMG,aAAcJ,EAAM,CACvCkS,KAAM,WACJhlB,EAAO6Q,MAAMzQ,GAASc,mBACtB,MAAM+jB,EAA2BlS,EAAMG,aACvCoE,EACGU,kBAAkBiN,EAA0BnS,GAC5Cjb,MAAKqtB,IAGAhwB,EAAMc,WAAWivB,EAA0BlS,EAAMG,eACnDiS,EAAgBD,GAAkB,CAAA,MAGrC/b,OAAM9O,IACL8L,EAAQK,iBAAiB,IAAI/V,EAAOD,iBAAiB4P,GAASuB,mBAAmBtH,OAEtF,EACD+qB,IAAK,SAASxnB,GACZ,MAAM0V,EAAOwR,EAAalnB,EAAE0V,MACvBA,IAGLtT,EAAO6Q,MAAMzQ,GAASiB,kBACtB8jB,EAAgB7R,GAGjB,EACD+R,MAAO,SAASznB,GACd,MAAM0V,EAAOwR,EAAalnB,EAAE0V,MAC5B,IAAKA,EACH,OAKF,MAAMgS,EAAUhuB,EAAMgc,EAAK5f,KAC3B,IAAK4xB,IAAYA,EAAQ/uB,UAAY+c,EAAK/c,SAAW+uB,EAAQ/uB,QAAU+c,EAAK/c,QAAS,CACnFyJ,EAAO6Q,MAAMzQ,GAASY,iBAAiBsS,EAAK5f,MAC5C,MAAM6xB,EAAO,CAAA,EACP9B,EAAUvuB,EAAMe,OAAO,CAAE,EAAEqd,UAC1BmQ,EAAa,IACpBnsB,EAAMgc,EAAK5f,KAAO+vB,EAClB,MAAM+B,EAAY9B,EAAcD,GAE9B8B,EAAKjS,EAAK5f,KADR4xB,EACe,CAAEG,SAAUH,EAAQnuB,MAAOuuB,QAASF,GAEpC,CAAEE,QAASF,GAE9BhC,EAA4BlQ,EAAMmQ,GAClCkC,EAAkBJ,EAC5B,MACUvlB,EAAO6Q,MAAMzQ,GAASa,wBAAwBqS,EAAK5f,KAEtD,EACDkyB,OAAQ,SAAShoB,GACf,MAAM0V,EAAOwR,EAAalnB,EAAE0V,MAC5B,GAAKA,EAGL,IAAKhc,EAAMgc,EAAK5f,MAAQ4D,EAAMgc,EAAK5f,KAAK6C,QAAU+c,EAAK/c,QAAS,CAC9DyJ,EAAO6Q,MAAMzQ,GAASU,kBAAkBwS,EAAK5f,MAC7C,MAAM6xB,EAAO,CAAA,EACTjuB,EAAMgc,EAAK5f,OAAS4D,EAAMgc,EAAK5f,KAAK+wB,UACtCc,EAAKjS,EAAK5f,KAAO,CAAE+xB,SAAUnuB,EAAMgc,EAAK5f,KAAKyD,QAE/CG,EAAMgc,EAAK5f,KAAO,CAAE6C,QAAS+c,EAAK/c,QAASkuB,SAAS,GACpDjB,EAA4BlQ,EAAMhc,EAAMgc,EAAK5f,MAC7CiyB,EAAkBJ,EAC5B,MACUvlB,EAAO6Q,MAAMzQ,GAASW,yBAAyBuS,EAAK5f,KAEvD,GAEJ,CAED,SAASmyB,IACHnD,IACFtO,EAAOmC,aACPmM,GAAe,EAElB,CAKD,SAASyC,EAAgBW,GACvB,MAAMC,EAAU,CAAA,EAEhB,IAAKD,EACH,OAAO/tB,QAAQ0Q,UAGjB,IAAK,MAAM/U,KAAO4D,EACZpC,EAAMH,qBAAqBuC,EAAO5D,IAAQ4D,EAAM5D,KAC9CoyB,EAASpyB,KAASwB,EAAMc,WAAW8vB,EAASpyB,GAAKyD,MAAOG,EAAM5D,GAAKyD,OACrE4uB,EAAQryB,GAAO,CAAE+xB,SAAUnuB,EAAM5D,GAAKyD,MAAOuuB,QAAShC,EAAcoC,EAASpyB,KACnEoyB,EAASpyB,KAAQoyB,EAASpyB,GAAK+wB,UACzCsB,EAAQryB,GAAO,CAAE+xB,SAAUnuB,EAAM5D,GAAKyD,SAI5C,IAAK,MAAMzD,KAAOoyB,EACZ5wB,EAAMH,qBAAqB+wB,EAAUpyB,IAAQoyB,EAASpyB,MAAU4D,EAAM5D,IAAQ4D,EAAM5D,GAAK+wB,WAC3FsB,EAAQryB,GAAO,CAAEgyB,QAAShC,EAAcoC,EAASpyB,MAQrD,OAJA4D,EAAQ,IAAKwuB,GAEbnC,IAEOgC,EAAkBI,GAAS5c,OAAM,QACzC,CAID,SAASwc,EAAkBI,GACzB,MAAM5yB,EAAOD,OAAOC,KAAK4yB,GAEzB,GAAI5yB,EAAKnB,OAAS,EAAG,CACnB,MAAMg0B,EAAoB,CAAA,EAC1B7yB,EAAK8D,SAAQvD,IACX,MAAMgyB,EAAUK,EAAQryB,GAAKgyB,QACvBvuB,EAAQuuB,EAAUA,EAAQvuB,WAAQH,EAClCyuB,EAAWM,EAAQryB,GAAK+xB,SAC9Btf,EAAQmL,KAAK8P,GAAc,IAAM1tB,EAAKyD,EAAOsuB,GAC7CO,EAAkBtyB,GAAOgyB,EAAU,CAAEA,QAASvuB,EAAOsuB,SAAUA,GAAa,CAAEA,SAAUA,MAG1Ftf,EAAQmL,KAAK8P,GAAa4E,GAC1B7f,EAAQmL,KAAK+P,GAAqB/pB,GAO7B0G,EAAQuG,4BAA+Ba,GAC1CjS,EAAK8D,SAAQvD,IACXmwB,EAAcnwB,EAAKqyB,EAAQryB,GAAKgyB,WAGrC,CAED,OAAIjD,GAAmBY,EACdA,EAAoB1P,UAAUrc,GAE9BS,QAAQ0Q,SAElB,CAwCD,SAASwd,IACP,MAAMC,EAAoBtD,GAAsBD,QAAkD3rB,IAAtB4rB,EACxEsD,IAAsBxD,EACxBmC,KACUqB,GAAqBxD,GAC/BmD,IAEEzD,GACFA,EAAmB7F,aAAa2J,EAEnC,CAED,SAASC,EAAiBxlB,GACxB,OAAOA,IAAUygB,IAAezgB,EAAMxH,OAAO,EAAGioB,KAA4BA,GAAc,GAC3F,CAgBD,GAdiC,iBAAtBpjB,EAAQ+G,WAA8D,iBAApC/G,EAAQ+G,UAAUqhB,gBACzD/C,EACFZ,GAAkB,EAElBziB,EAAOiG,KAAK7F,GAASyC,4BAIQ,iBAAtB7E,EAAQ+G,YAGjBzN,EA5iBF,SAAgCgc,GAI9B,MAAMngB,EAAOD,OAAOC,KAAKmgB,GACnB+S,EAAc,cACdC,EAAW,SACXC,EAAWjT,EAAK+S,IACjBE,GAAYpzB,EAAKnB,QACpBgO,EAAOiG,KAAK7F,GAASE,uBAEA,IAAnBgT,EAAKgT,IACPtmB,EAAOiG,KAAK7F,GAASC,oBAEvB,MAAM9I,EAAM,CAAA,EAYZ,OAXApE,EAAK8D,SAAQvD,IACX,GAAIA,IAAQ2yB,GAAe3yB,IAAQ4yB,EAAU,CAC3C,IAAI5a,EAAO,CAAEvU,MAAOmc,EAAK5f,IACrB6yB,GAAYA,EAAS7yB,GACvBgY,EAAOxW,EAAMe,OAAOyV,EAAM6a,EAAS7yB,IAEnCgY,EAAKnV,QAAU,EAEjBgB,EAAI7D,GAAOgY,CACZ,KAEInU,CACR,CAihBSivB,CAAuBxoB,EAAQ+G,YAGrCK,EAAe,CAKjB,MAAMqhB,EAAQrhB,EAAcshB,kBACxBD,EACFE,EAAsBF,GAEtBrhB,EAAc+L,GAAG,OAAQwV,GAE3BvhB,EAAc+L,GAAG,UAqFnB,SAAiCsV,GAC3BA,EAAM5vB,SACRkc,EAAMqF,WAAWqO,EAAM5vB,SAErB4vB,EAAMnvB,OACR6tB,EAAgBsB,EAAMnvB,MAEzB,GA3FH,MAIE,WACE,IAAKwpB,EACH,OAAO/oB,QAAQC,OAAO,IAAIvH,EAAOL,4BAA4BgQ,GAASsB,4BAExE,IAAIoe,EACJ,OAAOqD,EACJvK,eAAe/hB,GACfgB,KAAKssB,GACLtsB,MAAKhB,IACJipB,EAAgB5qB,EAAM+C,KAAK6pB,EAAWpC,SAAS7oB,OAASG,IACjDH,KAERgB,MAAK+uB,IACJ9G,IAAgB,CAAEjvB,OAAQ,cAC1BkiB,EAAMqF,WAAWwO,GACgB,iBAAtB5oB,EAAQ+G,UAEV8hB,KACEpE,EAaRY,EAAoBlQ,YAAYtb,MAAKivB,GACtCA,SACFxvB,EAAQ,CAAA,EACDggB,EACJU,kBAAkBjF,EAAMG,aAAcJ,GACtCjb,MAAKqtB,GAAkBC,EAAgBD,GAAkB,CAAE,KAC3DrtB,KAAKgvB,IACL1d,OAAM9O,IAEL0sB,GADgB,IAAIt2B,EAAOD,iBAAiB4P,GAASuB,mBAAmBtH,UAO5E/C,EAAQwvB,EACR5xB,EAAMuB,WAAWowB,IAEVvP,EACJU,kBAAkBjF,EAAMG,aAAcJ,GACtCjb,MAAKqtB,GAAkBC,EAAgBD,KACvC/b,OAAM9O,GAAO8L,EAAQK,iBAAiBnM,QAMtCid,EACJU,kBAAkBjF,EAAMG,aAAcJ,GACtCjb,MAAKqtB,IACJ5tB,EAAQ4tB,GAAkB,GAE1BvB,IAEAkD,QAED1d,OAAM9O,IACL/C,EAAQ,CAAA,EACRyvB,GAAiB1sB,SA7ClB8O,OAAM9O,IAEL,MADAylB,IAAgB,CAAEjvB,OAAQ,UACpBwJ,IAEX,EA/BC2sB,GAAa7d,MAAM4d,IA4ErB,SAASJ,EAAsBF,GAC7B5T,EAAc4T,EAAM5T,YACpBE,EAAMqF,WAAWqO,EAAM5vB,SACvBS,EAAQ,IAAKmvB,EAAMnvB,OACnBpC,EAAMuB,WAAWowB,GAClB,CAWD,SAASA,KACP7mB,EAAO6V,KAAKzV,GAASG,qBACrBsiB,GAAS,EACToD,IACAtE,EAA2BlP,eAC5B,CAED,SAASsU,GAAiB1sB,GACxBsnB,EAA2BjP,cAAcrY,EAC1C,CA8ED,MAAMsmB,GAAS,CACbsG,sBAnBF,SAA+BtH,OAAU3oB,GACvC,GAAI2oB,QAA2C,CAC7C,GAAuB,iBAAZA,EACT,OAvBN,SAA0CA,GACpCA,EAnyBqB,GAoyBvB3f,EAAOiG,KACL,qIAMJ,MAAMihB,EAAcvF,EAA2BtP,2BACzC8U,EAAiBC,GAAazH,EAAS,yBAE7C,OAAO5nB,QAAQsvB,KAAK,CAACF,EAAgBD,IAAc/d,OAAMvL,IAIvD,MAHIA,aAAanN,EAAOE,gBACtBqP,EAAOlI,MAAM,gCAAgC8F,KAEzCA,IAET,CAKY0pB,CAAiC3H,GAE1C3f,EAAOiG,KAAK,4EACb,CAKD,OAJAjG,EAAOiG,KACL,qIAGK0b,EAA2BtP,0BACnC,EAQCkV,eAAgB,IAAM5F,EAA2BnP,kBACjDkN,SAjmBF,SAAkB7oB,EAASwf,EAASmR,GAClC,GAAI1E,EACF,OAAO5tB,EAAMwC,oBAAoBK,QAAQ0Q,QAAQ,CAAE,GAAG+e,GAExD,GAAIpiB,EAGF,OADApF,EAAOiG,KAAK7F,GAAS6B,oBACd/M,EAAMwC,oBAAoBK,QAAQ0Q,QAAQvT,EAAMsC,iCAAiCF,IAASkwB,GAEnG,IAAI1H,EACJ,MAAM2H,EAAahF,GAAmBY,EAAsBA,EAAoB3P,aAAe3b,QAAQ0Q,UACvG,OAAOvT,EAAMwC,oBACX+vB,EACG5vB,MAAK,IAAMsrB,EAA0BvK,eAAe/hB,KACpDgB,KAAKssB,GACLtsB,MAAKhB,IACJipB,EAAgB5qB,EAAM+C,KAAK6pB,EAAWpC,SAAS7oB,OAASG,IACjDH,KAERgB,MAAK+uB,GACJtP,EACGU,kBAAkB4O,EAAkBvQ,GAEpCxe,MAAKqtB,IACJ,MAAMwC,EAAexyB,EAAMsC,iCAAiC0tB,GAG5D,OAFAnS,EAAMqF,WAAWwO,GACjB9T,EAAOuD,EACH6O,EACKC,EAAgBD,GAAgBrtB,MAAK,IAAM6vB,IAE3CA,OAId7vB,MAAK6vB,IACJ5H,IAAgB,CAAEjvB,OAAQ,cACtB6xB,GACFmC,IAEK6C,KAERve,OAAM9O,IACLylB,IAAgB,CAAEjvB,OAAQ,UAC1BsV,EAAQK,iBAAiBnM,GAClBtC,QAAQC,OAAOqC,MAE1BmtB,EAEH,EAkjBCtU,WAhjBF,WACE,OAAOH,EAAMG,YACd,EA+iBCnI,UAziBF,SAAmBrX,EAAKurB,GACtB,MAAM9nB,MAAEA,GAAU2qB,EAAW9C,eAAetrB,EAAKqf,EAAMG,aAAc+L,GAAc,IACjFmF,EAAwB1wB,EAAKurB,GAAc,GAAM,GAAO,GAAO,KAEjE,OAAO9nB,CACR,EAqiBCwwB,gBAniBF,SAAyBj0B,EAAKurB,GAC5B,OAAO6C,EAAW9C,eAAetrB,EAAKqf,EAAMG,aAAc+L,GAAc,IACtEmF,EAAwB1wB,EAAKurB,GAAc,GAAM,GAAM,GAAO,IAEjE,EAgiBC2I,MAvdF,SAAel0B,EAAK4f,EAAMuU,GACxB,GAAmB,iBAARn0B,EAET,YADAyS,EAAQK,iBAAiB,IAAI/V,EAAOH,uBAAuB8P,GAASkD,sBAAsB5P,UAGxEsD,IAAhB6wB,GAAoD,iBAAhBA,GACtC7nB,EAAOiG,KAAK7F,GAASqC,0BAA0BolB,IAQ7CvxB,EAASwxB,oBAAsBxxB,EAASwxB,kBAAkBp0B,IAC5DsM,EAAOiG,KAAK7F,GAASkD,sBAAsB5P,IAG7C,MAAMmD,EAAUkc,EAAMG,aAChBtV,EAAI,CACR7G,KAAM,SACNrD,IAAKA,EACLmD,UACAuK,IAAK9K,EAASyxB,gBACd1c,cAAc,IAAIvX,MAAOE,WAEvB6C,GAAWA,EAAQ0X,YACrB3Q,EAAEoqB,YAA8BnxB,EA9BtB0X,UAAY,gBAAkB,QAiCtC+E,UACF1V,EAAE0V,KAAOA,GAEPuU,UACFjqB,EAAEiqB,YAAcA,GAElB5E,EAAarlB,GACbkkB,EAAW7B,WAAW,CAAEppB,UAASnD,MAAK4f,OAAMuU,eAC7C,EAkbC1W,GA5QF,SAAYxQ,EAAOyQ,EAASva,GACtBsvB,EAAiBxlB,IACnBgiB,GAA2B,EACvBE,GACFoD,IAEF9f,EAAQgL,GAAGxQ,EAAOyQ,EAASva,IAE3BsP,EAAQgL,MAAM9X,UAEjB,EAmQCgY,IAjQF,SAAa1Q,GAEX,GADAwF,EAAQkL,OAAOhY,WACX8sB,EAAiBxlB,GAAQ,CAC3B,IAAIsnB,GAAgB,EACpB9hB,EAAQqL,YAAYva,SAAQvD,IACtByyB,EAAiBzyB,IAAQyS,EAAQsL,sBAAsB/d,GAAO,IAChEu0B,GAAgB,MAGfA,IACHtF,GAA2B,EACvBD,QAAsC1rB,IAAtB4rB,GAClBiD,IAGL,CACF,EAkPCtJ,aAhPF,SAAsBkK,GACpB,MAAMyB,EAAqB,OAAVzB,OAAiBzvB,EAAYyvB,EAC1CyB,IAAatF,IACfA,EAAoBsF,EACpBjC,IAEH,EA2OCxV,MAnjBF,SAAe+W,GACb,OAAOtyB,EAAMwC,oBAAoByM,EAAamE,EAAOmI,QAAU1Y,QAAQ0Q,UAAW+e,EACnF,EAkjBCW,SAvfF,WACE,MAAMC,EAAU,CAAA,EAEhB,IAAK9wB,EACH,OAAO8wB,EAGT,IAAK,MAAM10B,KAAO4D,EACZpC,EAAMH,qBAAqBuC,EAAO5D,KAAS4D,EAAM5D,GAAK+wB,UACxD2D,EAAQ10B,GAAO0wB,EACb1wB,EACA,MACCsK,EAAQuG,4BACT,GACA,GACA,GACApN,OAIN,OAAOixB,CACR,EAmeCnS,MAhFF,SAAeuR,GACb,GAAI1E,EACF,OAAO5tB,EAAMwC,oBAAoBK,QAAQ0Q,UAAW+e,GAEtD,MAAMa,EAAc,KAClBvF,GAAS,EACTxrB,EAAQ,CAAA,GAEJ2f,EAAIlf,QAAQ0Q,UACf5Q,MAAK,KAKJ,GAJAguB,IACIzD,GACFA,EAAmBpR,OAEjB7M,EAEF,OADAmE,EAAO0I,OACA1I,EAAOmI,WAGjB5Y,KAAKwwB,GACLlf,MAAMkf,GACT,OAAOnzB,EAAMwC,oBAAoBuf,EAAGuQ,EACrC,EA2DCxH,QAlBF,SAAiBrB,GACfmD,EAAW9B,QAAQrB,EACpB,GAqBD,OAFA+B,GAAgB1gB,EAAQihB,EAAmBN,GAAQ/a,GAE5C,CACL+a,OAAQA,GACR3iB,QAASA,EACTmI,QAASA,EACT4M,MAAOA,EACP/S,OAAQA,EACRsX,UAAWA,EACX1kB,MAtGF,WACMuR,IACEie,GACFA,EAAmBxvB,QAErB0V,EAAO1V,QAEV,EAgGCqwB,aAAcA,EACdqF,iBAvEF,WAEE,OAAOhxB,CACR,EAqECixB,iBAAkB,IAAM1V,EACxB2V,wBAAyBnH,GAE7B,EAIAviB,kBAAEA,GACArO,SACA2P,YACAlL,QACAgV,kkCCv4BF,IAAQpL,GAAsBgF,GAAtBhF,kBAMR,OAJA,SAAqBd,GACnB,OAAOc,GAAiB2pB,GAAA,CAAGzpB,YAAaI,QAAQW,KAAQ/B,GAC1D,ECUA,IAAM0qB,GAAc,CAAE/wB,QAASI,QAAQ0Q,QAAQ,CAAE5X,OAAQ,IAAKkY,OAAQ,WAAF,OAAQ,IAAI,EAAE6M,KAAM,QAEzE,SAAS+S,GAAehT,EAAQvU,EAAKgG,EAASwO,EAAMgT,GACjE,GAAIA,IAjBN,WAGE,IAAMpyB,EAAYqyB,OAAOC,WAAaD,OAAOC,UAAUtyB,UACvD,GAAIA,EAAW,CACb,IAAMuyB,EAAcvyB,EAAUwP,MAAM,4BACpC,GAAI+iB,EAEF,OADgBpvB,SAASovB,EAAY,GAAI,IACxB,EAErB,CACA,OAAO,CACT,CAQSC,GACH,OAAON,GAKX,IAAMO,EAAM,IAAIJ,OAAOK,eAEvB,IAAK,IAAMx1B,KADXu1B,EAAIE,KAAKxT,EAAQvU,GAAMwnB,GACLxhB,GAAW,GACvBlU,OAAOhD,UAAUmD,eAAegB,KAAK+S,EAAS1T,IAChDu1B,EAAIG,iBAAiB11B,EAAK0T,EAAQ1T,IAGtC,GAAIk1B,EAAe,CACjB,IACEK,EAAII,KAAKzT,EACV,CAAC,MAAOhY,GACP,CAEF,OAAO8qB,EACT,CACE,IAAIY,EACErS,EAAI,IAAIlf,SAAQ,SAAC0Q,EAASzQ,GAC9BixB,EAAInT,iBAAiB,QAAQ,WACvBwT,GAGJ7gB,EAAQ,CACN5X,OAAQo4B,EAAIp4B,OACZkY,OAAQ,SAACrV,GAAG,OAAKu1B,EAAIM,kBAAkB71B,EAAI,EAC3CkiB,KAAMqT,EAAIO,cAEd,IACAP,EAAInT,iBAAiB,SAAS,WACxBwT,GAGJtxB,EAAO,IAAIlI,MACb,IACAm5B,EAAII,KAAKzT,EACX,IAKA,MAAO,CAAEje,QAASsf,EAAGc,OAJN,WACbuR,GAAY,EACZL,EAAIQ,SAIV,CCjEA,IAAcC,GAAGC,IAChB,GAAsB,iBAAXA,EACV,MAAM,IAAIpwB,UAAU,qBAKrB,OAAOowB,EACLj0B,QAAQ,sBAAuB,QAC/BA,QAAQ,KAAM,UCTV,SAASk0B,GAAaC,EAASC,EAAMC,EAAQjX,GAClD,IAGIkX,EACAC,EAHEC,IAD6B,cAAjBL,EAAQ9yB,MAAyC,UAAjB8yB,EAAQ9yB,OAAqB+b,EAAKvJ,SAAS,KAC5DugB,EAAOA,EAAKp0B,QAAQod,EAAM,KAAKpd,QAAQq0B,EAAQ,IAKhF,OAAQF,EAAQ9yB,MACd,IAAK,QACHkzB,EAAUH,EACVE,EAAQ,IAAI91B,OAAO,IAAMw1B,GAAmBG,EAAQzoB,KAAO,OAC3D,MACF,IAAK,YACH6oB,EAAUC,EACVF,EAAQ,IAAI91B,OAAO,IAAMw1B,GAAmBG,EAAQzoB,KAAO,OAC3D,MACF,IAAK,YACH6oB,EAAUC,EACVF,EAAQ,IAAI91B,OAAO,KAAOw1B,GAAmBG,EAAQt0B,WAAa,OAClE,MACF,IAAK,QACH00B,EAAUC,EACVF,EAAQ,IAAI91B,OAAO21B,EAAQM,SAC3B,MACF,QACE,OAAO,EAEX,OAAOH,EAAMjxB,KAAKkxB,EACpB,CAuBe,SAASG,GAAYC,EAAOC,GAMzC,IALA,IAAMC,EAAU,CAAA,EACZC,EAAa,KAEXC,EAAa,GAEVn5B,EAAI,EAAGA,EAAI+4B,EAAMr4B,OAAQV,IAIhC,IAHA,IAAMo5B,EAAOL,EAAM/4B,GACbq5B,EAAOD,EAAKC,MAAQ,GAEjBrtB,EAAI,EAAGA,EAAIqtB,EAAK34B,OAAQsL,IAC/B,GAAIssB,GAAae,EAAKrtB,GAAIurB,OAAO+B,SAASd,KAAMjB,OAAO+B,SAASb,OAAQlB,OAAO+B,SAAS9X,MAAO,CAC3E,aAAd4X,EAAK3zB,KACPuzB,EAAQ,WAAYI,IAEpBD,EAAWp4B,KAAKq4B,GAChBJ,EAAQ,iBAAkBI,IAE5B,KACF,CAmBJ,OAfID,EAAWz4B,OAAS,IACtBw4B,EAAa,SAAU7pB,GAErB,IADA,IAAM0pB,EA9CZ,SAA2B1pB,EAAO8pB,GAGhC,IAFA,IAAMI,EAAU,GAEPv5B,EAAI,EAAGA,EAAIm5B,EAAWz4B,OAAQV,IAKrC,IAJA,IAAIic,EAAS5M,EAAM4M,OACbmd,EAAOD,EAAWn5B,GAClBw5B,EAAWJ,EAAKI,SAChBC,EAAWC,SAASC,iBAAiBH,GACpCvd,GAAUwd,EAAS/4B,OAAS,GAAG,CACpC,IAAK,IAAIsL,EAAI,EAAGA,EAAIytB,EAAS/4B,OAAQsL,IAC/BiQ,IAAWwd,EAASztB,IACtButB,EAAQx4B,KAAKq4B,GAGjBnd,EAASA,EAAO2d,UAClB,CAGF,OAAOL,CACT,CA2BoBM,CAAkBxqB,EAAO8pB,GAC9Bn5B,EAAI,EAAGA,EAAI+4B,EAAMr4B,OAAQV,IAChCg5B,EAAQ,QAASD,EAAM/4B,KAI3B05B,SAASlV,iBAAiB,QAAS0U,IAGrCD,EAAQa,QAAU,WAChBJ,SAASK,oBAAoB,QAASb,IAGjCD,CACT,CCvFe,SAASe,GAAYC,EAAYC,GAC9C,IAAInB,EACAoB,EAQJ,SAASC,IACHD,GACFA,EAAYL,UAEVf,GAASA,EAAMr4B,SACjBy5B,EAAcrB,GAAYC,EAAOsB,GAErC,CAEA,SAASA,EAAc50B,EAAM2zB,GAC3B,IAAM7zB,EAAU00B,EAAWxY,MAAMG,aAC3BvS,EAAQ,CACZ5J,KAAMA,EACNrD,IAAKg3B,EAAKh3B,IACV4f,KAAM,KACNlS,IAAKynB,OAAO+B,SAASd,KACrBze,cAAc,IAAIvX,MAAOE,UACzB6C,QAASA,GAOX,MAJa,UAATE,IACF4J,EAAMmqB,SAAWJ,EAAKI,UAGjBS,EAAWtI,aAAatiB,EACjC,CAgDA,OAjBA4qB,EAAWjU,UACRE,UA5DM,cAAgB+T,EAAWhD,oBA6DjC1wB,MAAK,SAAC+zB,GACDA,GAAKA,EAAE55B,OAAS,IAElBy5B,EAAcrB,GADdC,EAAQuB,EACyBD,GAlCvC,SAAuBE,EAAUj0B,GAC/B,IACIk0B,EADAC,EAAclD,OAAO+B,SAASd,KAGlC,SAASkC,KACPF,EAAajD,OAAO+B,SAASd,QAEViC,IACjBA,EAAcD,EACdl0B,IAEJ,EAEA,SAASq0B,EAAKC,EAAIL,GAChBK,IACAv1B,YAAW,WACTs1B,EAAKC,EAAIL,EACV,GAAEA,EACL,CAEAI,CAAKD,EAAUH,GAEXhD,OAAOsD,SAAWtD,OAAOsD,QAAQC,UACnCvD,OAAO/S,iBAAiB,WAAYkW,GAEpCnD,OAAO/S,iBAAiB,aAAckW,EAE1C,CAQMK,CA1EwB,IA0EeX,IAEzCF,GACF,IACCriB,OAAM,SAAC9O,GACNkxB,EAAWplB,QAAQK,iBACjB,IAAI8lB,GAAcn8B,2BAAsDkK,GAAOA,EAAIzK,QAAWyK,EAAIzK,WAEpG47B,GACF,IA7EU,CAAA,CAgFd,CCpFA,IAAMe,GAAa,aACbnmB,GAAkB,CACtB8V,WAAY,CAAElY,SAAS,GACvB8O,KAAM,CAAE3Q,KAAM,UACdmgB,eAAgB,CAAEngB,KAAM,UACxBqqB,oBAAqB,CAAErqB,KAAM,YAC7BsqB,qBAAsB,CAAEzoB,SAAS,IAI5B,SAASud,GAAWT,EAAK3W,GAAoB,IAAdnM,EAAO3E,UAAArH,OAAA,QAAAgF,IAAAqC,UAAA,GAAAA,UAAA,GAAG,CAAA,EACxC/C,ECdO,SAA6B0H,GAC1C,IAgBI0uB,EAhBEn1B,EAAM,CACVgQ,oBAAqB,4BAGvBhQ,kBAAuB,GAGvB,GAAIsxB,OAAOK,eAAgB,CACzB,IAAMyD,EAAmB3uB,GAAWA,EAAQyuB,qBAC5Cl1B,EAAIiR,YAAc,SAACmN,EAAQvU,EAAKgG,EAASwO,GACvC,IAAMgX,EAAYr1B,EAAIs1B,kBAAoBF,EAE1C,OADAp1B,EAAIs1B,kBAAmB,EAChBlE,GAAehT,EAAQvU,EAAKgG,EAASwO,EAAMgX,GAEtD,CAGAr1B,EAAIu1B,eAAiB,WAKnB,YAHgB91B,IAAZ01B,IACFA,IAAU7D,OAAOK,gBAAiB,oBAAqB,IAAIL,OAAOK,gBAE7DwD,GAITn1B,EAAIw1B,iBAAmB,SAAC3rB,IACV,IAAIynB,OAAOmE,OACnB1L,IAAMlgB,GAGZ,IAgDI6rB,EAhDET,EAAsBxuB,GAAWA,EAAQwuB,oBAC/Cj1B,EAAIwwB,cAAgB,WAAA,OAAOyE,EAAsBA,EAAoB3D,OAAO+B,SAASd,MAAQjB,OAAO+B,SAASd,MAE7GvyB,EAAIgsB,aAAe,WACjB,IAAI7X,EAQJ,OAAgB,KANdA,EADEmd,OAAOC,gBAA6C9xB,IAAhC6xB,OAAOC,UAAUoE,WAChCrE,OAAOC,UAAUoE,WACfrE,OAAOC,gBAA+C9xB,IAAlC6xB,OAAOC,UAAUqE,aACvCtE,OAAOC,UAAUqE,aAEjBtE,OAAOqE,cAEc,IAATxhB,GAA0B,MAATA,GAAyB,QAATA,GAGxD,IACMmd,OAAO5G,eACT1qB,EAAI0qB,aAAe,CACjB7O,IAAK,SAAC1f,GAAG,OACP,IAAIqE,SAAQ,SAAC0Q,GACXA,EAAQogB,OAAO5G,aAAamL,QAAQ15B,GACtC,GAAE,EACJ0G,IAAK,SAAC1G,EAAKyD,GAAK,OACd,IAAIY,SAAQ,SAAC0Q,GACXogB,OAAO5G,aAAaoL,QAAQ35B,EAAKyD,GACjCsR,GACF,GAAE,EACJmL,MAAO,SAAClgB,GAAG,OACT,IAAIqE,SAAQ,SAAC0Q,GACXogB,OAAO5G,aAAaqL,WAAW55B,GAC/B+U,GACF,GAAE,GAGT,CAAC,MAAO7K,GAGPrG,EAAI0qB,aAAe,IACrB,CA0BA,GAfkBjkB,GAAWA,EAAQwG,WAGG,mBAA/BqkB,OAAO0E,qBACd1E,OAAO0E,oBAAoBC,kBAC3B3E,OAAO0E,oBAAoBC,iBAAiB7X,QAE5Cpe,EAAIme,yBAA0B,EAC9BuX,EAAyBpE,OAAO0E,sBAEhCh2B,EAAIme,yBAA0B,EAC9BuX,EAAyBpE,OAAO4E,aAI9B5E,OAAO4E,YAAa,CACtB,IAAMC,EAAgB,IAEtBn2B,EAAIke,mBAAqB,SAACrU,EAAKpD,GAQ7B,IAMM2vB,EAASlF,GAAAA,GAAQmF,CAAAA,EANA,CACrBC,iBAAkBH,EAClBI,cAAeJ,EACfK,oBAAoB,IAGoB/vB,GAE1C,OAAO,IAAIivB,EAAuB7rB,EAAKusB,IAGzCp2B,EAAIkf,oBAAsB,SAACjM,GAAE,OAC3BA,EAAGwjB,aAAenF,OAAO4E,YAAYQ,MAAQzjB,EAAGwjB,aAAenF,OAAO4E,YAAYS,UAAU,CAChG,CAgBA,OAdA32B,EAAIf,UAAY,WAChBe,EAAIhB,QAAU,QAEdgB,EAAI+jB,kBAAoB,CACtB5rB,KAAM,gBACN6G,QAAS,SAGXgB,EAAIwjB,uBAAyB,CAC3BrrB,KAAM,MAGR6H,EAAI4iB,4BAA6B,EAE1B5iB,CACT,CD3HmB42B,CAAgBnwB,GAC3ButB,EAAae,GAAkBxL,EAAK3W,EAAMnM,EAAS1H,EAAU8P,IAE7Dua,EAAS4K,EAAW5K,OACpByN,EAAmB7C,EAAWvtB,QAC9BmI,EAAUolB,EAAWplB,QAErBkoB,EAAe,IAAIt2B,SAAQ,SAAC0Q,GAChC,IAAM6lB,EAAUnoB,EAAQgL,GAAGob,IAAY,WACrCpmB,EAAQkL,IAAIkb,GAAY+B,GACxB7lB,GACF,GACF,IACAkY,EAAO4N,oBAAsB,WAAA,OAAMF,CAAY,EAE3CD,EAAiBlS,WACnBoP,GAAYC,GAAY,WAAA,OAAMplB,EAAQmL,KAAKib,OAI3CpmB,EAAQmL,KAAKib,IAGa,aAAxBvB,SAASgD,WACXnF,OAAO/S,iBAAiB,OAAQyV,EAAW34B,OAE3C24B,EAAW34B,QAGb,IAAMg6B,EAAY,WAIhBt2B,EAASu2B,kBAAmB,EAC5BlM,EAAOlQ,QAAQtH,OAAM,WAAQ,IAC7B7S,EAASu2B,kBAAmB,GAsB9B,OAHA7B,SAASlV,iBAAiB,oBANK,WACI,WAA7BkV,SAASwD,iBACX5B,OAKJ/D,OAAO/S,iBAAiB,WAAY8W,GAE7BjM,CACT,CAEa8N,IAAAA,GAAcC,GAIdn4B,GAAU,QAOR,IAAAo4B,GAAA,CAAEpN,WALjB,SAA8BT,EAAK3W,GAAoB,IAAdnM,EAAO3E,UAAArH,OAAA,QAAAgF,IAAAqC,UAAA,GAAAA,UAAA,GAAG,CAAA,EAEjD,OADA+F,SAAWA,QAAQ6G,MAAQ7G,QAAQ6G,KAAKqmB,GAAgBhrB,WAAW,iBAAkB,0BAC9EigB,GAAWT,EAAK3W,EAAMnM,EAC/B,EAEmDzH,QAAAA,2CAThB+1B"}