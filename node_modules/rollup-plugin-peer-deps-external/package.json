{"name": "rollup-plugin-peer-deps-external", "version": "2.2.4", "description": "Rollup plugin to automatically add a library's peerDependencies to its bundle's external config.", "repository": "pmowrer/rollup-plugin-peer-deps-external", "keywords": ["rollup", "plugin", "rollup-plugin", "peerDependencies", "external", "optimize", "exclude"], "files": ["src", "dist", "README.md", "LICENSE"], "scripts": {"clear": "rimraf ./dist", "build": "rollup -c", "format": "prettier --write --single-quote --trailing-comma es5", "format:all": "yarn format \"src/**/*.js\"", "prepublishOnly": "yarn test && yarn build", "test": "jest"}, "main": "./dist/rollup-plugin-peer-deps-external.js", "module": "./dist/rollup-plugin-peer-deps-external.module.js", "license": "MIT", "peerDependencies": {"rollup": "*"}, "devDependencies": {"@babel/core": "^7.8.4", "@babel/preset-env": "^7.8.4", "@rollup/plugin-babel": "^5.0.2", "@rollup/plugin-node-resolve": "^8.0.0", "husky": "^4.2.1", "jest": "^25.1.0", "lint-staged": "^10.0.7", "lodash-es": "^4.17.15", "prettier": "^1.19.1", "ramda": "^0.26.1", "rimraf": "^3.0.1", "rollup": "^2.13.1", "semantic-release": "^17.0.2", "semantic-release-github-pr": "^6.0.0"}, "jest": {"transformIgnorePatterns": ["<rootDir>/node_modules/(?!lodash-es/)"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.js": ["yarn format"]}}