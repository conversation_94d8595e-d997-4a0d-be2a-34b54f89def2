{"name": "launchdarkly-react-client-sdk", "version": "3.8.1", "description": "LaunchDarkly SDK for React", "author": "LaunchDarkly <<EMAIL>>", "license": "Apache-2.0", "keywords": ["launchdarkly", "launch", "darkly", "react", "sdk", "bindings"], "exports": {"types": "./lib/index.d.ts", "require": "./lib/cjs/index.js", "import": "./lib/esm/index.js"}, "main": "./lib/cjs/index.js", "types": "./lib/index.d.ts", "files": ["lib", "src", "!**/*.test.*", "!**/__snapshots__"], "scripts": {"test": "jest", "test:junit": "jest --ci --reporters=default", "clean": "rimraf lib/*", "rb": "rollup -c --configPlugin typescript", "rbw": "npm run rb --watch", "build": "npm run clean && npm run rb", "lint": "eslint ./src", "check-typescript": "tsc", "prepublishOnly": "npm run build", "prettier": "prettier --write 'src/*.@(js|ts|tsx|json|css)'", "link-dev": "./link-dev.sh", "check": "npm i && npm run prettier && npm run lint && tsc && npm run test", "doc": "typedoc"}, "repository": {"type": "git", "url": "git://github.com/launchdarkly/react-client-sdk.git"}, "homepage": "https://github.com/launchdarkly/react-client-sdk", "devDependencies": {"@eslint/js": "^9.7.0", "@rollup/plugin-json": "^6.0.0", "@rollup/plugin-node-resolve": "^15.1.0", "@rollup/plugin-terser": "^0.4.3", "@rollup/plugin-typescript": "^12.1.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.0", "@types/eslint__js": "^8.42.3", "@types/hoist-non-react-statics": "^3.3.1", "@types/jest": "^29.5.12", "@types/lodash.camelcase": "^4.3.6", "@types/node": "^22.1.0", "@types/prop-types": "^15.7.4", "@types/react": "^18.0.3", "@types/react-dom": "^18.0.0", "@types/react-test-renderer": "^18.0.0", "esbuild": "^0.24.0", "eslint": "^9.8.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-environment-jsdom-global": "^4.0.0", "prettier": "^3.3.3", "prop-types": "^15.7.2", "react": "^18.0.0", "react-dom": "^18.0.0", "react-test-renderer": "^18.0.0", "rimraf": "^6.0.1", "rollup": "^4.19.0", "rollup-plugin-dts": "^6.1.0", "rollup-plugin-esbuild": "^6.1.1", "ts-jest": "^29.2.2", "tslib": "^2.8.1", "typedoc": "^0.26.5", "typescript": "^5.5.4", "typescript-eslint": "^8.0.0", "parse5": "7.2.1"}, "dependencies": {"hoist-non-react-statics": "^3.3.2", "launchdarkly-js-client-sdk": "^3.8.1", "lodash.camelcase": "^4.3.0"}, "peerDependencies": {"react": "^16.6.3 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.8.4 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}