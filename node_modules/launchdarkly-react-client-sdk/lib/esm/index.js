import*as e from"react";import t,{createContext as r,Component as n,useState as o,useEffect as a,useContext as i}from"react";import{initialize as s}from"launchdarkly-js-client-sdk";export*from"launchdarkly-js-client-sdk";import l from"lodash.camelcase";import c from"hoist-non-react-statics";const p=()=>r({flags:{},flagKeyMap:{},ldClient:void 0}),u=p(),{Provider:f,Consumer:d}=u,y={useCamelCaseFlagKeys:!0,sendEventsOnFlagRead:!0,reactContext:u},h=e=>{var t;return null!=(t=e.context)?t:e.user},g=e=>{const t={};for(const r in e)0!==r.indexOf("$")&&(t[l(r)]=e[r]);return t},O=(e,t)=>{const r={};for(const n in e)t&&void 0===t[n]||(r[n]=e[n].current);return r},b=(e,t)=>{const r=e.allFlags();return t?Object.keys(t).reduce(((e,n)=>(e[n]=Object.prototype.hasOwnProperty.call(r,n)?r[n]:t[n],e)),{}):r};function m(e,t,r=y,n){const o=function(e,t){if(void 0===t)return e;return Object.keys(t).reduce(((t,r)=>(v(e,r)&&(t[r]=e[r]),t)),{})}(t,n),{useCamelCaseFlagKeys:a=!0}=r,[i,s={}]=a?function(e){const t={},r={};for(const n in e){if(0===n.indexOf("$"))continue;const o=l(n);t[o]=e[n],r[o]=n}return[t,r]}(o):[o];return{flags:r.sendEventsOnFlagRead?C(e,i,s,a):i,flagKeyMap:s}}function v(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function C(e,t,r,n){return new Proxy(t,{get(t,o,a){const i=Reflect.get(t,o,a),s=n&&v(r,o)||v(t,o);if("symbol"==typeof o||!s)return i;if(void 0===i)return;const l=n?r[o]:o;return e.variation(l,i)}})}g.camelCaseKeys=g;const x={wrapperName:"react-client-sdk",wrapperVersion:"3.8.1",sendEventsOnlyForVariation:!0};var j=Object.defineProperty,w=Object.defineProperties,P=Object.getOwnPropertyDescriptors,F=Object.getOwnPropertySymbols,E=Object.prototype.hasOwnProperty,S=Object.prototype.propertyIsEnumerable,k=(e,t,r)=>t in e?j(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,D=(e,t)=>{for(var r in t||(t={}))E.call(t,r)&&k(e,r,t[r]);if(F)for(var r of F(t))S.call(t,r)&&k(e,r,t[r]);return e},I=(e,t)=>w(e,P(t)),K=(e,t,r)=>new Promise(((n,o)=>{var a=e=>{try{s(r.next(e))}catch(e){o(e)}},i=e=>{try{s(r.throw(e))}catch(e){o(e)}},s=e=>e.done?n(e.value):Promise.resolve(e.value).then(a,i);s((r=r.apply(e,t)).next())}));class R extends n{constructor(e){super(e),this.getReactOptions=()=>D(D({},y),this.props.reactOptions),this.subscribeToChanges=e=>{const{flags:t}=this.props;e.on("change",(r=>{const n=this.getReactOptions(),o=O(r,t),a=D(D({},this.state.unproxiedFlags),o);Object.keys(o).length>0&&this.setState((r=>D(I(D({},r),{unproxiedFlags:a}),m(e,a,n,t))))}))},this.onFailed=(e,t)=>{this.setState((e=>I(D({},e),{error:t})))},this.onReady=(e,t,r)=>{const n=b(e,r);this.setState((o=>D(I(D({},o),{unproxiedFlags:n}),m(e,n,t,r))))},this.prepareLDClient=()=>K(this,null,(function*(){var e;const{clientSideID:t,flags:r,options:n}=this.props;let o=yield this.props.ldClient;const a=this.getReactOptions();let i,l=this.state.unproxiedFlags;if(o)l=b(o,r);else{const c=null!=(e=h(this.props))?e:{anonymous:!0,kind:"user"};o=s(t,c,D(D({},x),n));try{yield o.waitForInitialization(this.props.timeout),l=b(o,r)}catch(e){i=e,(null==i?void 0:i.name.toLowerCase().includes("timeout"))&&(o.on("failed",this.onFailed),o.on("ready",(()=>{this.onReady(o,a,r)})))}}this.setState((e=>I(D(I(D({},e),{unproxiedFlags:l}),m(o,l,a,r)),{ldClient:o,error:i}))),this.subscribeToChanges(o)}));const{options:t}=e;if(this.state={flags:{},unproxiedFlags:{},flagKeyMap:{}},t){const{bootstrap:e}=t;if(e&&"localStorage"!==e){const{useCamelCaseFlagKeys:t}=this.getReactOptions();this.state={flags:t?g(e):e,unproxiedFlags:e,flagKeyMap:{}}}}}componentDidMount(){return K(this,null,(function*(){const{deferInitialization:e}=this.props;e&&!h(this.props)||(yield this.prepareLDClient())}))}componentDidUpdate(e){return K(this,null,(function*(){const{deferInitialization:t}=this.props,r=!h(e)&&h(this.props);t&&r&&(yield this.prepareLDClient())}))}render(){const{flags:e,flagKeyMap:r,ldClient:n,error:o}=this.state,{reactContext:a}=this.getReactOptions();return t.createElement(a.Provider,{value:{flags:e,flagKeyMap:r,ldClient:n,error:o}},this.props.children)}}var M=Object.defineProperty,L=Object.defineProperties,z=Object.getOwnPropertyDescriptors,T=Object.getOwnPropertySymbols,V=Object.prototype.hasOwnProperty,$=Object.prototype.propertyIsEnumerable,N=(e,t,r)=>t in e?M(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,U=(e,t)=>{for(var r in t||(t={}))V.call(t,r)&&N(e,r,t[r]);if(T)for(var r of T(t))$.call(t,r)&&N(e,r,t[r]);return e};function q(t){return function(r){const{reactOptions:n}=t,o=U(U({},y),n),a=(i=U({},t),L(i,z({reactOptions:o})));var i;function s(t){return e.createElement(R,U({},a),e.createElement(r,U({},t)))}return c(s,r),s}}var A=Object.defineProperty,B=Object.defineProperties,G=Object.getOwnPropertyDescriptors,H=Object.getOwnPropertySymbols,J=Object.prototype.hasOwnProperty,Q=Object.prototype.propertyIsEnumerable,W=(e,t,r)=>t in e?A(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,X=(e,t)=>{for(var r in t||(t={}))J.call(t,r)&&W(e,r,t[r]);if(H)for(var r of H(t))Q.call(t,r)&&W(e,r,t[r]);return e},Y=(e,t)=>B(e,G(t));function Z(e){return r=this,n=null,i=function*(){var r,n;const{clientSideID:i,flags:l,options:c,reactOptions:p}=e,u=X(X({},y),p),f=null!=(r=h(e))?r:{anonymous:!0,kind:"user"};let d,g={};const v=null!=(n=yield e.ldClient)?n:s(i,f,X(X({},x),c));try{yield v.waitForInitialization(e.timeout),g=b(v,l)}catch(e){d=e}const C=(null==c?void 0:c.bootstrap)&&"localStorage"!==c.bootstrap?c.bootstrap:g;return({children:e})=>{const[r,n]=o((()=>Y(X({unproxiedFlags:C},m(v,C,u,l)),{ldClient:v,error:d})));a((()=>{function e(e){const t=O(e,l);Object.keys(t).length>0&&n((e=>{const r=X(X({},e.unproxiedFlags),t);return X(Y(X({},e),{unproxiedFlags:r}),m(v,r,u,l))}))}function t(){const e=b(v,l);n((t=>X(Y(X({},t),{unproxiedFlags:e}),m(v,e,u,l))))}function r(e){n((t=>Y(X({},t),{error:e})))}return v.on("change",e),(null==d?void 0:d.name.toLowerCase().includes("timeout"))&&(v.on("failed",r),v.on("ready",t)),function(){v.off("change",e),v.off("failed",r),v.off("ready",t)}}),[]);const i=r,{unproxiedFlags:s}=i,c=((e,t)=>{var r={};for(var n in e)J.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&H)for(var n of H(e))t.indexOf(n)<0&&Q.call(e,n)&&(r[n]=e[n]);return r})(i,["unproxiedFlags"]),{reactContext:p}=u;return t.createElement(p.Provider,{value:c},e)}},new Promise(((e,t)=>{var o=e=>{try{s(i.next(e))}catch(e){t(e)}},a=e=>{try{s(i.throw(e))}catch(e){t(e)}},s=t=>t.done?e(t.value):Promise.resolve(t.value).then(o,a);s((i=i.apply(r,n)).next())}));var r,n,i}var _=Object.defineProperty,ee=Object.getOwnPropertySymbols,te=Object.prototype.hasOwnProperty,re=Object.prototype.propertyIsEnumerable,ne=(e,t,r)=>t in e?_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,oe=(e,t)=>{for(var r in t||(t={}))te.call(t,r)&&ne(e,r,t[r]);if(ee)for(var r of ee(t))re.call(t,r)&&ne(e,r,t[r]);return e};function ae(t={clientOnly:!1}){return function(r){var n;const o=null!=(n=t.reactContext)?n:y.reactContext;return n=>e.createElement(o.Consumer,null,(({flags:o,ldClient:a})=>t.clientOnly?e.createElement(r,oe({ldClient:a},n)):e.createElement(r,oe({flags:o,ldClient:a},n))))}}const ie=e=>{const{flags:t}=i(null!=e?e:y.reactContext);return t},se=e=>{const{ldClient:t}=i(null!=e?e:y.reactContext);return t};function le(e){const{error:t}=i(null!=e?e:y.reactContext);return t}export{R as LDProvider,Z as asyncWithLDProvider,g as camelCaseKeys,y as defaultReactOptions,p as reactSdkContextFactory,ie as useFlags,se as useLDClient,le as useLDClientError,ae as withLDConsumer,q as withLDProvider};
//# sourceMappingURL=index.js.map
