"use strict";var e=require("react"),t=require("launchdarkly-js-client-sdk"),r=require("lodash.camelcase"),n=require("hoist-non-react-statics");function o(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var s=o(e);const i=()=>e.createContext({flags:{},flagKeyMap:{},ldClient:void 0}),a=i(),{Provider:l,Consumer:c}=a,u={useCamelCaseFlagKeys:!0,sendEventsOnFlagRead:!0,reactContext:a},p=e=>{var t;return null!=(t=e.context)?t:e.user},f=e=>{const t={};for(const n in e)0!==n.indexOf("$")&&(t[r(n)]=e[n]);return t},d=(e,t)=>{const r={};for(const n in e)t&&void 0===t[n]||(r[n]=e[n].current);return r},y=(e,t)=>{const r=e.allFlags();return t?Object.keys(t).reduce(((e,n)=>(e[n]=Object.prototype.hasOwnProperty.call(r,n)?r[n]:t[n],e)),{}):r};function h(e,t,n=u,o){const s=function(e,t){if(void 0===t)return e;return Object.keys(t).reduce(((t,r)=>(O(e,r)&&(t[r]=e[r]),t)),{})}(t,o),{useCamelCaseFlagKeys:i=!0}=n,[a,l={}]=i?function(e){const t={},n={};for(const o in e){if(0===o.indexOf("$"))continue;const s=r(o);t[s]=e[o],n[s]=o}return[t,n]}(s):[s];return{flags:n.sendEventsOnFlagRead?g(e,a,l,i):a,flagKeyMap:l}}function O(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function g(e,t,r,n){return new Proxy(t,{get(t,o,s){const i=Reflect.get(t,o,s),a=n&&O(r,o)||O(t,o);if("symbol"==typeof o||!a)return i;if(void 0===i)return;const l=n?r[o]:o;return e.variation(l,i)}})}f.camelCaseKeys=f;const b={wrapperName:"react-client-sdk",wrapperVersion:"3.8.1",sendEventsOnlyForVariation:!0};var v=Object.defineProperty,x=Object.defineProperties,m=Object.getOwnPropertyDescriptors,C=Object.getOwnPropertySymbols,j=Object.prototype.hasOwnProperty,P=Object.prototype.propertyIsEnumerable,w=(e,t,r)=>t in e?v(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,F=(e,t)=>{for(var r in t||(t={}))j.call(t,r)&&w(e,r,t[r]);if(C)for(var r of C(t))P.call(t,r)&&w(e,r,t[r]);return e},E=(e,t)=>x(e,m(t)),D=(e,t,r)=>new Promise(((n,o)=>{var s=e=>{try{a(r.next(e))}catch(e){o(e)}},i=e=>{try{a(r.throw(e))}catch(e){o(e)}},a=e=>e.done?n(e.value):Promise.resolve(e.value).then(s,i);a((r=r.apply(e,t)).next())}));class S extends e.Component{constructor(e){super(e),this.getReactOptions=()=>F(F({},u),this.props.reactOptions),this.subscribeToChanges=e=>{const{flags:t}=this.props;e.on("change",(r=>{const n=this.getReactOptions(),o=d(r,t),s=F(F({},this.state.unproxiedFlags),o);Object.keys(o).length>0&&this.setState((r=>F(E(F({},r),{unproxiedFlags:s}),h(e,s,n,t))))}))},this.onFailed=(e,t)=>{this.setState((e=>E(F({},e),{error:t})))},this.onReady=(e,t,r)=>{const n=y(e,r);this.setState((o=>F(E(F({},o),{unproxiedFlags:n}),h(e,n,t,r))))},this.prepareLDClient=()=>D(this,null,(function*(){var e;const{clientSideID:r,flags:n,options:o}=this.props;let s=yield this.props.ldClient;const i=this.getReactOptions();let a,l=this.state.unproxiedFlags;if(s)l=y(s,n);else{const c=null!=(e=p(this.props))?e:{anonymous:!0,kind:"user"};s=t.initialize(r,c,F(F({},b),o));try{yield s.waitForInitialization(this.props.timeout),l=y(s,n)}catch(e){a=e,(null==a?void 0:a.name.toLowerCase().includes("timeout"))&&(s.on("failed",this.onFailed),s.on("ready",(()=>{this.onReady(s,i,n)})))}}this.setState((e=>E(F(E(F({},e),{unproxiedFlags:l}),h(s,l,i,n)),{ldClient:s,error:a}))),this.subscribeToChanges(s)}));const{options:r}=e;if(this.state={flags:{},unproxiedFlags:{},flagKeyMap:{}},r){const{bootstrap:e}=r;if(e&&"localStorage"!==e){const{useCamelCaseFlagKeys:t}=this.getReactOptions();this.state={flags:t?f(e):e,unproxiedFlags:e,flagKeyMap:{}}}}}componentDidMount(){return D(this,null,(function*(){const{deferInitialization:e}=this.props;e&&!p(this.props)||(yield this.prepareLDClient())}))}componentDidUpdate(e){return D(this,null,(function*(){const{deferInitialization:t}=this.props,r=!p(e)&&p(this.props);t&&r&&(yield this.prepareLDClient())}))}render(){const{flags:t,flagKeyMap:r,ldClient:n,error:o}=this.state,{reactContext:s}=this.getReactOptions();return e.createElement(s.Provider,{value:{flags:t,flagKeyMap:r,ldClient:n,error:o}},this.props.children)}}var k=Object.defineProperty,K=Object.defineProperties,L=Object.getOwnPropertyDescriptors,R=Object.getOwnPropertySymbols,I=Object.prototype.hasOwnProperty,z=Object.prototype.propertyIsEnumerable,M=(e,t,r)=>t in e?k(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,q=(e,t)=>{for(var r in t||(t={}))I.call(t,r)&&M(e,r,t[r]);if(R)for(var r of R(t))z.call(t,r)&&M(e,r,t[r]);return e};var T=Object.defineProperty,V=Object.defineProperties,$=Object.getOwnPropertyDescriptors,N=Object.getOwnPropertySymbols,U=Object.prototype.hasOwnProperty,W=Object.prototype.propertyIsEnumerable,A=(e,t,r)=>t in e?T(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,B=(e,t)=>{for(var r in t||(t={}))U.call(t,r)&&A(e,r,t[r]);if(N)for(var r of N(t))W.call(t,r)&&A(e,r,t[r]);return e},G=(e,t)=>V(e,$(t));var H=Object.defineProperty,J=Object.getOwnPropertySymbols,Q=Object.prototype.hasOwnProperty,X=Object.prototype.propertyIsEnumerable,Y=(e,t,r)=>t in e?H(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Z=(e,t)=>{for(var r in t||(t={}))Q.call(t,r)&&Y(e,r,t[r]);if(J)for(var r of J(t))X.call(t,r)&&Y(e,r,t[r]);return e};exports.LDProvider=S,exports.asyncWithLDProvider=function(r){return n=this,o=null,s=function*(){var n,o;const{clientSideID:s,flags:i,options:a,reactOptions:l}=r,c=B(B({},u),l),f=null!=(n=p(r))?n:{anonymous:!0,kind:"user"};let O,g={};const v=null!=(o=yield r.ldClient)?o:t.initialize(s,f,B(B({},b),a));try{yield v.waitForInitialization(r.timeout),g=y(v,i)}catch(e){O=e}const x=(null==a?void 0:a.bootstrap)&&"localStorage"!==a.bootstrap?a.bootstrap:g;return({children:t})=>{const[r,n]=e.useState((()=>G(B({unproxiedFlags:x},h(v,x,c,i)),{ldClient:v,error:O})));e.useEffect((()=>{function e(e){const t=d(e,i);Object.keys(t).length>0&&n((e=>{const r=B(B({},e.unproxiedFlags),t);return B(G(B({},e),{unproxiedFlags:r}),h(v,r,c,i))}))}function t(){const e=y(v,i);n((t=>B(G(B({},t),{unproxiedFlags:e}),h(v,e,c,i))))}function r(e){n((t=>G(B({},t),{error:e})))}return v.on("change",e),(null==O?void 0:O.name.toLowerCase().includes("timeout"))&&(v.on("failed",r),v.on("ready",t)),function(){v.off("change",e),v.off("failed",r),v.off("ready",t)}}),[]);const o=r,{unproxiedFlags:s}=o,a=((e,t)=>{var r={};for(var n in e)U.call(e,n)&&t.indexOf(n)<0&&(r[n]=e[n]);if(null!=e&&N)for(var n of N(e))t.indexOf(n)<0&&W.call(e,n)&&(r[n]=e[n]);return r})(o,["unproxiedFlags"]),{reactContext:l}=c;return e.createElement(l.Provider,{value:a},t)}},new Promise(((e,t)=>{var r=e=>{try{a(s.next(e))}catch(e){t(e)}},i=e=>{try{a(s.throw(e))}catch(e){t(e)}},a=t=>t.done?e(t.value):Promise.resolve(t.value).then(r,i);a((s=s.apply(n,o)).next())}));var n,o,s},exports.camelCaseKeys=f,exports.defaultReactOptions=u,exports.reactSdkContextFactory=i,exports.useFlags=t=>{const{flags:r}=e.useContext(null!=t?t:u.reactContext);return r},exports.useLDClient=t=>{const{ldClient:r}=e.useContext(null!=t?t:u.reactContext);return r},exports.useLDClientError=function(t){const{error:r}=e.useContext(null!=t?t:u.reactContext);return r},exports.withLDConsumer=function(e={clientOnly:!1}){return function(t){var r;const n=null!=(r=e.reactContext)?r:u.reactContext;return r=>s.createElement(n.Consumer,null,(({flags:n,ldClient:o})=>e.clientOnly?s.createElement(t,Z({ldClient:o},r)):s.createElement(t,Z({flags:n,ldClient:o},r))))}},exports.withLDProvider=function(e){return function(t){const{reactOptions:r}=e,o=q(q({},u),r),i=(a=q({},e),K(a,L({reactOptions:o})));var a;function l(e){return s.createElement(S,q({},i),s.createElement(t,q({},e)))}return n(l,t),l}},Object.keys(t).forEach((function(e){"default"===e||Object.prototype.hasOwnProperty.call(exports,e)||Object.defineProperty(exports,e,{enumerable:!0,get:function(){return t[e]}})}));
//# sourceMappingURL=index.js.map
