{"version": 3, "file": "index.js", "sources": ["../../src/context.ts", "../../src/types.ts", "../../src/utils.ts", "../../src/getFlagsProxy.ts", "../../src/wrapperOptions.ts", "../../src/provider.tsx", "../../src/asyncWithLDProvider.tsx", "../../src/useFlags.ts", "../../src/useLDClient.ts", "../../src/useLDClientError.tsx", "../../src/withLDConsumer.tsx", "../../src/withLDProvider.tsx"], "sourcesContent": ["import { createContext } from 'react';\nimport { ReactSdkContext } from './types';\n\n/**\n * `reactSdkContextFactory` is a function useful for creating a React context for use with\n * all the providers and consumers in this library.\n *\n * @return a React Context\n */\nconst reactSdkContextFactory = () => createContext<ReactSdkContext>({ flags: {}, flagKeyMap: {}, ldClient: undefined });\n/**\n * @ignore\n */\nconst context = reactSdkContextFactory();\nconst {\n  /**\n   * @ignore\n   */\n  Provider,\n  /**\n   * @ignore\n   */\n  Consumer,\n} = context;\n\nexport { Provider, Consumer, ReactSdkContext, reactSdkContextFactory };\nexport default context;\n", "import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ontext, LDFlagSet, LDOptions } from 'launchdarkly-js-client-sdk';\nimport * as React from 'react';\nimport defaultReactContext from './context';\n\n/**\n * Initialization options for the LaunchDarkly React SDK. These are in addition to the options exposed\n * by [[LDOptions]] which are common to both the JavaScript and React SDKs.\n */\nexport interface LDReactOptions {\n  /**\n   * Whether the React SDK should transform flag keys into camel-cased format.\n   * Using camel-cased flag keys allow for easier use as prop values, however,\n   * these keys won't directly match the flag keys as known to LaunchDarkly.\n   * Consequently, flag key collisions may be possible and the Code References feature\n   * will not function properly.\n   *\n   * This is true by default, meaning that keys will automatically be converted to camel-case.\n   *\n   * For more information, see the React SDK Reference Guide on\n   * [flag keys](https://docs.launchdarkly.com/sdk/client-side/react/react-web#flag-keys).\n   *\n   * @see https://docs.launchdarkly.com/sdk/client-side/react/react-web#flag-keys\n   */\n  useCamelCaseFlagKeys?: boolean;\n\n  /**\n   * Whether to send flag evaluation events when a flag is read from the `flags` object\n   * returned by the `use<PERSON>lags` hook. This is true by default, meaning flag evaluation\n   * events will be sent by default.\n   */\n  sendEventsOnFlagRead?: boolean;\n\n  /**\n   * The react context to use within the provider objects.\n   */\n  reactContext?: React.Context<ReactSdkContext>;\n}\n\n/**\n * Contains default values for the `reactOptions` object.\n */\nexport const defaultReactOptions = {\n  useCamelCaseFlagKeys: true,\n  sendEventsOnFlagRead: true,\n  reactContext: defaultReactContext,\n};\n\n/**\n * Configuration object used to initialise LaunchDarkly's JS client.\n */\nexport interface ProviderConfig {\n  /**\n   * Your project and environment specific client side ID. You can find\n   * this in your LaunchDarkly portal under Account settings. This is\n   * the only mandatory property required to use the React SDK.\n   */\n  clientSideID: string;\n\n  /**\n   * A LaunchDarkly context object. If unspecified, an anonymous context\n   * with kind: 'user' will be created and used.\n   */\n  context?: LDContext;\n\n  /**\n   * @deprecated The `user` property will be removed in a future version,\n   * please update your code to use context instead.\n   */\n  user?: LDContext;\n\n  /**\n   * If set to true, the ldClient will not be initialized until the context prop has been defined.\n   */\n  deferInitialization?: boolean;\n\n  /**\n   * LaunchDarkly initialization options. These options are common between LaunchDarkly's JavaScript and React SDKs.\n   *\n   * @see https://docs.launchdarkly.com/sdk/features/config#javascript\n   */\n  options?: LDOptions;\n\n  /**\n   * Additional initialization options specific to the React SDK.\n   *\n   * @see options\n   */\n  reactOptions?: LDReactOptions;\n\n  /**\n   * If specified, `launchdarkly-react-client-sdk` will only listen for changes to these flags.\n   * Otherwise, all flags will be requested and listened to.\n   * Flag keys must be in their original form as known to LaunchDarkly rather than in their camel-cased form.\n   */\n  flags?: LDFlagSet;\n\n  /**\n   * Optionally, the ldClient can be initialized outside of the provider\n   * and passed in, instead of being initialized by the provider.\n   *\n   * Note: it should only be passed in when it has emitted the 'ready'\n   * event when using withLDProvider, to ensure that the flags are properly set.\n   * If using with asyncWithLDProvider, then it will wait internally, so\n   * it is not required that the client have emitted the 'ready' event.\n   */\n  ldClient?: LDClient | Promise<LDClient | undefined>;\n\n  /**\n   *  The amount of time, in seconds, to wait for initialization before rejecting the promise.\n   *  Using a large timeout is not recommended. If you use a large timeout and await it, then\n   *  any network delays will cause your application to wait a long time before continuing\n   *  execution. This gets passed to the underlying Javascript SDK `waitForInitialization`\n   *  function.\n   */\n  timeout?: number;\n}\n\n/**\n * Configuration object used to initialize LaunchDarkly's JS client asynchronously.\n */\nexport type AsyncProviderConfig = Omit<ProviderConfig, 'deferInitialization'> & {\n  /**\n   * @deprecated - `asyncWithLDProvider` does not support the `deferInitialization` config option because\n   * `asyncWithLDProvider` needs to be initialized at the app entry point prior to render to ensure flags and the\n   * ldClient are ready at the beginning of the app.\n   */\n  deferInitialization?: boolean;\n};\n\n/**\n * The return type of withLDProvider HOC. Exported for testing purposes only.\n *\n * @ignore\n */\nexport interface EnhancedComponent extends React.Component {\n  subscribeToChanges(ldClient: LDClient): void;\n  componentDidMount(): Promise<void>;\n  componentDidUpdate(prevProps: ProviderConfig): Promise<void>;\n}\n\n/**\n * Return type of `initLDClient`.\n */\nexport interface AllFlagsLDClient {\n  /**\n   * Contains all flags from LaunchDarkly.\n   */\n  flags: LDFlagSet;\n\n  /**\n   * An instance of `LDClient` from the LaunchDarkly JS SDK (`launchdarkly-js-client-sdk`).\n   *\n   * @see https://docs.launchdarkly.com/sdk/client-side/javascript\n   */\n  ldClient: LDClient;\n\n  /**\n   * LaunchDarkly client initialization error, if there was one.\n   */\n  error?: Error;\n}\n\n/**\n * Map of camelized flag keys to original unmodified flag keys.\n */\nexport type LDFlagKeyMap = Record<string, string>;\n\nexport { type LDProps } from './withLDConsumer';\n\n/**\n * The sdk context stored in the Provider state and passed to consumers.\n */\nexport interface ReactSdkContext {\n  /**\n   * JavaScript proxy that will trigger a LDClient#variation call on flag read in order\n   * to register a flag evaluation event in LaunchDarkly. Empty {} initially\n   * until flags are fetched from the LaunchDarkly servers.\n   */\n  flags: LDFlagSet;\n\n  /**\n   * Map of camelized flag keys to their original unmodified form. Empty if useCamelCaseFlagKeys option is false.\n   */\n  flagKeyMap: LDFlagKeyMap;\n\n  /**\n   * An instance of `LDClient` from the LaunchDarkly JS SDK (`launchdarkly-js-client-sdk`).\n   * This will be be undefined initially until initialization is complete.\n   *\n   * @see https://docs.launchdarkly.com/sdk/client-side/javascript\n   */\n  ldClient?: LDClient;\n\n  /**\n   * LaunchDarkly client initialization error, if there was one.\n   */\n  error?: Error;\n}\n\nexport * from 'launchdarkly-js-client-sdk';\n", "import { LDClient, LDContext, LDFlagChangeset, LDFlagSet } from 'launchdarkly-js-client-sdk';\nimport camelCase from 'lodash.camelcase';\nimport { ProviderConfig } from './types';\n\n/**\n * Helper function to get the context or fallback to classic user.\n * Safe to remove when the user property is deprecated.\n */\nexport const getContextOrUser = (config: ProviderConfig): LDContext | undefined => config.context ?? config.user;\n\n/**\n * Transforms a set of flags so that their keys are camelCased. This function ignores\n * flag keys which start with `$`.\n *\n * @param rawFlags A mapping of flag keys and their values\n * @return A transformed `LDFlagSet` with camelCased flag keys\n */\nexport const camelCaseKeys = (rawFlags: LDFlagSet) => {\n  const flags: LDFlagSet = {};\n  for (const rawFlag in rawFlags) {\n    // Exclude system keys\n    if (rawFlag.indexOf('$') !== 0) {\n      flags[camelCase(rawFlag)] = rawFlags[rawFlag];\n    }\n  }\n\n  return flags;\n};\n\n/**\n * Gets the flags to pass to the provider from the changeset.\n *\n * @param changes the `LDFlagChangeset` from the ldClient onchange handler.\n * @param targetFlags if targetFlags are specified, changes to other flags are ignored and not returned in the\n * flattened `LDFlagSet`\n * @return an `LDFlagSet` with the current flag values from the LDFlagChangeset filtered by `targetFlags`. The returned\n * object may be empty `{}` if none of the targetFlags were changed.\n */\nexport const getFlattenedFlagsFromChangeset = (\n  changes: LDFlagChangeset,\n  targetFlags: LDFlagSet | undefined,\n): LDFlagSet => {\n  const flattened: LDFlagSet = {};\n  for (const key in changes) {\n    if (!targetFlags || targetFlags[key] !== undefined) {\n      flattened[key] = changes[key].current;\n    }\n  }\n\n  return flattened;\n};\n\n/**\n * Retrieves flag values.\n *\n * @param ldClient LaunchDarkly client\n * @param targetFlags If specified, `launchdarkly-react-client-sdk` will only listen for changes to these flags.\n * Flag keys must be in their original form as known to LaunchDarkly rather than in their camel-cased form.\n *\n * @returns an `LDFlagSet` with the current flag values from LaunchDarkly filtered by `targetFlags`.\n */\nexport const fetchFlags = (ldClient: LDClient, targetFlags?: LDFlagSet) => {\n  const allFlags = ldClient.allFlags();\n  if (!targetFlags) {\n    return allFlags;\n  }\n\n  return Object.keys(targetFlags).reduce<LDFlagSet>((acc, key) => {\n    acc[key] = Object.prototype.hasOwnProperty.call(allFlags, key) ? allFlags[key] : targetFlags[key];\n\n    return acc;\n  }, {});\n};\n\n/**\n * @deprecated The `camelCaseKeys.camelCaseKeys` property will be removed in a future version,\n * please update your code to use the `camelCaseKeys` function directly.\n */\ncamelCaseKeys.camelCaseKeys = camelCaseKeys;\n\nexport default { camelCaseKeys, getFlattenedFlagsFromChangeset, fetchFlags };\n", "import { LDFlagSet, LDClient } from 'launchdarkly-js-client-sdk';\nimport camelCase from 'lodash.camelcase';\nimport { defaultReactOptions, LDFlagKeyMap, LDReactOptions } from './types';\n\nexport default function getFlagsProxy(\n  ldClient: LDClient,\n  rawFlags: LDFlagSet,\n  reactOptions: LDReactOptions = defaultReactOptions,\n  targetFlags?: LDFlagSet,\n): { flags: LDFlagSet; flagKeyMap: LDFlagKeyMap } {\n  const filteredFlags = filterFlags(rawFlags, targetFlags);\n  const { useCamelCaseFlagKeys = true } = reactOptions;\n  const [flags, flagKeyMap = {}] = useCamelCaseFlagKeys ? getCamelizedKeysAndFlagMap(filteredFlags) : [filteredFlags];\n\n  return {\n    flags: reactOptions.sendEventsOnFlagRead ? toFlagsProxy(ldClient, flags, flagKeyMap, useCamelCaseFlagKeys) : flags,\n    flagKeyMap,\n  };\n}\n\nfunction filterFlags(flags: LDFlagSet, targetFlags?: LDFlagSet): LDFlagSet {\n  if (targetFlags === undefined) {\n    return flags;\n  }\n\n  return Object.keys(targetFlags).reduce<LDFlagSet>((acc, key) => {\n    if (hasFlag(flags, key)) {\n      acc[key] = flags[key];\n    }\n\n    return acc;\n  }, {});\n}\n\nfunction getCamelizedKeysAndFlagMap(rawFlags: LDFlagSet) {\n  const flags: LDFlagSet = {};\n  const flagKeyMap: LDFlagKeyMap = {};\n  for (const rawFlag in rawFlags) {\n    // Exclude system keys\n    if (rawFlag.indexOf('$') === 0) {\n      continue;\n    }\n    const camelKey = camelCase(rawFlag);\n    flags[camelKey] = rawFlags[rawFlag];\n    flagKeyMap[camelKey] = rawFlag;\n  }\n\n  return [flags, flagKeyMap];\n}\n\nfunction hasFlag(flags: LDFlagSet, flagKey: string) {\n  return Object.prototype.hasOwnProperty.call(flags, flagKey);\n}\n\nfunction toFlagsProxy(\n  ldClient: LDClient,\n  flags: LDFlagSet,\n  flagKeyMap: LDFlagKeyMap,\n  useCamelCaseFlagKeys: boolean,\n): LDFlagSet {\n  return new Proxy(flags, {\n    // trap for reading a flag value using `LDClient#variation` to trigger an evaluation event\n    get(target, prop, receiver) {\n      const currentValue = Reflect.get(target, prop, receiver);\n\n      // check if flag key exists as camelCase or original case\n      const validFlagKey =\n        (useCamelCaseFlagKeys && hasFlag(flagKeyMap, prop as string)) || hasFlag(target, prop as string);\n\n      // only process flag keys and ignore symbols and native Object functions\n      if (typeof prop === 'symbol' || !validFlagKey) {\n        return currentValue;\n      }\n\n      if (currentValue === undefined) {\n        return;\n      }\n\n      const pristineFlagKey = useCamelCaseFlagKeys ? flagKeyMap[prop] : prop;\n\n      return ldClient.variation(pristineFlagKey, currentValue);\n    },\n  });\n}\n", "import { LDOptions } from 'launchdarkly-js-client-sdk';\nimport * as packageInfo from '../package.json';\n\nconst wrapperOptions: LDOptions = {\n  wrapperName: 'react-client-sdk',\n  wrapperVersion: packageInfo.version,\n  sendEventsOnlyForVariation: true,\n};\n\nexport default wrapperOptions;\n", "import React, { Component, PropsWithChildren } from 'react';\nimport { initialize, LDClient, LDFlagChangeset, LDFlagSet } from 'launchdarkly-js-client-sdk';\nimport { EnhancedComponent, ProviderConfig, defaultReactOptions, LDReactOptions } from './types';\nimport { camelCaseKeys, fetchFlags, getContextOrUser, getFlattenedFlagsFromChangeset } from './utils';\nimport getFlagsProxy from './getFlagsProxy';\nimport wrapperOptions from './wrapperOptions';\nimport ProviderState from './providerState';\n\n/**\n * The `LDProvider` is a component which accepts a config object which is used to\n * initialize `launchdarkly-js-client-sdk`.\n *\n * This Provider does three things:\n * - It initializes the ldClient instance by calling `launchdarkly-js-client-sdk` initialize on `componentDidMount`\n * - It saves all flags and the ldClient instance in the context API\n * - It subscribes to flag changes and propagate them through the context API\n *\n * Because the `launchdarkly-js-client-sdk` in only initialized on `componentDidMount`, your flags and the\n * ldClient are only available after your app has mounted. This can result in a flicker due to flag changes at\n * startup time.\n *\n * This component can be used as a standalone provider. However, be mindful to only include the component once\n * within your application. This provider is used inside the `withLDProviderHOC` and can be used instead to initialize\n * the `launchdarkly-js-client-sdk`. For async initialization, check out the `asyncWithLDProvider` function\n */\nclass LDProvider extends Component<PropsWithChildren<ProviderConfig>, ProviderState> implements EnhancedComponent {\n  readonly state: Readonly<ProviderState>;\n\n  constructor(props: ProviderConfig) {\n    super(props);\n\n    const { options } = props;\n\n    this.state = {\n      flags: {},\n      unproxiedFlags: {},\n      flagKeyMap: {},\n    };\n\n    if (options) {\n      const { bootstrap } = options;\n      if (bootstrap && bootstrap !== 'localStorage') {\n        const { useCamelCaseFlagKeys } = this.getReactOptions();\n        this.state = {\n          flags: useCamelCaseFlagKeys ? camelCaseKeys(bootstrap) : bootstrap,\n          unproxiedFlags: bootstrap,\n          flagKeyMap: {},\n        };\n      }\n    }\n  }\n\n  getReactOptions = () => ({ ...defaultReactOptions, ...this.props.reactOptions });\n\n  subscribeToChanges = (ldClient: LDClient) => {\n    const { flags: targetFlags } = this.props;\n    ldClient.on('change', (changes: LDFlagChangeset) => {\n      const reactOptions = this.getReactOptions();\n      const updates = getFlattenedFlagsFromChangeset(changes, targetFlags);\n      const unproxiedFlags = {\n        ...this.state.unproxiedFlags,\n        ...updates,\n      };\n      if (Object.keys(updates).length > 0) {\n        this.setState((prevState) => ({\n          ...prevState,\n          unproxiedFlags,\n          ...getFlagsProxy(ldClient, unproxiedFlags, reactOptions, targetFlags),\n        }));\n      }\n    });\n  };\n\n  onFailed = (_ldClient: LDClient, e: Error) => {\n    this.setState((prevState) => ({ ...prevState, error: e }));\n  };\n\n  onReady = (ldClient: LDClient, reactOptions: LDReactOptions, targetFlags?: LDFlagSet) => {\n    const unproxiedFlags = fetchFlags(ldClient, targetFlags);\n    this.setState((prevState) => ({\n      ...prevState,\n      unproxiedFlags,\n      ...getFlagsProxy(ldClient, unproxiedFlags, reactOptions, targetFlags),\n    }));\n  };\n\n  prepareLDClient = async () => {\n    const { clientSideID, flags: targetFlags, options } = this.props;\n    let ldClient = await this.props.ldClient;\n    const reactOptions = this.getReactOptions();\n    let unproxiedFlags = this.state.unproxiedFlags;\n    let error: Error;\n\n    if (ldClient) {\n      unproxiedFlags = fetchFlags(ldClient, targetFlags);\n    } else {\n      const context = getContextOrUser(this.props) ?? { anonymous: true, kind: 'user' };\n      ldClient = initialize(clientSideID, context, { ...wrapperOptions, ...options });\n\n      try {\n        await ldClient.waitForInitialization(this.props.timeout);\n        unproxiedFlags = fetchFlags(ldClient, targetFlags);\n      } catch (e) {\n        error = e as Error;\n\n        if (error?.name.toLowerCase().includes('timeout')) {\n          ldClient.on('failed', this.onFailed);\n          ldClient.on('ready', () => {\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            this.onReady(ldClient!, reactOptions, targetFlags);\n          });\n        }\n      }\n    }\n    this.setState((prevState) => ({\n      ...prevState,\n      unproxiedFlags,\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      ...getFlagsProxy(ldClient!, unproxiedFlags, reactOptions, targetFlags),\n      ldClient,\n      error,\n    }));\n    this.subscribeToChanges(ldClient);\n  };\n\n  async componentDidMount() {\n    const { deferInitialization } = this.props;\n    if (deferInitialization && !getContextOrUser(this.props)) {\n      return;\n    }\n\n    await this.prepareLDClient();\n  }\n\n  async componentDidUpdate(prevProps: ProviderConfig) {\n    const { deferInitialization } = this.props;\n    const contextJustLoaded = !getContextOrUser(prevProps) && getContextOrUser(this.props);\n    if (deferInitialization && contextJustLoaded) {\n      await this.prepareLDClient();\n    }\n  }\n\n  render() {\n    const { flags, flagKeyMap, ldClient, error } = this.state;\n\n    const { reactContext } = this.getReactOptions();\n\n    return (\n      <reactContext.Provider value={{ flags, flagKeyMap, ldClient, error }}>\n        {this.props.children}\n      </reactContext.Provider>\n    );\n  }\n}\n\nexport default LDProvider;\n", "import React, { useState, useEffect, ReactNode } from 'react';\nimport { initialize, LDFlagChangeset } from 'launchdarkly-js-client-sdk';\nimport { AsyncProviderConfig, defaultReactOptions } from './types';\nimport { fetchFlags, getContextOrUser, getFlattenedFlagsFromChangeset } from './utils';\nimport getFlagsProxy from './getFlagsProxy';\nimport wrapperOptions from './wrapperOptions';\nimport ProviderState from './providerState';\n\n/**\n * This is an async function which initializes LaunchDarkly's JS SDK (`launchdarkly-js-client-sdk`)\n * and awaits it so all flags and the ldClient are ready before the consumer app is rendered.\n *\n * The difference between `withLDProvider` and `asyncWithLDProvider` is that `withLDProvider` initializes\n * `launchdarkly-js-client-sdk` at componentDidMount. This means your flags and the ldClient are only available after\n * your app has mounted. This can result in a flicker due to flag changes at startup time.\n *\n * `asyncWithLDProvider` initializes `launchdarkly-js-client-sdk` at the entry point of your app prior to render.\n * This means that your flags and the ldClient are ready at the beginning of your app. This ensures your app does not\n * flicker due to flag changes at startup time.\n *\n * `asyncWithLDProvider` accepts a config object which is used to initialize `launchdarkly-js-client-sdk`.\n *\n * `asyncWithLDProvider` does not support the `deferInitialization` config option because `asyncWithLDProvider` needs\n * to be initialized at the entry point prior to render to ensure your flags and the ldClient are ready at the beginning\n * of your app.\n *\n * It returns a provider which is a React FunctionComponent which:\n * - saves all flags and the ldClient instance in the context API\n * - subscribes to flag changes and propagate them through the context API\n *\n * @param config - The configuration used to initialize LaunchDarkly's JS SDK\n */\nexport default async function asyncWithLDProvider(config: AsyncProviderConfig) {\n  const { clientSideID, flags: targetFlags, options, reactOptions: userReactOptions } = config;\n  const reactOptions = { ...defaultReactOptions, ...userReactOptions };\n  const context = getContextOrUser(config) ?? { anonymous: true, kind: 'user' };\n  let error: Error;\n  let fetchedFlags = {};\n\n  const ldClient = (await config.ldClient) ?? initialize(clientSideID, context, { ...wrapperOptions, ...options });\n  try {\n    await ldClient.waitForInitialization(config.timeout);\n    fetchedFlags = fetchFlags(ldClient, targetFlags);\n  } catch (e) {\n    error = e as Error;\n  }\n\n  const initialFlags = options?.bootstrap && options.bootstrap !== 'localStorage' ? options.bootstrap : fetchedFlags;\n\n  const LDProvider = ({ children }: { children: ReactNode }) => {\n    const [ldData, setLDData] = useState<ProviderState>(() => ({\n      unproxiedFlags: initialFlags,\n      ...getFlagsProxy(ldClient, initialFlags, reactOptions, targetFlags),\n      ldClient,\n      error,\n    }));\n\n    useEffect(() => {\n      function onChange(changes: LDFlagChangeset) {\n        const updates = getFlattenedFlagsFromChangeset(changes, targetFlags);\n        if (Object.keys(updates).length > 0) {\n          setLDData((prevState) => {\n            const updatedUnproxiedFlags = { ...prevState.unproxiedFlags, ...updates };\n\n            return {\n              ...prevState,\n              unproxiedFlags: updatedUnproxiedFlags,\n              ...getFlagsProxy(ldClient, updatedUnproxiedFlags, reactOptions, targetFlags),\n            };\n          });\n        }\n      }\n      ldClient.on('change', onChange);\n\n      function onReady() {\n        const unproxiedFlags = fetchFlags(ldClient, targetFlags);\n        setLDData((prevState) => ({\n          ...prevState,\n          unproxiedFlags,\n          ...getFlagsProxy(ldClient, unproxiedFlags, reactOptions, targetFlags),\n        }));\n      }\n\n      function onFailed(e: Error) {\n        setLDData((prevState) => ({ ...prevState, error: e }));\n      }\n\n      // Only subscribe to ready and failed if waitForInitialization timed out\n      // because we want the introduction of init timeout to be as minimal and backwards\n      // compatible as possible.\n      if (error?.name.toLowerCase().includes('timeout')) {\n        ldClient.on('failed', onFailed);\n        ldClient.on('ready', onReady);\n      }\n\n      return function cleanup() {\n        ldClient.off('change', onChange);\n        ldClient.off('failed', onFailed);\n        ldClient.off('ready', onReady);\n      };\n    }, []);\n\n    // unproxiedFlags is for internal use only. Exclude it from context.\n    const { unproxiedFlags: _, ...rest } = ldData;\n\n    const { reactContext } = reactOptions;\n\n    return <reactContext.Provider value={rest}>{children}</reactContext.Provider>;\n  };\n\n  return LDProvider;\n}\n", "import { LDFlagSet } from 'launchdarkly-js-client-sdk';\nimport React, { useContext } from 'react';\nimport { defaultReactOptions, ReactSdkContext } from './types';\n\n/**\n * `useFlags` is a custom hook which returns all feature flags. It uses the `useContext` primitive\n * to access the LaunchDarkly context set up by `withLDProvider`. As such you will still need to\n * use the `withLDProvider` HOC at the root of your app to initialize the React SDK and populate the\n * context with `ldClient` and your flags.\n *\n * @param reactContext If specified, the provided React context will be used.\n *\n * @return All the feature flags configured in your LaunchDarkly project\n */\nconst useFlags = <T extends LDFlagSet = LDFlagSet>(reactContext?: React.Context<ReactSdkContext>): T => {\n  const { flags } = useContext<ReactSdkContext>(reactContext ?? defaultReactOptions.reactContext);\n\n  return flags as T;\n};\nexport default useFlags;\n", "import React, { useContext } from 'react';\nimport { defaultReactOptions, ReactSdkContext } from './types';\n\n// eslint:disable:max-line-length\n/**\n * `useLDClient` is a custom hook which returns the underlying [LaunchDarkly JavaScript SDK client object](https://launchdarkly.github.io/js-client-sdk/interfaces/LDClient.html).\n * Like the `useFlags` custom hook, `useLDClient` also uses the `useContext` primitive to access the LaunchDarkly\n * context set up by `withLDProvider`. You will still need to use the `withLD<PERSON><PERSON>ider` HOC\n * to initialise the react sdk to use this custom hook.\n *\n * @param reactContext If specified, the custom React context will be used.\n *\n * @return The `launchdarkly-js-client-sdk` `LDClient` object\n */\nconst useLDClient = (reactContext?: React.Context<ReactSdkContext>) => {\n  const { ldClient } = useContext(reactContext ?? defaultReactOptions.reactContext);\n\n  return ldClient;\n};\n\nexport default useLDClient;\n", "import React, { useContext } from 'react';\nimport { defaultReactOptions, ReactSdkContext } from './types';\n\n/**\n * Provides the LaunchDarkly client initialization error, if there was one.\n *\n * @param reactContext If specified, the custom React context will be used.\n *\n * @return The `launchdarkly-js-client-sdk` `LDClient` initialization error\n */\nexport default function useLDClientError(reactContext?: React.Context<ReactSdkContext>) {\n  const { error } = useContext(reactContext ?? defaultReactOptions.reactContext);\n\n  return error;\n}\n", "import * as React from 'react';\nimport { LDClient, LDFlagSet } from 'launchdarkly-js-client-sdk';\nimport { defaultReactOptions, ReactSdkContext } from './types';\n\n/**\n * Controls the props the wrapped component receives from the `LDConsumer` HOC.\n */\nexport interface ConsumerOptions {\n  /**\n   * If true then the wrapped component only receives the `ldClient` instance\n   * and nothing else.\n   */\n  clientOnly: boolean;\n\n  reactContext?: React.Context<ReactSdkContext>;\n}\n\n/**\n * The possible props the wrapped component can receive from the `LDConsumer` HOC.\n */\nexport interface LDProps {\n  /**\n   * A map of feature flags from their keys to their values.\n   * Keys are camelCased using `lodash.camelcase`.\n   */\n  flags?: LDFlagSet;\n\n  /**\n   * An instance of `LDClient` from the LaunchDarkly JS SDK (`launchdarkly-js-client-sdk`)\n   *\n   * @see https://docs.launchdarkly.com/sdk/client-side/javascript\n   */\n  ldClient?: LDClient;\n}\n\n/**\n * withLDConsumer is a function which accepts an optional options object and returns a function\n * which accepts your React component. This function returns a HOC with flags\n * and the ldClient instance injected via props.\n *\n * @param options - If you need only the `ldClient` instance and not flags, then set `{ clientOnly: true }`\n * to only pass the ldClient prop to your component. Defaults to `{ clientOnly: false }`.\n * @return A HOC with flags and the `ldClient` instance injected via props\n */\nfunction withLDConsumer(options: ConsumerOptions = { clientOnly: false }) {\n  return function withLDConsumerHoc<P>(WrappedComponent: React.ComponentType<P & LDProps>) {\n    const ReactContext = options.reactContext ?? defaultReactOptions.reactContext;\n\n    return (props: P) => (\n      <ReactContext.Consumer>\n        {({ flags, ldClient }: ReactSdkContext) => {\n          if (options.clientOnly) {\n            return <WrappedComponent ldClient={ldClient} {...props} />;\n          }\n\n          return <WrappedComponent flags={flags} ldClient={ldClient} {...props} />;\n        }}\n      </ReactContext.Consumer>\n    );\n  };\n}\n\nexport default withLDConsumer;\n", "import * as React from 'react';\nimport { defaultReactOptions, ProviderConfig } from './types';\nimport LDProvider from './provider';\nimport hoistNonReactStatics from 'hoist-non-react-statics';\n\n/**\n * `withLDProvider` is a function which accepts a config object which is used to\n * initialize `launchdarkly-js-client-sdk`.\n *\n * This HOC handles passing configuration to the `LDProvider`, which does the following:\n * - It initializes the ldClient instance by calling `launchdarkly-js-client-sdk` initialize on `componentDidMount`\n * - It saves all flags and the ldClient instance in the context API\n * - It subscribes to flag changes and propagate them through the context API\n *\n * The difference between `withLDProvider` and `asyncWithLDProvider` is that `withLD<PERSON><PERSON>ider` initializes\n * `launchdarkly-js-client-sdk` at `componentDidMount`. This means your flags and the ldClient are only available after\n * your app has mounted. This can result in a flicker due to flag changes at startup time.\n *\n * `asyncWithLDProvider` initializes `launchdarkly-js-client-sdk` at the entry point of your app prior to render.\n * This means that your flags and the ldClient are ready at the beginning of your app. This ensures your app does not\n * flicker due to flag changes at startup time.\n *\n * @param config - The configuration used to initialize LaunchDarkly's JS SDK\n * @return A function which accepts your root React component and returns a HOC\n */\n// eslint-disable-next-line @typescript-eslint/no-empty-object-type\nexport function withLDProvider<T extends JSX.IntrinsicAttributes = {}>(\n  config: ProviderConfig,\n): (WrappedComponent: React.ComponentType<T>) => React.ComponentType<T> {\n  return function withLDProviderHoc(WrappedComponent: React.ComponentType<T>): React.ComponentType<T> {\n    const { reactOptions: userReactOptions } = config;\n    const reactOptions = { ...defaultReactOptions, ...userReactOptions };\n    const providerProps = { ...config, reactOptions };\n\n    function HoistedComponent(props: T) {\n      return (\n        <LDProvider {...providerProps}>\n          <WrappedComponent {...props} />\n        </LDProvider>\n      );\n    }\n\n    hoistNonReactStatics(HoistedComponent, WrappedComponent);\n\n    return HoistedComponent;\n  };\n}\n\nexport default withLDProvider;\n"], "names": ["reactSdkContextFactory", "createContext", "flags", "flagKeyMap", "ldClient", "context", "Provider", "Consumer", "defaultReactOptions", "useCamelCaseFlagKeys", "sendEventsOnFlagRead", "reactContext", "defaultReactContext", "getContextOrUser", "config", "_a", "user", "camelCase<PERSON>eys", "rawFlags", "rawFlag", "indexOf", "camelCase", "getFlattenedFlagsFromChangeset", "changes", "targetFlags", "flattened", "key", "current", "fetchFlags", "allFlags", "Object", "keys", "reduce", "acc", "prototype", "hasOwnProperty", "call", "getFlagsProxy", "reactOptions", "filteredFlags", "hasFlag", "filterFlags", "camel<PERSON><PERSON>", "getCamelizedKeysAndFlagMap", "toFlagsProxy", "<PERSON><PERSON><PERSON>", "Proxy", "get", "target", "prop", "receiver", "currentValue", "Reflect", "validFlagKey", "pristineFlag<PERSON>ey", "variation", "wrapperOptions", "wrapperName", "wrapperVersion", "sendEventsOnlyForVariation", "LDProvider", "Component", "constructor", "props", "super", "this", "getReactOptions", "__spreadValues", "subscribeToChanges", "on", "updates", "unproxiedFlags", "state", "length", "setState", "prevState", "__spreadProps", "onFailed", "_ldClient", "e", "error", "onReady", "prepareLDClient", "__async", "clientSideID", "options", "anonymous", "kind", "initialize", "waitForInitialization", "timeout", "name", "toLowerCase", "includes", "bootstrap", "componentDidMount", "deferInitialization", "componentDidUpdate", "prevProps", "contextJustLoaded", "render", "React", "createElement", "value", "children", "_b", "userReactOptions", "fetchedFlags", "initialFlags", "ldData", "setLDData", "useState", "useEffect", "onChange", "updatedUnproxiedFlags", "off", "_", "rest", "__objRest", "useContext", "clientOnly", "WrappedComponent", "ReactContext", "providerProps", "HoistedComponent", "hoistNonReactStatics"], "mappings": "6ZASA,MAAMA,EAAyB,IAAMC,EAA+BA,cAAA,CAAEC,MAAO,CAAI,EAAAC,WAAY,CAAA,EAAIC,cAAU,IAIrGC,EAAUL,KACVM,SAIJA,EAAAC,SAIAA,GACEF,ECkBSG,EAAsB,CACjCC,sBAAsB,EACtBC,sBAAsB,EACtBC,aAAcC,GCpCHC,EAAoBC,IARjC,IAAAC,EAQ0F,OAAP,OAAOA,EAAAD,EAAAT,WAAWS,EAAOE,MAS/FC,EAAiBC,IAC5B,MAAMhB,EAAmB,CAAC,EAC1B,IAAA,MAAWiB,KAAWD,EAES,IAAzBC,EAAQC,QAAQ,OAClBlB,EAAMmB,EAAUF,IAAYD,EAASC,IAIlC,OAAAjB,GAYIoB,EAAiC,CAC5CC,EACAC,KAEA,MAAMC,EAAuB,CAAC,EAC9B,IAAA,MAAWC,KAAOH,EACXC,QAAoC,IAArBA,EAAYE,KAC9BD,EAAUC,GAAOH,EAAQG,GAAKC,SAI3B,OAAAF,GAYIG,EAAa,CAACxB,EAAoBoB,KACvC,MAAAK,EAAWzB,EAASyB,WAC1B,OAAKL,EAIEM,OAAOC,KAAKP,GAAaQ,QAAkB,CAACC,EAAKP,KACtDO,EAAIP,GAAOI,OAAOI,UAAUC,eAAeC,KAAKP,EAAUH,GAAOG,EAASH,GAAOF,EAAYE,GAEtFO,IACN,IAPMJ,GC5DX,SAAwBQ,EACtBjC,EACAc,EACAoB,EAA+B9B,EAC/BgB,GAEM,MAAAe,EAUR,SAAqBrC,EAAkBsB,GACrC,QAAoB,IAAhBA,EACK,OAAAtB,EAGT,OAAO4B,OAAOC,KAAKP,GAAaQ,QAAkB,CAACC,EAAKP,KAClDc,EAAQtC,EAAOwB,KACbO,EAAAP,GAAOxB,EAAMwB,IAGZO,IACN,GACL,CAtBwBQ,CAAYvB,EAAUM,IACtCf,qBAAEA,GAAuB,GAAS6B,GACjCpC,EAAOC,EAAa,IAAMM,EAsBnC,SAAoCS,GAClC,MAAMhB,EAAmB,CAAC,EACpBC,EAA2B,CAAC,EAClC,IAAA,MAAWgB,KAAWD,EAAU,CAE9B,GAA6B,IAAzBC,EAAQC,QAAQ,KAClB,SAEI,MAAAsB,EAAWrB,EAAUF,GACrBjB,EAAAwC,GAAYxB,EAASC,GAC3BhB,EAAWuC,GAAYvB,CAAA,CAGlB,MAAA,CAACjB,EAAOC,EACjB,CApC0DwC,CAA2BJ,GAAiB,CAACA,GAE9F,MAAA,CACLrC,MAAOoC,EAAa5B,qBAAuBkC,EAAaxC,EAAUF,EAAOC,EAAYM,GAAwBP,EAC7GC,aAEJ,CAgCA,SAASqC,EAAQtC,EAAkB2C,GACjC,OAAOf,OAAOI,UAAUC,eAAeC,KAAKlC,EAAO2C,EACrD,CAEA,SAASD,EACPxC,EACAF,EACAC,EACAM,GAEO,OAAA,IAAIqC,MAAM5C,EAAO,CAEtB,GAAA6C,CAAIC,EAAQC,EAAMC,GAChB,MAAMC,EAAeC,QAAQL,IAAIC,EAAQC,EAAMC,GAGzCG,EACH5C,GAAwB+B,EAAQrC,EAAY8C,IAAoBT,EAAQQ,EAAQC,GAGnF,GAAoB,iBAATA,IAAsBI,EACxB,OAAAF,EAGT,QAAqB,IAAjBA,EACF,OAGF,MAAMG,EAAkB7C,EAAuBN,EAAW8C,GAAQA,EAE3D,OAAA7C,EAASmD,UAAUD,EAAiBH,EAAY,GAG7D,CDLAlC,EAAcA,cAAgBA,EE3E9B,MAAMuC,EAA4B,CAChCC,YAAa,mBACbC,uBACAC,4BAA4B,2mBCmB9B,MAAMC,UAAmBC,EAAAA,UAGvB,WAAAC,CAAYC,GACVC,MAAMD,GAuBRE,KAAAC,gBAAkB,IAAOC,EAAAA,EAAA,GAAK3D,GAAwByD,KAAKF,MAAMzB,cAEjE2B,KAAAG,mBAAsBhE,IACpB,MAAQF,MAAOsB,GAAgByC,KAAKF,MAC3B3D,EAAAiE,GAAG,UAAW9C,IACf,MAAAe,EAAe2B,KAAKC,kBACpBI,EAAUhD,EAA+BC,EAASC,GAClD+C,EAAiBJ,EAAAA,EAAA,CAAA,EAClBF,KAAKO,MAAMD,gBACXD,GAEDxC,OAAOC,KAAKuC,GAASG,OAAS,GAChCR,KAAKS,UAAUC,GAAeR,EAAAS,EAAAT,EAAA,GACzBQ,GADyB,CAE5BJ,mBACGlC,EAAcjC,EAAUmE,EAAgBjC,EAAcd,UAMtDyC,KAAAY,SAAA,CAACC,EAAqBC,KAC1Bd,KAAAS,UAAUC,GAAeC,EAAAT,EAAA,GAAKQ,GAAL,CAAgBK,MAAOD,OAG7Cd,KAAAgB,QAAA,CAAC7E,EAAoBkC,EAA8Bd,KACrD,MAAA+C,EAAiB3C,EAAWxB,EAAUoB,GAC5CyC,KAAKS,UAAUC,GAAeR,EAAAS,EAAAT,EAAA,GACzBQ,GADyB,CAE5BJ,mBACGlC,EAAcjC,EAAUmE,EAAgBjC,EAAcd,OAI7DyC,KAAAiB,gBAAkB,IAAYC,EAAAlB,KAAA,MAAA,YAtFhC,IAAAlD,EAuFI,MAAMqE,aAAEA,EAAclF,MAAOsB,EAAa6D,QAAAA,GAAYpB,KAAKF,MACvD,IAAA3D,QAAiB6D,KAAKF,MAAM3D,SAC1B,MAAAkC,EAAe2B,KAAKC,kBACtB,IACAc,EADAT,EAAiBN,KAAKO,MAAMD,eAGhC,GAAInE,EACemE,EAAA3C,EAAWxB,EAAUoB,OACjC,CACC,MAAAnB,EAAU,OAAAU,EAAiBF,EAAAoD,KAAKF,UAAU,CAAEuB,WAAW,EAAMC,KAAM,QACzEnF,EAAWoF,EAAAA,WAAWJ,EAAc/E,EAAS8D,EAAAA,EAAA,GAAKX,GAAmB6B,IAEjE,UACIjF,EAASqF,sBAAsBxB,KAAKF,MAAM2B,SAC/BnB,EAAA3C,EAAWxB,EAAUoB,SAC/BuD,GACCC,EAAAD,GAEG,MAAPC,OAAO,EAAAA,EAAAW,KAAKC,cAAcC,SAAS,cAC5BzF,EAAAiE,GAAG,SAAUJ,KAAKY,UAClBzE,EAAAiE,GAAG,SAAS,KAEdJ,KAAAgB,QAAQ7E,EAAWkC,EAAcd,MAE1C,CACF,CAEFyC,KAAKS,UAAUC,GAAeC,EAAAT,EAAAS,EAAAT,EAAA,GACzBQ,GADyB,CAE5BJ,mBAEGlC,EAAcjC,EAAWmE,EAAgBjC,EAAcd,IAJ9B,CAK5BpB,WACA4E,YAEFf,KAAKG,mBAAmBhE,EAAQ,IA3F1B,MAAAiF,QAAEA,GAAYtB,EAQpB,GANAE,KAAKO,MAAQ,CACXtE,MAAO,CAAC,EACRqE,eAAgB,CAAC,EACjBpE,WAAY,CAAA,GAGVkF,EAAS,CACL,MAAAS,UAAEA,GAAcT,EAClB,GAAAS,GAA2B,iBAAdA,EAA8B,CAC7C,MAAMrF,qBAAEA,GAAyBwD,KAAKC,kBACtCD,KAAKO,MAAQ,CACXtE,MAAOO,EAAuBQ,EAAc6E,GAAaA,EACzDvB,eAAgBuB,EAChB3F,WAAY,CAAA,EACd,CACF,CACF,CA4EI,iBAAA4F,GAAoB,OAAAZ,EAAAlB,KAAA,MAAA,YAClB,MAAA+B,oBAAEA,GAAwB/B,KAAKF,MACjCiC,IAAwBnF,EAAiBoD,KAAKF,eAI5CE,KAAKiB,kBAAgB,GAC7B,CAEM,kBAAAe,CAAmBC,GAA2B,OAAAf,EAAAlB,KAAA,MAAA,YAC5C,MAAA+B,oBAAEA,GAAwB/B,KAAKF,MAC/BoC,GAAqBtF,EAAiBqF,IAAcrF,EAAiBoD,KAAKF,OAC5EiC,GAAuBG,UACnBlC,KAAKiB,kBACb,GACF,CAEA,MAAAkB,GACE,MAAMlG,MAAEA,EAAOC,WAAAA,EAAAC,SAAYA,EAAU4E,MAAAA,GAAUf,KAAKO,OAE9C7D,aAAEA,GAAiBsD,KAAKC,kBAE9B,OACGmC,EAAAC,cAAA3F,EAAaL,SAAb,CAAsBiG,MAAO,CAAErG,QAAOC,aAAYC,WAAU4E,UAC1Df,KAAKF,MAAMyC,SACd,uqCCtHN,SAAkD1F,GAA6B,SAAAmD,OAAA,OAAA,YAhC/E,IAAAlD,EAAA0F,EAiCE,MAAMrB,aAAEA,EAAclF,MAAOsB,UAAa6D,EAAS/C,aAAcoE,GAAqB5F,EAChFwB,EAAe6B,OAAK3D,GAAwBkG,GAC5CrG,EAAU,OAAAU,IAAiBD,MAAW,CAAEwE,WAAW,EAAMC,KAAM,QACjE,IAAAP,EACA2B,EAAe,CAAC,EAEd,MAAAvG,EAAY,OAAAqG,QAAM3F,EAAOV,UAAbqG,EAA0BjB,EAAAA,WAAWJ,EAAc/E,EAAS8D,EAAKA,EAAA,CAAA,EAAAX,GAAmB6B,IAClG,UACIjF,EAASqF,sBAAsB3E,EAAO4E,SAC7BiB,EAAA/E,EAAWxB,EAAUoB,SAC7BuD,GACCC,EAAAD,CAAA,CAGV,MAAM6B,GAAwB,MAATvB,OAAS,EAAAA,EAAAS,YAAmC,iBAAtBT,EAAQS,UAA+BT,EAAQS,UAAYa,EA+D/F,MA7DY,EAAGH,eACpB,MAAOK,EAAQC,GAAaC,EAAAA,UAAwB,IAAOnC,EAAAT,EAAA,CACzDI,eAAgBqC,GACbvE,EAAcjC,EAAUwG,EAActE,EAAcd,IAFE,CAGzDpB,WACA4E,YAGFgC,EAAAA,WAAU,KACR,SAASC,EAAS1F,GACV,MAAA+C,EAAUhD,EAA+BC,EAASC,GACpDM,OAAOC,KAAKuC,GAASG,OAAS,GAChCqC,GAAWnC,IACH,MAAAuC,EAAwB/C,EAAKA,EAAA,CAAA,EAAAQ,EAAUJ,gBAAmBD,GAEhE,OAAOH,SACFQ,GADE,CAELJ,eAAgB2C,IACb7E,EAAcjC,EAAU8G,EAAuB5E,EAAcd,MAGtE,CAIF,SAASyD,IACD,MAAAV,EAAiB3C,EAAWxB,EAAUoB,GAClCsF,GAACnC,GAAeR,EAAAS,EAAAT,EAAA,CAAA,EACrBQ,GADqB,CAExBJ,mBACGlC,EAAcjC,EAAUmE,EAAgBjC,EAAcd,KACzD,CAGJ,SAASqD,EAASE,GAChB+B,GAAWnC,GAAeC,EAAAT,EAAA,CAAA,EAAKQ,GAAL,CAAgBK,MAAOD,KAAI,CAWvD,OAvBS3E,EAAAiE,GAAG,SAAU4C,IAkBX,MAAPjC,OAAO,EAAAA,EAAAW,KAAKC,cAAcC,SAAS,cAC5BzF,EAAAiE,GAAG,SAAUQ,GACbzE,EAAAiE,GAAG,QAASY,IAGhB,WACI7E,EAAA+G,IAAI,SAAUF,GACd7G,EAAA+G,IAAI,SAAUtC,GACdzE,EAAA+G,IAAI,QAASlC,EACxB,IACC,IAGoClE,MAAAA,EAAA8F,GAAftC,eAAA6C,GAAerG,EAATsG,+JAAAC,CAASvG,EAAT,CAAtB,oBAEFJ,aAAEA,GAAiB2B,EAEzB,uBAAQ3B,EAAaL,SAAb,CAAsBiG,MAAOc,GAAOb,GAGvC,2MACT,0GCjGmD7F,IACjD,MAAMT,MAAEA,GAAUqH,aAA4B,MAAA5G,EAAAA,EAAgBH,EAAoBG,cAE3E,OAAAT,uBCHYS,IACnB,MAAMP,SAAEA,GAAamH,aAAW,MAAA5G,EAAAA,EAAgBH,EAAoBG,cAE7D,OAAAP,4BCPT,SAAyCO,GACvC,MAAMqE,MAAEA,GAAUuC,aAAW,MAAA5G,EAAAA,EAAgBH,EAAoBG,cAE1D,OAAAqE,CACT,yBC8BA,SAAwBK,EAA2B,CAAEmC,YAAY,IACxD,OAAA,SAA8BC,GA7CvC,IAAA1G,EA8CI,MAAM2G,EAAe,OAAA3G,EAAAsE,EAAQ1E,cAARI,EAAwBP,EAAoBG,aAE1D,OAACoD,GACNsC,EAAAC,cAACoB,EAAanH,SAAb,MACE,EAAGL,QAAOE,cACLiF,EAAQmC,WACHnB,EAAAC,cAACmB,EAAiBtD,EAAA,CAAA/D,YAAwB2D,IAG3CsC,EAAAC,cAAAmB,EAAAtD,EAAA,CAAiBjE,QAAcE,YAAwB2D,KAIvE,CACF,yBClCO,SACLjD,GAEO,OAAA,SAA2B2G,GAC1B,MAAEnF,aAAcoE,GAAqB5F,EACrCwB,EAAe6B,OAAK3D,GAAwBkG,GAC5CiB,KAAgBxD,EAAA,CAAA,EAAKrD,SAAL,CAAawB,yBAEnC,SAASsF,EAAiB7D,GACxB,uBACGH,EAAeO,EAAA,CAAA,EAAAwD,GACbtB,EAAAC,cAAAmB,EAAAtD,EAAA,CAAA,EAAqBJ,IACxB,CAMG,OAFP8D,EAAqBD,EAAkBH,GAEhCG,CACT,CACF"}