# Launch Darkly React Wrapper

A modern, reusable React library for Launch Darkly feature flag interactions with TypeScript support, memoized hooks, and reactive real-time updates.

## Features

- 🚀 **Modern React Hooks**: Built with React hooks and context for optimal performance
- 🔄 **Real-time Updates**: Automatically responds to feature flag changes at runtime
- 💾 **Memoized Values**: Prevents unnecessary re-renders with intelligent memoization
- 🛡️ **Error Handling**: Comprehensive error handling with fallback values
- 📘 **TypeScript Support**: Full TypeScript support with proper type definitions
- 🔧 **Easy Migration**: Simple migration path from legacy implementations
- ⚡ **Performance Optimized**: Minimal bundle size with tree-shaking support

## Installation

```bash
npm install @your-org/launchdarkly-react-wrapper launchdarkly-react-client-sdk
```

## Quick Start

### 1. Wrap your app with LaunchDarklyProvider

```tsx
import React from 'react';
import { LaunchDarklyProvider } from '@your-org/launchdarkly-react-wrapper';

const App: React.FC = () => {
  const config = {
    clientSideId: 'your-client-side-id',
    context: {
      kind: 'user',
      key: 'user-123',
      name: '<PERSON>',
      email: '<EMAIL>'
    }
  };

  return (
    <LaunchDarklyProvider 
      config={config}
      fallback={<div>Loading feature flags...</div>}
    >
      <YourApp />
    </LaunchDarklyProvider>
  );
};
```

### 2. Use feature flags in your components

```tsx
import React from 'react';
import { useFeatureFlag, useBooleanFeatureFlag } from '@your-org/launchdarkly-react-wrapper';

const MyComponent: React.FC = () => {
  // Boolean flag
  const { value: isNewFeatureEnabled, loading, error } = useBooleanFeatureFlag(
    'new-feature-enabled', 
    false
  );

  // String flag with custom type
  const { value: theme } = useFeatureFlag<string>({
    flagKey: 'app-theme',
    defaultValue: 'light'
  });

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div className={`theme-${theme}`}>
      {isNewFeatureEnabled ? (
        <NewFeature />
      ) : (
        <LegacyFeature />
      )}
    </div>
  );
};
```

## API Reference

### LaunchDarklyProvider

The main provider component that wraps your application.

```tsx
interface LaunchDarklyConfig {
  clientSideId: string;
  context: LDContext;
  options?: {
    streaming?: boolean;
    bootstrap?: Record<string, LDFlagValue>;
    baseUri?: string;
    eventsUri?: string;
    streamUri?: string;
    debug?: boolean;
  };
}

<LaunchDarklyProvider 
  config={config}
  fallback={<LoadingComponent />}
>
  {children}
</LaunchDarklyProvider>
```

### useFeatureFlag Hook

The main hook for accessing feature flags with full configuration options.

```tsx
const { value, loading, error, isAvailable, refresh } = useFeatureFlag({
  flagKey: 'my-flag',
  defaultValue: 'default-value',
  context?: customContext // Optional context override
});
```

### Convenience Hooks

```tsx
// Boolean flags
const { value } = useBooleanFeatureFlag('feature-enabled', false);

// String flags  
const { value } = useStringFeatureFlag('app-theme', 'light');

// Number flags
const { value } = useNumberFeatureFlag('max-items', 10);
```

### useLaunchDarklyContext Hook

Access the Launch Darkly context directly for advanced use cases.

```tsx
const { 
  isInitialized, 
  loading, 
  error, 
  client, 
  context, 
  updateContext 
} = useLaunchDarklyContext();
```

## Migration Guide

### From Legacy Window-based Implementation

**Old Implementation:**
```tsx
// App.tsx - Old way with deprecated user property
<LDProvider clientSideID="your-id" user={user}>
  <App />
</LDProvider>

// Component - Old way reading from window
const flagValue = (window as any).IdClient?.variation('flag-key', false);
```

**New Implementation:**
```tsx
// App.tsx - New way with modern context
<LaunchDarklyProvider config={{
  clientSideId: 'your-id',
  context: {
    kind: 'user',
    key: user.id,
    name: user.name,
    email: user.email
  }
}}>
  <App />
</LaunchDarklyProvider>

// Component - New way with reactive hooks
const { value: flagValue } = useBooleanFeatureFlag('flag-key', false);
```

### Key Migration Benefits

1. **No more window object dependency** - Clean, testable code
2. **Automatic re-renders** - Components update when flags change
3. **Better error handling** - Graceful fallbacks and error states
4. **TypeScript support** - Full type safety
5. **Performance optimized** - Memoized values prevent unnecessary renders

## Advanced Usage

### Custom Context Updates

```tsx
const { updateContext } = useLaunchDarklyContext();

const handleUserChange = async (newUser) => {
  try {
    await updateContext({
      kind: 'user',
      key: newUser.id,
      name: newUser.name,
      email: newUser.email
    });
  } catch (error) {
    console.error('Failed to update context:', error);
  }
};
```

### Error Handling

```tsx
const { value, error, isAvailable } = useFeatureFlag({
  flagKey: 'my-flag',
  defaultValue: 'fallback'
});

if (!isAvailable) {
  // Launch Darkly is not available, using fallback
}

if (error) {
  // Handle specific error types
  switch (error.type) {
    case LaunchDarklyErrorType.CLIENT_UNAVAILABLE:
      // Handle client unavailable
      break;
    case LaunchDarklyErrorType.FLAG_EVALUATION_ERROR:
      // Handle flag evaluation error
      break;
  }
}
```

### Manual Flag Refresh

```tsx
const { value, refresh } = useFeatureFlag({
  flagKey: 'my-flag',
  defaultValue: false
});

const handleRefresh = async () => {
  try {
    await refresh();
  } catch (error) {
    console.error('Failed to refresh flag:', error);
  }
};
```

## Best Practices

1. **Use TypeScript**: Leverage the full type safety provided by the library
2. **Provide meaningful defaults**: Always provide sensible default values
3. **Handle loading states**: Show appropriate loading indicators
4. **Handle errors gracefully**: Implement proper error boundaries
5. **Use convenience hooks**: Use `useBooleanFeatureFlag` for boolean flags
6. **Avoid context overrides**: Only use context overrides when necessary
7. **Test with fallbacks**: Ensure your app works when Launch Darkly is unavailable

## Troubleshooting

### Common Issues

**Issue**: "useLaunchDarklyContext must be used within a LaunchDarklyProvider"
**Solution**: Ensure your component is wrapped with `LaunchDarklyProvider`

**Issue**: Flags not updating in real-time
**Solution**: Ensure streaming is enabled in your configuration (default: true)

**Issue**: TypeScript errors with flag values
**Solution**: Use the generic type parameter: `useFeatureFlag<string>(...)`

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details
